@echo off
title 班级成绩管理系统V2.0
echo 正在启动班级成绩管理系统V2.0...

REM 检查Node.js是否安装
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未找到Node.js。请确保Node.js已安装并添加到PATH环境变量中。
    echo 您可以从 https://nodejs.org/ 下载Node.js。
    pause
    exit /b 1
)

REM 获取当前目录
set CURRENT_DIR=%~dp0

REM 显示提示信息
echo.
echo 正在安装依赖...
cd /d %CURRENT_DIR%
call npm install

echo.
echo 检查数据库配置...
if not exist .env (
    echo 警告: 未找到.env文件，将使用默认配置。
    echo 如需自定义配置，请创建.env文件。
) else (
    echo .env文件已存在，将使用已配置的数据库设置。
)

echo.
echo 初始化数据库...
call npm run init-db

echo.
echo 启动服务器...
echo 服务器将在 http://localhost:3001 上运行
echo 请保持此窗口打开，并使用浏览器访问以下地址：
echo.
echo 教师端: http://localhost:3001/index.html
echo 学生端: http://localhost:3001/student.html
echo.

REM 启动服务器
call npm start

echo 服务器已停止。
pause