// 测试批量删除API的简单脚本
const fetch = require('node-fetch');

async function testBatchDelete() {
    try {
        // 首先登录获取token
        const loginResponse = await fetch('http://localhost:3005/api/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                username: 'admin',
                password: 'admin123'
            })
        });

        if (!loginResponse.ok) {
            console.error('登录失败:', loginResponse.status);
            return;
        }

        const loginData = await loginResponse.json();
        const token = loginData.token;
        console.log('登录成功，获取到token');

        // 测试批量删除API
        const deleteResponse = await fetch('http://localhost:3005/api/teacher/students/batch-delete', {
            method: 'POST',
            headers: { 
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                studentIds: ['1', '2'] // 测试ID
            })
        });

        console.log('批量删除API响应状态:', deleteResponse.status);
        const deleteData = await deleteResponse.json();
        console.log('批量删除API响应数据:', deleteData);

    } catch (error) {
        console.error('测试失败:', error);
    }
}

testBatchDelete();
