/**
 * 认证控制器
 * 处理用户登录认证
 */

const db = require('../config/db');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

/**
 * 用户登录
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.login = async (req, res) => {
  try {
    const { username, password } = req.body;
    
    // 验证必填字段
    if (!username || !password) {
      return res.status(400).json({ error: '用户名和密码为必填项' });
    }
    
    // 查询用户（使用Supabase）
    const { data: users, error: queryError } = await db.supabase
      .from('users')
      .select('*')
      .eq('username', username);

    if (queryError) {
      throw queryError;
    }
    
    // 用户不存在
    if (users.length === 0) {
      return res.status(401).json({ error: '用户名或密码错误' });
    }

    // 验证密码（支持bcrypt加密和明文密码）
    const user = users[0];
    let passwordValid = false;

    // 检查是否是bcrypt加密的密码
    if (user.password.startsWith('$2a$') || user.password.startsWith('$2b$')) {
      passwordValid = await bcrypt.compare(password, user.password);
    } else {
      // 兼容旧的明文密码
      passwordValid = user.password === password;
    }

    if (!passwordValid) {
      return res.status(401).json({ error: '用户名或密码错误' });
    }
    
    // 获取用户信息(不包含密码)
    const userInfo = {
      id: user.id,
      username: user.username,
      role: user.role,
      display_name: user.display_name
    };
    
    // 生成JWT令牌
    const jwtSecret = process.env.JWT_SECRET;

    if (!jwtSecret) {
      console.error('JWT_SECRET环境变量未设置，无法生成令牌');
      return res.status(500).json({ error: '服务器配置错误' });
    }

    const token = jwt.sign(
      { id: userInfo.id, username: userInfo.username, role: userInfo.role },
      jwtSecret,
      { expiresIn: '24h' }
    );

    // 记录登录日志（使用Supabase）
    await db.supabase
      .from('logs')
      .insert({
        action: 'login',
        details: `用户 ${username} 登录成功`,
        created_at: new Date().toISOString()
      });

    res.status(200).json({
      data: {
        user: userInfo,
        token
      }
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({ error: '登录失败: ' + error.message });
  }
};

/**
 * 教师注册
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.register = async (req, res) => {
  try {
    // {{ AURA-X: Modify - 安全日志记录，避免输出敏感信息. Approval: 寸止 }}
    console.log('收到注册请求，用户名:', req.body.username);
    const { username, password, confirmPassword } = req.body;

    // 验证必填字段
    if (!username || !password || !confirmPassword) {
      return res.status(400).json({
        error: '用户名、密码和确认密码为必填项'
      });
    }

    // 验证用户名格式（3-20个字符，只允许字母、数字、下划线）
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernameRegex.test(username)) {
      return res.status(400).json({
        error: '用户名必须是3-20个字符，只能包含字母、数字和下划线'
      });
    }

    // 验证密码强度（至少8位，包含字母和数字）
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
    if (!passwordRegex.test(password)) {
      return res.status(400).json({
        error: '密码必须至少8位，包含字母和数字'
      });
    }

    // 验证密码确认
    if (password !== confirmPassword) {
      return res.status(400).json({ error: '两次输入的密码不一致' });
    }

    // 检查用户名是否已存在（使用Supabase）
    const { data: existingUsers, error: checkError } = await db.supabase
      .from('users')
      .select('id')
      .eq('username', username);

    if (checkError) {
      throw checkError;
    }

    if (existingUsers && existingUsers.length > 0) {
      return res.status(409).json({ error: '用户名已存在，请选择其他用户名' });
    }

    // 加密密码
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 插入新用户（使用Supabase，不包含email字段）
    const { data: newUser, error: insertError } = await db.supabase
      .from('users')
      .insert({
        username,
        password: hashedPassword,
        display_name: username, // 使用用户名作为显示名称
        role: 'teacher',
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (insertError) {
      throw insertError;
    }

    // 记录注册日志（使用Supabase）
    await db.supabase
      .from('logs')
      .insert({
        action: 'register',
        details: `新教师 ${username} 注册成功`,
        created_at: new Date().toISOString()
      });

    // 返回成功响应（不包含密码和email）
    console.log('注册成功，返回用户信息:', newUser);
    res.status(201).json({
      message: '注册成功！请使用新账户登录',
      data: {
        id: newUser.id,
        username: newUser.username,
        display_name: newUser.display_name,
        role: newUser.role
      }
    });

  } catch (error) {
    console.error('=== 后端注册错误详情 ===');
    console.error('错误对象:', error);
    console.error('错误类型:', typeof error);
    console.error('错误消息:', error?.message);
    console.error('错误堆栈:', error?.stack);

    // 处理不同类型的错误
    let errorMessage = '注册失败，请稍后重试';
    let statusCode = 500;

    if (error && error.message) {
      errorMessage = error.message;

      // 根据错误类型设置适当的状态码
      if (error.message.includes('duplicate') || error.message.includes('already exists')) {
        statusCode = 409; // Conflict
      } else if (error.message.includes('validation') || error.message.includes('invalid')) {
        statusCode = 400; // Bad Request
      }
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else if (error && error.error) {
      errorMessage = error.error;
    }

    // 确保错误消息是字符串
    if (typeof errorMessage !== 'string') {
      errorMessage = '注册失败，服务器内部错误';
    }

    console.error('返回的错误消息:', errorMessage);
    console.error('状态码:', statusCode);
    console.error('=== 后端错误处理结束 ===');

    // 确保返回标准格式的错误响应
    res.status(statusCode).json({
      error: errorMessage,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 生成JWT token
 * @param {object} user 用户信息
 * @returns {string} JWT token
 */
const generateToken = (user) => {
  return jwt.sign(
    { id: user.id, username: user.username, role: user.role },
    process.env.JWT_SECRET || 'fallback-secret-key-change-in-production',
    { expiresIn: '24h' }
  );
};

/**
 * 验证JWT token中间件
 */
const verifyToken = async (req, res, next) => {
  try {
    // 获取请求头中的token
    const token = req.headers.authorization?.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({ message: '未提供认证令牌' });
    }
    
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key-change-in-production');
    req.user = decoded;
    
    next();
  } catch (error) {
    return res.status(401).json({ message: '无效的认证令牌' });
  }
};

/**
 * 检查管理员权限中间件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - 下一个中间件函数
 */
exports.isAdmin = (req, res, next) => {
  if (!req.user || req.user.role !== 'admin') {
    return res.status(403).json({ error: '需要管理员权限' });
  }
  next();
};

// 新添加的方法已移除，待登录问题解决后重新添加