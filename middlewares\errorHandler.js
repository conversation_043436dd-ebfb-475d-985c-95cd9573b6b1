/**
 * 错误处理中间件
 * 用于集中处理应用程序中的错误
 */

/**
 * 404错误处理中间件
 * 处理未匹配的路由请求
 */
exports.notFoundHandler = (req, res, next) => {
  res.status(404).json({
    error: '请求的资源不存在',
    path: req.originalUrl
  });
};

/**
 * 通用错误处理中间件
 * 处理应用程序中的所有未捕获错误
 */
exports.errorHandler = (err, req, res, next) => {
  logError(err);

  // 判断错误类型，设置适当的状态码
  const statusCode = err.statusCode || err.status || 500;

  // 确保错误消息是字符串
  let errorMessage = '服务器内部错误';

  if (err.message && typeof err.message === 'string') {
    errorMessage = err.message;
  } else if (typeof err === 'string') {
    errorMessage = err;
  } else if (err.error && typeof err.error === 'string') {
    errorMessage = err.error;
  }

  // 构建标准错误响应格式
  const errorResponse = {
    error: errorMessage,
    timestamp: new Date().toISOString()
  };

  // 在开发环境中添加更多详细信息
  if (process.env.NODE_ENV !== 'production') {
    errorResponse.details = {
      stack: err.stack,
      code: err.code,
      originalError: err
    };
  }

  console.error('错误处理中间件 - 返回响应:', errorResponse);

  // 发送错误响应
  res.status(statusCode).json(errorResponse);
};

/**
 * 验证错误处理器
 * 专门处理请求验证错误
 */
exports.validationErrorHandler = (err, req, res, next) => {
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      error: '请求验证失败',
      details: err.errors
    });
  }
  
  // 如果不是验证错误，传递给下一个错误处理中间件
  next(err);
};

/**
 * SQL错误处理器
 * 专门处理数据库错误
 */
exports.sqlErrorHandler = (err, req, res, next) => {
  // 检查是否为MySQL错误
  if (err.code && (err.code.startsWith('ER_') || err.sqlState)) {
    console.error('数据库错误:', err);
    
    // 确定错误类型并设置适当的消息
    let errorMessage = '数据库操作失败';
    let statusCode = 500;
    
    // 处理常见的MySQL错误
    switch (err.code) {
      case 'ER_DUP_ENTRY':
        errorMessage = '数据重复，记录已存在';
        statusCode = 409; // Conflict
        break;
      case 'ER_NO_REFERENCED_ROW':
      case 'ER_NO_REFERENCED_ROW_2':
        errorMessage = '引用的记录不存在';
        statusCode = 422; // Unprocessable Entity
        break;
      case 'ER_ACCESS_DENIED_ERROR':
        errorMessage = '数据库访问被拒绝';
        statusCode = 403; // Forbidden
        break;
      case 'ER_BAD_FIELD_ERROR':
        errorMessage = '数据库字段错误';
        statusCode = 400; // Bad Request
        break;
      default:
        errorMessage = '数据库操作失败';
    }
    
    return res.status(statusCode).json({
      error: errorMessage,
      status: statusCode,
      code: process.env.NODE_ENV !== 'production' ? err.code : undefined
    });
  }
  
  // 如果不是SQL错误，传递给下一个错误处理中间件
  next(err);
}; 