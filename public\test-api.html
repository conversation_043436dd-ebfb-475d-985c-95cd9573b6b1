<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🧪 教师管理API测试</h1>
    
    <div class="container">
        <h2>🔐 认证测试</h2>
        <div>
            <input type="text" id="username" placeholder="用户名" value="teacher">
            <input type="password" id="password" placeholder="密码" value="password">
            <button class="btn" onclick="testLogin()">登录</button>
            <button class="btn btn-danger" onclick="clearToken()">清除Token</button>
        </div>
        <div id="auth-result" class="result"></div>
    </div>

    <div class="container">
        <h2>📊 API端点测试</h2>
        <div>
            <button class="btn btn-success" onclick="testAPI('/api/auth/validate', 'GET')">验证Token</button>
            <button class="btn btn-success" onclick="testAPI('/api/teacher/schools', 'GET')">获取学校</button>
            <button class="btn btn-success" onclick="testAPI('/api/teacher/classes', 'GET')">获取班级</button>
            <button class="btn btn-success" onclick="testAPI('/api/teacher/students', 'GET')">获取学生</button>
        </div>
        <div id="api-result" class="result"></div>
    </div>

    <div class="container">
        <h2>➕ 添加数据测试</h2>
        <div>
            <input type="text" id="school-name" placeholder="学校名称" value="测试学校">
            <button class="btn" onclick="testAddSchool()">添加学校</button>
        </div>
        <div id="add-result" class="result"></div>
    </div>

    <script>
        let currentToken = localStorage.getItem('token');

        function updateTokenDisplay() {
            const authResult = document.getElementById('auth-result');
            if (currentToken) {
                authResult.className = 'result success';
                authResult.textContent = `Token: ${currentToken.substring(0, 50)}...`;
            } else {
                authResult.className = 'result info';
                authResult.textContent = '未登录';
            }
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();
                
                if (response.ok && result.data && result.data.token) {
                    currentToken = result.data.token;
                    localStorage.setItem('token', currentToken);
                    localStorage.setItem('user', JSON.stringify(result.data.user));
                    
                    const authResult = document.getElementById('auth-result');
                    authResult.className = 'result success';
                    authResult.textContent = `登录成功！\n用户: ${result.data.user.username}\n角色: ${result.data.user.role}\nToken: ${currentToken.substring(0, 50)}...`;
                } else {
                    throw new Error(result.error || '登录失败');
                }
            } catch (error) {
                const authResult = document.getElementById('auth-result');
                authResult.className = 'result error';
                authResult.textContent = `登录失败: ${error.message}`;
            }
        }

        function clearToken() {
            currentToken = null;
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            updateTokenDisplay();
        }

        async function testAPI(endpoint, method = 'GET', body = null) {
            const resultDiv = document.getElementById('api-result');
            
            if (!currentToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '请先登录获取Token';
                return;
            }

            try {
                const options = {
                    method,
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    }
                };

                if (body) {
                    options.body = JSON.stringify(body);
                }

                const response = await fetch(endpoint, options);
                const result = await response.json();

                resultDiv.className = response.ok ? 'result success' : 'result error';
                resultDiv.textContent = `${method} ${endpoint}\n状态: ${response.status} ${response.statusText}\n响应: ${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求失败: ${error.message}`;
            }
        }

        async function testAddSchool() {
            const schoolName = document.getElementById('school-name').value;
            const resultDiv = document.getElementById('add-result');
            
            if (!currentToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '请先登录获取Token';
                return;
            }

            if (!schoolName.trim()) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '请输入学校名称';
                return;
            }

            try {
                const response = await fetch('/api/teacher/schools', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: schoolName.trim(),
                        address: '测试地址',
                        contact_phone: '123456789'
                    })
                });

                const result = await response.json();

                resultDiv.className = response.ok ? 'result success' : 'result error';
                resultDiv.textContent = `添加学校\n状态: ${response.status} ${response.statusText}\n响应: ${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `添加学校失败: ${error.message}`;
            }
        }

        // 页面加载时更新Token显示
        window.onload = function() {
            updateTokenDisplay();
        };
    </script>
</body>
</html>
