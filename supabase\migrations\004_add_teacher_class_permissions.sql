-- 添加教师班级权限管理
-- 这个迁移用于实现教师只能查看自己任教班级的功能

-- 创建教师班级关联表
CREATE TABLE IF NOT EXISTS teacher_classes (
  id SERIAL PRIMARY KEY,
  teacher_id INTEGER NOT NULL,
  grade INTEGER NOT NULL,
  class INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 外键约束
  CONSTRAINT fk_teacher_classes_teacher FOREIGN KEY (teacher_id) 
    REFERENCES users(id) ON DELETE CASCADE,
  
  -- 唯一约束：一个教师不能重复分配到同一个班级
  CONSTRAINT unique_teacher_grade_class UNIQUE (teacher_id, grade, class)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_teacher_classes_teacher ON teacher_classes(teacher_id);
CREATE INDEX IF NOT EXISTS idx_teacher_classes_grade_class ON teacher_classes(grade, class);

-- 创建更新时间触发器
CREATE TRIGGER update_teacher_classes_updated_at 
  BEFORE UPDATE ON teacher_classes 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入一些示例数据（可选）
-- 假设teacher1负责1年级1班和1年级2班
-- INSERT INTO teacher_classes (teacher_id, grade, class) 
-- SELECT u.id, 1, 1 FROM users u WHERE u.username = 'teacher1'
-- UNION ALL
-- SELECT u.id, 1, 2 FROM users u WHERE u.username = 'teacher1';

-- 创建视图：教师可见的学生
CREATE OR REPLACE VIEW teacher_visible_students AS
SELECT DISTINCT
  s.*,
  tc.teacher_id
FROM students s
JOIN teacher_classes tc ON s.grade = tc.grade AND s.class = tc.class;

-- 创建函数：检查教师是否有权限查看某个班级
CREATE OR REPLACE FUNCTION check_teacher_class_permission(
  p_teacher_id INTEGER,
  p_grade INTEGER,
  p_class INTEGER
) RETURNS BOOLEAN AS $$
BEGIN
  -- 管理员可以查看所有班级
  IF EXISTS (
    SELECT 1 FROM users 
    WHERE id = p_teacher_id AND role = 'admin'
  ) THEN
    RETURN TRUE;
  END IF;
  
  -- 检查教师是否有权限查看指定班级
  RETURN EXISTS (
    SELECT 1 FROM teacher_classes 
    WHERE teacher_id = p_teacher_id 
      AND grade = p_grade 
      AND class = p_class
  );
END;
$$ LANGUAGE plpgsql;

-- 创建函数：获取教师可见的学生列表
CREATE OR REPLACE FUNCTION get_teacher_students(p_teacher_id INTEGER)
RETURNS TABLE (
  id INTEGER,
  student_identifier VARCHAR(255),
  name VARCHAR(100),
  grade INTEGER,
  class INTEGER,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  -- 管理员可以查看所有学生
  IF EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = p_teacher_id AND role = 'admin'
  ) THEN
    RETURN QUERY
    SELECT s.id, s.student_identifier, s.name, s.grade, s.class, s.created_at, s.updated_at
    FROM students s
    ORDER BY s.grade, s.class, s.name;
  ELSE
    -- 普通教师只能查看自己任教的班级
    RETURN QUERY
    SELECT s.id, s.student_identifier, s.name, s.grade, s.class, s.created_at, s.updated_at
    FROM students s
    JOIN teacher_classes tc ON s.grade = tc.grade AND s.class = tc.class
    WHERE tc.teacher_id = p_teacher_id
    ORDER BY s.grade, s.class, s.name;
  END IF;
END;
$$ LANGUAGE plpgsql;
