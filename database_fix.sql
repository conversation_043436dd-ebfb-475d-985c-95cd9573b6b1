-- 班级成绩管理系统V2.0 - 数据库修复脚本
-- 解决学校添加功能失败和数据库结构冗余问题
-- 请在Supabase SQL Editor中执行此脚本

-- ==================== 第一部分：清理冗余表结构 ====================

-- 1. 删除冗余的teacher_schools表（如果存在）
DROP TABLE IF EXISTS teacher_schools CASCADE;

-- 2. 确保teacher_school_assignments表存在且结构正确
CREATE TABLE IF NOT EXISTS teacher_school_assignments (
  id SERIAL PRIMARY KEY,
  teacher_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  school_id INTEGER NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(teacher_id, school_id)
);

-- 3. 确保teacher_class_permissions表存在
CREATE TABLE IF NOT EXISTS teacher_class_permissions (
  id SERIAL PRIMARY KEY,
  teacher_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  school_id INTEGER NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
  grade INTEGER NOT NULL,
  class INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(teacher_id, school_id, grade, class)
);

-- 4. 确保school_grade_configs表存在
CREATE TABLE IF NOT EXISTS school_grade_configs (
  id SERIAL PRIMARY KEY,
  school_id INTEGER NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
  grade INTEGER NOT NULL,
  class_count INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(school_id, grade)
);

-- ==================== 第二部分：配置RLS策略 ====================

-- 注意：由于我们使用自定义的users表而不是Supabase的auth系统，
-- 我们需要禁用RLS或者使用service role key来绕过RLS限制

-- 1. 禁用schools表的RLS（因为我们在应用层处理权限）
ALTER TABLE schools DISABLE ROW LEVEL SECURITY;

-- 2. 禁用其他表的RLS
ALTER TABLE teacher_school_assignments DISABLE ROW LEVEL SECURITY;
ALTER TABLE students DISABLE ROW LEVEL SECURITY;

-- 如果将来需要启用RLS，需要先建立Supabase auth与自定义users表的映射关系

-- RLS策略已禁用，权限控制在应用层处理

-- ==================== 第三部分：创建辅助函数 ====================

-- 1. 创建获取用户ID的函数（从JWT token）
-- 注意：Supabase的auth.uid()返回UUID，需要通过users表查找对应的integer ID
CREATE OR REPLACE FUNCTION get_current_user_id()
RETURNS INTEGER AS $$
DECLARE
  user_uuid UUID;
  user_int_id INTEGER;
BEGIN
  user_uuid := auth.uid();

  IF user_uuid IS NULL THEN
    RETURN NULL;
  END IF;

  -- 通过JWT中的用户信息查找对应的integer ID
  -- 这里假设JWT中包含username信息，或者需要其他方式关联
  -- 由于Supabase的auth系统与我们的users表是分离的，我们需要另一种方法

  -- 暂时返回NULL，需要在应用层处理用户ID映射
  RETURN NULL;
EXCEPTION
  WHEN OTHERS THEN
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. 创建检查用户是否为管理员的函数（简化版本）
CREATE OR REPLACE FUNCTION is_admin(user_id INTEGER)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users
    WHERE id = user_id
    AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. 创建自动分配学校权限的函数（由应用层调用）
CREATE OR REPLACE FUNCTION assign_school_to_teacher(teacher_id INTEGER, school_id INTEGER)
RETURNS BOOLEAN AS $$
BEGIN
  INSERT INTO teacher_school_assignments (teacher_id, school_id)
  VALUES (teacher_id, school_id)
  ON CONFLICT (teacher_id, school_id) DO NOTHING;

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 注意：由于权限控制在应用层，不需要数据库触发器

-- ==================== 第四部分：创建索引优化 ====================

-- 创建必要的索引
CREATE INDEX IF NOT EXISTS idx_teacher_school_assignments_teacher ON teacher_school_assignments(teacher_id);
CREATE INDEX IF NOT EXISTS idx_teacher_school_assignments_school ON teacher_school_assignments(school_id);
CREATE INDEX IF NOT EXISTS idx_teacher_class_permissions_teacher ON teacher_class_permissions(teacher_id);
CREATE INDEX IF NOT EXISTS idx_teacher_class_permissions_school ON teacher_class_permissions(school_id);
CREATE INDEX IF NOT EXISTS idx_school_grade_configs_school ON school_grade_configs(school_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

-- ==================== 第五部分：数据完整性检查 ====================

-- 确保默认用户存在
INSERT INTO users (username, password, display_name, role) 
VALUES 
  ('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 'admin'),
  ('teacher', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '教师用户', 'teacher')
ON CONFLICT (username) DO NOTHING;

-- 确保默认学校存在
INSERT INTO schools (name, address, contact_phone) 
VALUES 
  ('示例小学', '北京市朝阳区示例路123号', '010-12345678'),
  ('测试中学', '上海市浦东新区测试大道456号', '021-87654321')
ON CONFLICT (name) DO NOTHING;

-- 为默认教师分配学校权限
DO $$
DECLARE
    teacher_user_id INTEGER;
    school1_id INTEGER;
BEGIN
    SELECT id INTO teacher_user_id FROM users WHERE username = 'teacher' AND role = 'teacher';
    SELECT id INTO school1_id FROM schools WHERE name = '示例小学';
    
    IF teacher_user_id IS NOT NULL AND school1_id IS NOT NULL THEN
        INSERT INTO teacher_school_assignments (teacher_id, school_id) 
        VALUES (teacher_user_id, school1_id)
        ON CONFLICT (teacher_id, school_id) DO NOTHING;
    END IF;
END $$;

-- 完成提示
SELECT '数据库修复脚本执行完成！学校添加功能应该已经修复。' as message;
