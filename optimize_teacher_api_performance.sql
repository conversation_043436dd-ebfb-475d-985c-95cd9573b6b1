-- 教师端API性能优化脚本
-- 解决API请求超时问题，优化数据库查询性能

-- 1. 为教师权限相关表添加索引
CREATE INDEX IF NOT EXISTS idx_teacher_class_permissions_teacher_id 
ON teacher_class_permissions(teacher_id);

CREATE INDEX IF NOT EXISTS idx_teacher_class_permissions_school_grade_class 
ON teacher_class_permissions(school_id, grade, class);

CREATE INDEX IF NOT EXISTS idx_teacher_school_assignments_teacher_id 
ON teacher_school_assignments(teacher_id);

CREATE INDEX IF NOT EXISTS idx_teacher_school_assignments_school_id 
ON teacher_school_assignments(school_id);

-- 2. 为学生表添加复合索引
CREATE INDEX IF NOT EXISTS idx_students_school_grade_class 
ON students(school_id, grade, class);

CREATE INDEX IF NOT EXISTS idx_students_created_at 
ON students(created_at DESC);

-- 3. 为学校表添加索引
CREATE INDEX IF NOT EXISTS idx_schools_name 
ON schools(name);

-- 4. 创建教师权限视图，优化查询性能
CREATE OR REPLACE VIEW teacher_permissions_optimized AS
SELECT 
    tcp.teacher_id,
    tcp.school_id,
    s.name as school_name,
    tcp.grade,
    tcp.class,
    tcp.created_at
FROM teacher_class_permissions tcp
LEFT JOIN schools s ON tcp.school_id = s.id;

-- 5. 创建教师可见学生视图，优化学生查询
CREATE OR REPLACE VIEW teacher_visible_students_optimized AS
SELECT DISTINCT
    st.*,
    sch.name as school_name,
    tcp.teacher_id
FROM students st
INNER JOIN teacher_class_permissions tcp ON (
    st.school_id = tcp.school_id AND 
    st.grade = tcp.grade AND 
    st.class = tcp.class
)
LEFT JOIN schools sch ON st.school_id = sch.id;

-- 6. 分析表统计信息，优化查询计划
ANALYZE teacher_class_permissions;
ANALYZE teacher_school_assignments;
ANALYZE students;
ANALYZE schools;

-- 7. 检查索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('teacher_class_permissions', 'teacher_school_assignments', 'students', 'schools')
ORDER BY tablename, indexname;

-- 8. 检查表大小和性能
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_rows,
    n_dead_tup as dead_rows,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables 
WHERE schemaname = 'public' 
AND tablename IN ('teacher_class_permissions', 'teacher_school_assignments', 'students', 'schools')
ORDER BY tablename;

-- 9. 优化建议查询
SELECT 
    'teacher_class_permissions' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT teacher_id) as unique_teachers,
    COUNT(DISTINCT school_id) as unique_schools
FROM teacher_class_permissions
UNION ALL
SELECT 
    'teacher_school_assignments' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT teacher_id) as unique_teachers,
    COUNT(DISTINCT school_id) as unique_schools
FROM teacher_school_assignments
UNION ALL
SELECT 
    'students' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT school_id) as unique_schools,
    COUNT(DISTINCT CONCAT(grade, '-', class)) as unique_classes
FROM students;

-- 10. 显示优化完成信息
SELECT 
    '数据库性能优化完成' as status,
    '已添加必要索引和视图' as description,
    '建议重启应用以获得最佳性能' as recommendation;
