-- 检查当前数据库状态和权限设置

-- 1. 查看所有用户
SELECT id, username, role, display_name FROM users ORDER BY id;

-- 2. 查看所有学校
SELECT id, name FROM schools ORDER BY id;

-- 3. 查看学生数据概览
SELECT 
  school_id,
  grade,
  class,
  COUNT(*) as student_count
FROM students 
GROUP BY school_id, grade, class 
ORDER BY school_id, grade, class;

-- 4. 查看teacher用户的权限分配
SELECT 
  tcp.teacher_id,
  u.username,
  tcp.school_id,
  s.name as school_name,
  tcp.grade,
  tcp.class
FROM teacher_class_permissions tcp
JOIN users u ON tcp.teacher_id = u.id
LEFT JOIN schools s ON tcp.school_id = s.id
WHERE u.username = 'teacher';

-- 5. 查看medals表状态
SELECT COUNT(*) as medal_records FROM medals;

-- 6. 查看学生和奖章的完整关联（前10条）
SELECT 
  st.student_identifier,
  st.name,
  st.grade,
  st.class,
  st.school_id,
  sc.name as school_name,
  COALESCE(m.count, 0) as medal_count
FROM students st
LEFT JOIN schools sc ON st.school_id = sc.id
LEFT JOIN medals m ON st.student_identifier = m.student_identifier
ORDER BY st.school_id, st.grade, st.class, st.name
LIMIT 10;

-- 7. 如果teacher用户没有权限，可以运行以下语句为其分配权限
-- 首先获取teacher用户的ID和现有学生的学校/年级/班级组合

-- 查看teacher用户ID
SELECT id FROM users WHERE username = 'teacher';

-- 查看现有的学校/年级/班级组合
SELECT DISTINCT school_id, grade, class 
FROM students 
ORDER BY school_id, grade, class;

-- 示例：为teacher用户分配所有现有班级的权限
-- 请根据实际情况修改teacher_id（假设是2）
/*
INSERT INTO teacher_class_permissions (teacher_id, school_id, grade, class)
SELECT 2, school_id, grade, class
FROM (
  SELECT DISTINCT school_id, grade, class 
  FROM students
) AS class_combinations
ON CONFLICT (teacher_id, school_id, grade, class) DO NOTHING;
*/
