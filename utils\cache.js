/**
 * 简单的内存缓存工具
 * 用于缓存频繁查询的数据，提高响应速度
 */

class SimpleCache {
  constructor() {
    this.cache = new Map();
    this.ttl = new Map(); // Time To Live
  }

  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {any} value 缓存值
   * @param {number} ttlMs 过期时间（毫秒），默认5分钟
   */
  set(key, value, ttlMs = 5 * 60 * 1000) {
    this.cache.set(key, value);
    this.ttl.set(key, Date.now() + ttlMs);
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @returns {any|null} 缓存值或null
   */
  get(key) {
    const expireTime = this.ttl.get(key);
    
    // 检查是否过期
    if (!expireTime || Date.now() > expireTime) {
      this.delete(key);
      return null;
    }

    return this.cache.get(key);
  }

  /**
   * 删除缓存
   * @param {string} key 缓存键
   */
  delete(key) {
    this.cache.delete(key);
    this.ttl.delete(key);
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear();
    this.ttl.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const now = Date.now();
    let validCount = 0;
    let expiredCount = 0;

    for (const [key, expireTime] of this.ttl) {
      if (now > expireTime) {
        expiredCount++;
      } else {
        validCount++;
      }
    }

    return {
      total: this.cache.size,
      valid: validCount,
      expired: expiredCount
    };
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now();
    const expiredKeys = [];

    for (const [key, expireTime] of this.ttl) {
      if (now > expireTime) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.delete(key));
    return expiredKeys.length;
  }
}

// 创建全局缓存实例
const cache = new SimpleCache();

// 定期清理过期缓存（每10分钟）
setInterval(() => {
  const cleaned = cache.cleanup();
  if (cleaned > 0) {
    console.log(`清理了 ${cleaned} 个过期缓存项`);
  }
}, 10 * 60 * 1000);

/**
 * 缓存键生成器
 */
const CacheKeys = {
  /**
   * 生成学生数据缓存键
   */
  studentData: (teacherId, filters = {}) => {
    const filterStr = Object.keys(filters)
      .sort()
      .map(key => `${key}:${filters[key]}`)
      .join('|');
    return `students:${teacherId}:${filterStr}`;
  },

  /**
   * 生成打字记录缓存键
   */
  typingRecords: (studentIdentifiers) => {
    const sortedIds = [...studentIdentifiers].sort();
    return `typing:batch:${sortedIds.join(',')}`;
  },

  /**
   * 生成教师权限缓存键
   */
  teacherPermissions: (teacherId) => {
    return `permissions:${teacherId}`;
  },

  /**
   * 生成最佳打字记录缓存键
   */
  bestTypingRecords: (filters = {}) => {
    const filterStr = Object.keys(filters)
      .sort()
      .map(key => `${key}:${filters[key]}`)
      .join('|');
    return `best_typing:${filterStr}`;
  }
};

/**
 * 批量缓存助手
 */
const BatchCacheHelper = {
  /**
   * 批量获取，支持缓存
   */
  async batchGetWithCache(keys, fetchFunction, cachePrefix, ttl = 5 * 60 * 1000) {
    const results = {};
    const missingKeys = [];

    // 检查缓存
    for (const key of keys) {
      const cacheKey = `${cachePrefix}:${key}`;
      const cached = cache.get(cacheKey);

      if (cached !== null) {
        results[key] = cached;
      } else {
        missingKeys.push(key);
      }
    }

    // 获取缺失的数据
    if (missingKeys.length > 0) {
      console.log(`缓存未命中${missingKeys.length}项，从数据库获取`);
      const fetchedData = await fetchFunction(missingKeys);

      // 缓存新数据
      for (const [key, value] of Object.entries(fetchedData)) {
        const cacheKey = `${cachePrefix}:${key}`;
        cache.set(cacheKey, value, ttl);
        results[key] = value;
      }
    }

    return results;
  }
};

module.exports = {
  cache,
  CacheKeys,
  BatchCacheHelper,
  SimpleCache
};
