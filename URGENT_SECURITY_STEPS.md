# 🚨 紧急安全操作步骤

## 立即执行（5分钟内完成）

### 步骤1：登录Supabase控制台
1. 打开 [Supabase控制台](https://supabase.com/dashboard)
2. 选择您的项目
3. 点击左侧菜单的 "SQL Editor"

### 步骤2：执行安全脚本
1. 复制 `supabase_security_setup.sql` 文件内容
2. 粘贴到SQL编辑器中
3. 点击 "Run" 执行脚本

### 步骤3：验证安全设置
执行完成后，您应该看到：
```
🔒 安全配置已完成
请测试应用功能是否正常
如有问题请检查认证状态
```

### 步骤4：测试应用
1. 刷新您的应用页面
2. 尝试登录和访问数据
3. 检查所有功能是否正常

## 如果遇到问题

### 问题1：应用无法访问数据
**解决方案**：检查用户是否已登录，RLS需要认证用户

### 问题2：某些功能不工作
**解决方案**：
1. 检查控制台错误信息
2. 可能需要调整安全策略
3. 确认用户角色设置正确

### 问题3：完全无法访问
**紧急回滚**：
```sql
-- 临时禁用RLS（仅用于紧急情况）
ALTER TABLE students DISABLE ROW LEVEL SECURITY;
ALTER TABLE medals DISABLE ROW LEVEL SECURITY;
-- ... 其他表
```

## 安全检查清单

- [ ] ✅ 已执行安全脚本
- [ ] ✅ RLS已启用
- [ ] ✅ 安全策略已创建
- [ ] ✅ 应用功能正常
- [ ] ✅ 用户可以正常登录
- [ ] ✅ 数据访问受限制

## 重要提醒

1. **备份数据**：在执行前确保有数据备份
2. **测试环境**：如有可能，先在测试环境执行
3. **监控日志**：执行后监控应用日志
4. **用户通知**：如有必要，通知用户可能的短暂中断

---

**当前状态**：您的数据库处于高风险状态，请立即执行上述步骤！
