<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统性能监控 - 班级成绩管理系统</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .performance-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .performance-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .metric-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .metric-label {
            color: #666;
            margin-top: 5px;
        }
        
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        
        .recommendations {
            margin-top: 20px;
        }
        
        .recommendation-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        
        .recommendation-success {
            background: #d4edda;
            border-color: #28a745;
        }
        
        .recommendation-warning {
            background: #fff3cd;
            border-color: #ffc107;
        }
        
        .recommendation-error {
            background: #f8d7da;
            border-color: #dc3545;
        }
        
        .recommendation-info {
            background: #d1ecf1;
            border-color: #17a2b8;
        }
        
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        
        .refresh-btn:hover {
            background: #0056b3;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="performance-container">
        <h1>系统性能监控</h1>
        
        <button class="refresh-btn" onclick="loadPerformanceData()">刷新数据</button>
        
        <div id="loading" class="loading">
            正在加载性能数据...
        </div>
        
        <div id="performance-content" style="display: none;">
            <!-- 性能指标 -->
            <div class="performance-card">
                <h2>性能指标</h2>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="total-requests">-</div>
                        <div class="metric-label">总请求数</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="avg-response-time">-</div>
                        <div class="metric-label">平均响应时间 (ms)</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="slow-requests">-</div>
                        <div class="metric-label">慢请求数</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="slow-percentage">-</div>
                        <div class="metric-label">慢请求比例 (%)</div>
                    </div>
                </div>
            </div>
            
            <!-- 缓存统计 -->
            <div class="performance-card">
                <h2>缓存统计</h2>
                <div class="metric-grid">
                    <div class="metric-item">
                        <div class="metric-value" id="cache-total">-</div>
                        <div class="metric-label">缓存总数</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="cache-valid">-</div>
                        <div class="metric-label">有效缓存</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="cache-expired">-</div>
                        <div class="metric-label">过期缓存</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value" id="recent-requests">-</div>
                        <div class="metric-label">最近1分钟请求</div>
                    </div>
                </div>
            </div>
            
            <!-- 优化建议 -->
            <div class="performance-card">
                <h2>优化建议</h2>
                <div id="recommendations" class="recommendations">
                    <!-- 建议将在这里动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 加载性能数据
        async function loadPerformanceData() {
            const loading = document.getElementById('loading');
            const content = document.getElementById('performance-content');
            
            loading.style.display = 'block';
            content.style.display = 'none';
            
            try {
                const response = await fetch('/api/performance');
                const result = await response.json();
                
                if (result.success) {
                    displayPerformanceData(result.data);
                } else {
                    throw new Error('获取性能数据失败');
                }
            } catch (error) {
                console.error('加载性能数据失败:', error);
                loading.innerHTML = '加载失败，请刷新重试';
            }
        }
        
        // 显示性能数据
        function displayPerformanceData(data) {
            const { performance, recommendations } = data;
            
            // 更新性能指标
            document.getElementById('total-requests').textContent = performance.totalRequests;
            document.getElementById('avg-response-time').textContent = performance.averageResponseTime;
            document.getElementById('slow-requests').textContent = performance.slowRequests;
            document.getElementById('slow-percentage').textContent = performance.slowRequestPercentage;
            
            // 更新缓存统计
            document.getElementById('cache-total').textContent = performance.cacheStats.total;
            document.getElementById('cache-valid').textContent = performance.cacheStats.valid;
            document.getElementById('cache-expired').textContent = performance.cacheStats.expired;
            document.getElementById('recent-requests').textContent = performance.recentRequestsCount;
            
            // 设置状态颜色
            setStatusColor('avg-response-time', performance.averageResponseTime, 500, 1000);
            setStatusColor('slow-percentage', performance.slowRequestPercentage, 5, 15);
            
            // 显示建议
            displayRecommendations(recommendations);
            
            // 显示内容
            document.getElementById('loading').style.display = 'none';
            document.getElementById('performance-content').style.display = 'block';
        }
        
        // 设置状态颜色
        function setStatusColor(elementId, value, warningThreshold, errorThreshold) {
            const element = document.getElementById(elementId);
            element.className = 'metric-value';
            
            if (value < warningThreshold) {
                element.classList.add('status-good');
            } else if (value < errorThreshold) {
                element.classList.add('status-warning');
            } else {
                element.classList.add('status-error');
            }
        }
        
        // 显示建议
        function displayRecommendations(recommendations) {
            const container = document.getElementById('recommendations');
            container.innerHTML = '';
            
            recommendations.forEach(rec => {
                const div = document.createElement('div');
                div.className = `recommendation-item recommendation-${rec.type}`;
                div.textContent = rec.message;
                container.appendChild(div);
            });
        }
        
        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', loadPerformanceData);
        
        // 每30秒自动刷新
        setInterval(loadPerformanceData, 30000);
    </script>
</body>
</html>
