{"version": 2, "name": "class-grade-management-v2", "functions": {"api/index.js": {"maxDuration": 30}}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/index.js"}, {"source": "/gl", "destination": "/api/index.js"}, {"source": "/teacher/gl", "destination": "/api/index.js"}, {"source": "/student", "destination": "/api/index.js"}, {"source": "/studentsign", "destination": "/api/index.js"}, {"source": "/stusign", "destination": "/api/index.js"}, {"source": "/student/stutype", "destination": "/api/index.js"}, {"source": "/stutype", "destination": "/api/index.js"}, {"source": "/teacher", "destination": "/api/index.js"}, {"source": "/teacher/medal", "destination": "/api/index.js"}, {"source": "/teacher/type", "destination": "/api/index.js"}, {"source": "/teacher/reg", "destination": "/api/index.js"}, {"source": "/respass", "destination": "/api/index.js"}, {"source": "/reg", "destination": "/api/index.js"}, {"source": "/", "destination": "/api/index.js"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}