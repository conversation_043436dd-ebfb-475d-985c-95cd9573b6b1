<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>班级成绩管理系统</title>
    <link rel="stylesheet" href="/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- SheetJS库 - 用于Excel导出 -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <!-- 数据库服务 -->
    <script src="/js/db.js"></script>
    <style>
        /* ... existing styles ... */
        
        .local-server-warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border: 1px solid #ffeeba;
            display: none;
        }
        
        /* 刷新按钮样式 */
        .panel-title-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 10px;
        }
        
        .refresh-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: #4a90e2;
            padding: 5px;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }
        
        .refresh-btn:hover {
            background-color: #f0f0f0;
        }

        /* 注册和登录链接样式 */
        .register-link, .login-link {
            text-align: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .register-link p, .login-link p {
            margin: 0;
            color: rgba(0, 0, 0, 0.8);
            font-size: 14px;
        }

        .register-link a, .login-link a {
            color: #4a90e2;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .register-link a:hover, .login-link a:hover {
            color: #357abd;
            text-decoration: underline;
        }

        /* 角色切换按钮样式 */
        .role-switch {
            text-align: right;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .role-btn {
            background: linear-gradient(135deg, #4a90e2, #67b8ff);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(74, 144, 226, 0.3);
            width: auto;
            min-width: 60px;
        }

        .role-btn:hover {
            background: linear-gradient(135deg, #357abd, #5ba3f5);
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(74, 144, 226, 0.4);
        }

        /* 注册表单特殊样式 */
        .register-form input {
            margin-bottom: 12px;
        }

        .register-form input:last-of-type {
            margin-bottom: 20px;
        }

        /* 表单验证错误提示 */
        .form-error {
            color: #ff6b6b;
            font-size: 12px;
            margin-top: 5px;
            margin-bottom: 10px;
            text-align: left;
            background: rgba(255, 107, 107, 0.1);
            padding: 8px;
            border-radius: 4px;
            border-left: 3px solid #ff6b6b;
        }

        /* 成功提示 */
        .form-success {
            color: #51cf66;
            font-size: 12px;
            margin-top: 5px;
            margin-bottom: 10px;
            text-align: center;
            background: rgba(81, 207, 102, 0.1);
            padding: 8px;
            border-radius: 4px;
            border-left: 3px solid #51cf66;
        }

        /* 注册容器样式 - 与登录容器保持一致 */
        #register-container {
            /* 移除自定义背景，使用与主界面相同的背景 */
        }

        /* 注册表单样式 - 与登录表单保持一致 */
        .register-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .register-form input {
            width: 100%;
            padding: 15px 20px;
            border: none;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            color: #333;
            outline: none;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .register-form input:focus {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .register-form input::placeholder {
            color: #999;
            font-weight: 400;
        }

        /* 注册按钮样式 - 与登录按钮保持一致 */
        #register-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #4a90e2, #67b8ff);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: none;
        }

        #register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
        }

        #register-btn:active {
            transform: translateY(0);
        }

        #register-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }
        
        .refresh-btn svg {
            width: 16px;
            height: 16px;
        }
        
        /* 对话框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            position: relative;
            background-color: #fff;
            margin: 10% auto;
            padding: 20px;
            width: 50%;
            max-width: 500px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }
        
        .modal-title {
            margin: 0;
            font-size: 1.25rem;
        }
        
        .close {
            cursor: pointer;
            font-size: 24px;
            font-weight: bold;
        }
        
        .modal-body {
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        
        .modal-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .modal-btn-primary {
            background-color: #4a90e2;
            color: white;
        }
        
        .modal-btn-secondary {
            background-color: #e0e0e0;
            color: #333;
        }
        
        /* 当进步之星排行模式生效时，隐藏图表容器 */
        body[data-ranking-type="progress"] .chart-container {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="local-server-warning" id="server-warning">
        <strong>注意：</strong> 检测到您直接从文件系统打开此页面。由于浏览器安全限制，API请求将不起作用。
        请使用以下方式运行：
        <ol>
            <li>使用Node.js服务器: <code>npm start</code></li>
            <li>然后在浏览器中访问: <code>http://localhost:3000/index.html</code></li>
        </ol>
        <button onclick="this.parentElement.style.display='none'">我知道了</button>
    </div>

    <!-- 添加背景动画元素 -->
    <div class="animated-background">
        <div class="shape shape1"></div>
        <div class="shape shape2"></div>
        <div class="shape shape3"></div>
        <div class="shape shape4"></div>
    </div>

    <!-- 登录界面 -->
    <div id="login-container">
        <div class="login-box glass-effect">
            <h1>班级成绩管理系统</h1>
            <div class="login-form">
                <input type="text" id="username" placeholder="用户名" required>
                <input type="password" id="password" placeholder="密码" required>
                <button id="login-btn">登录</button>
                <div class="register-link">
                    <p>还没有账户？<a href="/teacher/reg" id="teacher-register-link">教师注册</a></p>
                </div>
                <div class="register-link">
                    <p><a href="/respass" id="forgot-password-link">忘记密码？</a></p>
                </div>
                <div class="role-switch">
                    <button id="student-role-btn" class="role-btn">我是学生</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册界面 -->
    <div id="register-container" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; justify-content: center; align-items: center; z-index: 900;">
        <div class="login-box glass-effect">
            <h1>教师注册</h1>
            <div class="login-form">
                <input type="text" id="reg-username" placeholder="用户名 (3-20个字符)" required>
                <input type="password" id="reg-password" placeholder="密码 (至少8位，包含字母和数字)" required>
                <input type="password" id="reg-confirm-password" placeholder="确认密码" required>
                <button id="register-btn" class="login-btn">注册</button>
                <div class="register-link">
                    <p>已有账户？<a href="#" id="show-login">返回登录</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- 重置密码界面 -->
    <div id="reset-password-container" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; justify-content: center; align-items: center; z-index: 900;">
        <div class="login-box glass-effect">
            <h1>重置密码</h1>
            <div class="login-form">
                <div id="reset-step-1">
                    <input type="text" id="reset-username" placeholder="请输入用户名" required>
                    <input type="password" id="reset-new-password" placeholder="请输入新密码（至少6位）" required>
                    <input type="password" id="reset-confirm-password" placeholder="请确认新密码" required>
                    <div style="margin: 15px 0; padding: 12px; background: rgba(255, 255, 255, 0.8); border-radius: 12px; border: none; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);">
                        <label style="color: #333; font-size: 14px; display: flex; align-items: center; cursor: pointer;">
                            <input type="checkbox" id="generate-random-password" style="margin-right: 8px; transform: scale(1.1);">
                            <span>自动生成随机密码（8位字母数字组合）</span>
                        </label>
                    </div>
                    <button id="reset-request-btn" class="login-btn">重置密码</button>
                    <div class="register-link">
                        <p><a href="#" id="back-to-login">返回登录</a></p>
                    </div>
                </div>
                <div id="reset-step-2" style="display: none;">
                    <div style="background: rgba(255, 255, 255, 0.8); padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);">
                        <h3 style="color: #28a745; margin-bottom: 15px; text-align: center;">
                            <i class="fas fa-check-circle"></i> 密码重置成功
                        </h3>
                        <p style="color: #333; margin-bottom: 15px; text-align: center;">
                            您的新密码是：
                        </p>
                        <div style="background: rgba(255, 255, 255, 0.9); padding: 15px; border-radius: 12px; margin-bottom: 15px; border: 2px solid #28a745; text-align: center; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);">
                            <span id="new-password-display" style="font-family: 'Courier New', monospace; font-size: 20px; color: #333; font-weight: bold; letter-spacing: 2px;"></span>
                            <br>
                            <button id="copy-password-btn" style="margin-top: 10px; padding: 12px 20px; background: linear-gradient(90deg, #28a745, #20c997); color: white; border: none; border-radius: 12px; cursor: pointer; font-size: 14px; font-weight: 600; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3); transition: all 0.3s;">
                                <i class="fas fa-copy"></i> 复制密码
                            </button>
                        </div>
                        <div style="background: rgba(255, 243, 205, 0.8); padding: 12px; border-radius: 12px; border-left: 4px solid #ffc107; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);">
                            <p style="color: #856404; font-size: 14px; margin: 0;">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>重要提醒：</strong>请妥善保存此密码，登录后建议立即修改为您熟悉的密码
                            </p>
                        </div>
                    </div>
                    <button id="reset-login-btn" class="login-btn">使用新密码登录</button>
                    <div class="register-link">
                        <p><a href="#" id="back-to-login-2">返回登录</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能选择界面 -->
    <div id="module-selector" style="display:none;">
        <div class="selector-box glass-effect">
            <h1>班级成绩管理系统</h1>
            <p>欢迎回来，<span id="user-name"></span>老师</p>
            <div class="module-buttons">
                <button id="medal-btn" class="module-btn glass-effect">
                    <div class="icon">🏅</div>
                    <div class="text">奖章管理</div>
                </button>
                <button id="typing-btn" class="module-btn glass-effect">
                    <div class="icon">⌨️</div>
                    <div class="text">打字管理</div>
                </button>
                <button id="management-btn" class="module-btn glass-effect">
                    <div class="icon">⚙️</div>
                    <div class="text">管理</div>
                </button>
            </div>
            <button id="logout-btn" class="logout-btn">退出登录</button>
        </div>
    </div>

    <!-- 模块容器 -->
    <div id="module-container" style="display:none;">
        <header>
            <h1 id="module-title">模块标题</h1>
            <div class="header-actions">
                <button id="back-btn">返回选择</button>
                <button id="logout-btn2">退出登录</button>
            </div>
        </header>
        
        <!-- 奖章管理模块 -->
        <div id="medal-module" class="module-content" style="display:none;">
            <div class="two-column-layout">
                <!-- 左侧：筛选和数据管理 -->
                <div class="left-column">
                    <!-- 筛选面板 -->
                    <div class="panel">
                        <div class="panel-title">筛选条件</div>
                        <div class="filters">
                            <div class="filter-item">
                                <label for="medal-school-filter">学校：</label>
                                <select id="medal-school-filter">
                                    <option value="">全部学校</option>
                                </select>
                            </div>
                            <div class="filter-item">
                                <label for="medal-grade-filter">年级：</label>
                                <select id="medal-grade-filter">
                                    <option value="">全部年级</option>
                                </select>
                            </div>
                            <div class="filter-item">
                                <label for="medal-class-filter">班级：</label>
                                <select id="medal-class-filter">
                                    <option value="">全部班级</option>
                                </select>
                            </div>
                            <div class="filter-item">
                                <label for="medal-search">搜索学生：</label>
                                <input type="text" id="medal-search" placeholder="搜索学生...">
                            </div>
                            <div class="buttons">
                                <button id="medal-reset-filter">重置筛选</button>
                                <button id="medal-export" class="action-btn">导出数据</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据表格面板 -->
                    <div class="panel">
                        <div class="panel-title-container">
                            <div class="panel-title">学生奖章数据</div>
                            <button id="medal-refresh" class="refresh-btn" title="刷新数据">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.3"/>
                                </svg>
                            </button>
                        </div>
                        <div class="batch-actions">
                            <button id="select-all-medal" class="batch-btn">全选</button>
                            <button id="deselect-all-medal" class="batch-btn">取消全选</button>
                            <button id="batch-increase-medal" class="batch-btn">批量加章</button>
                            <button id="batch-decrease-medal" class="batch-btn">批量减章</button>
                            <input type="number" id="medal-change-count" min="1" max="10" value="1" class="medal-count-input">
                        </div>
                        <div class="data-grid">
                            <table id="medal-table" class="data-table">
                                <thead>
                                    <tr>
                                        <th width="40"><input type="checkbox" id="medal-select-all"></th>
                                        <th>学校</th>
                                        <th>年级</th>
                                        <th>班级</th>
                                        <th>姓名</th>
                                        <th>奖章数量</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="medal-data">
                                    <!-- 奖章数据将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：排行榜和图表 -->
                <div class="right-column">
                    <div class="rankings">
                        <div class="ranking-box">
                            <div class="ranking-header">
                                <h3>奖章排行榜</h3>
                                <div class="ranking-type">
                                    <label>排行类型：</label>
                                    <select id="medal-ranking-type">
                                        <option value="individual">个人排行</option>
                                        <option value="class">班级排行</option>
                                        <option value="grade">年级排行</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- 文本形式排行榜 -->
                            <div id="medal-ranking"></div>
                            
                            <!-- 图表形式排行榜 -->
                            <div class="chart-container">
                                <canvas id="medal-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 打字管理模块 -->
        <div id="typing-module" class="module-content" style="display:none;">
            <div class="two-column-layout">
                <!-- 左侧：筛选条件和数据表格 -->
                <div class="left-column">
                    <div class="panel">
                        <div class="panel-title">筛选条件</div>
                        <div class="filters">
                            <div class="filter-item">
                                <label for="typing-school-filter">学校：</label>
                                <select id="typing-school-filter">
                                    <option value="">全部学校</option>
                                </select>
                            </div>
                            <div class="filter-item">
                                <label for="typing-grade-filter">年级：</label>
                                <select id="typing-grade-filter">
                                    <option value="">全部年级</option>
                                </select>
                            </div>
                            <div class="filter-item">
                                <label for="typing-class-filter">班级：</label>
                                <select id="typing-class-filter">
                                    <option value="">全部班级</option>
                                </select>
                            </div>
                            <div class="filter-item">
                                <label for="typing-search">搜索学生：</label>
                                <input type="text" id="typing-search" placeholder="搜索学生...">
                            </div>
                            <div class="buttons">
                                <button id="typing-reset-filter">重置筛选</button>
                                <button id="typing-export">导出数据</button>
                                <button id="add-article-btn">添加文章</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据表格面板 -->
                    <div class="panel">
                        <div class="panel-title-container">
                            <div class="panel-title">学生打字数据</div>
                            <button id="typing-refresh" class="refresh-btn" title="刷新数据">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.3"/>
                                </svg>
                            </button>
                        </div>
                        <div class="data-grid">
                            <table id="typing-table" class="data-table">
                                <thead>
                                    <tr>
                                        <th>学校</th>
                                        <th>年级</th>
                                        <th>班级</th>
                                        <th>姓名</th>
                                        <th>打字速度(字/分钟)</th>
                                    </tr>
                                </thead>
                                <tbody id="typing-data">
                                    <!-- 打字数据将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：排行榜和图表 -->
                <div class="right-column">
                    <div class="rankings">
                        <div class="ranking-box">
                            <div class="ranking-header">
                                <h3>打字排行榜</h3>
                                <div class="ranking-type">
                                    <label>排行类型：</label>
                                    <select id="typing-ranking-type">
                                        <option value="individual">个人排行</option>
                                        <option value="progress">进步之星</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- 文本形式排行榜 -->
                            <div id="typing-ranking"></div>
                            
                            <!-- 图表形式排行榜 -->
                            <div class="chart-container">
                                <canvas id="typing-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loading" class="loading-overlay" style="display:none;">
        <div class="spinner"></div>
        <div class="loading-text">加载中...</div>
    </div>

    <!-- 提示弹窗 -->
    <div id="message" class="message-box" style="display:none;"></div>
    
    <!-- 确认弹窗 -->
    <div id="confirm" class="confirm-box" style="display:none;">
        <div class="confirm-content glass-effect">
            <div class="confirm-message">确认要继续吗？</div>
            <div class="confirm-buttons">
                <button id="confirm-yes">确定</button>
                <button id="confirm-no">取消</button>
            </div>
        </div>
    </div>
    
    <!-- 添加学生对话框 -->
    <div id="add-student-modal" class="modal" style="display:none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">添加学生</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="add-student-grade">年级：</label>
                    <select id="add-student-grade" required>
                        <option value="一年级">一年级</option>
                        <option value="二年级">二年级</option>
                        <option value="三年级">三年级</option>
                        <option value="四年级">四年级</option>
                        <option value="五年级">五年级</option>
                        <option value="六年级">六年级</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="add-student-class">班级：</label>
                    <input type="text" id="add-student-class" placeholder="请输入班级" required>
                </div>
                <div class="form-group">
                    <label for="add-student-name">姓名：</label>
                    <input type="text" id="add-student-name" placeholder="请输入学生姓名" required>
                </div>
                <div class="form-group">
                    <label for="add-student-medals">奖章数量：</label>
                    <input type="number" id="add-student-medals" min="0" value="0" required>
                </div>
            </div>
            <div class="modal-footer">
                <button id="add-student-cancel" class="modal-btn modal-btn-secondary">取消</button>
                <button id="add-student-save" class="modal-btn modal-btn-primary">保存</button>
            </div>
        </div>
    </div>

    <!-- 添加文章弹窗 -->
    <div id="article-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">添加新文章</h2>
                <span id="close-article-modal" class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="article-title">标题 <span class="required">*</span></label>
                    <input type="text" id="article-title" placeholder="请输入文章标题">
                </div>
                <div class="form-group">
                    <label for="article-content">内容 <span class="required">*</span></label>
                    <textarea id="article-content" rows="10" placeholder="请输入文章内容"></textarea>
                </div>
                <div class="form-group">
                    <label for="article-language">语言 <span class="required">*</span></label>
                    <select id="article-language">
                        <option value="zh" selected>中文</option>
                        <option value="en">英文</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="article-difficulty">难度 (1-5) <span class="required">*</span></label>
                    <input type="number" id="article-difficulty" min="1" max="5" value="3">
                </div>
                <div class="form-group">
                    <label for="article-grade">建议使用年级</label>
                    <input type="text" id="article-grade" placeholder="例如: 四年级 (选填)">
                </div>
            </div>
            <div class="modal-footer">
                <button id="cancel-article" class="modal-btn modal-btn-secondary">取消</button>
                <button id="save-article" class="modal-btn modal-btn-primary">保存文章</button>
            </div>
        </div>
    </div>

    <!-- JavaScript 文件 -->
    <script src="/js/config.js"></script>
    <script src="/js/utils.js"></script>
    <script src="/js/medal.js"></script>
    <script src="/js/typing.js"></script>
    <script src="/js/main.js"></script>
    <script>
        // 检测是否从文件系统直接访问
        if (window.location.protocol === 'file:') {
            document.getElementById('server-warning').style.display = 'block';
        }
    </script>
    
    <!-- 添加文章按钮修复脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 创建一个函数来检查打字模块的可见性并绑定事件
            function checkAndBindArticleButton() {
                // 检查打字管理模块是否可见
                const typingModule = document.getElementById('typing-module');
                if (typingModule && getComputedStyle(typingModule).display !== 'none') {
                    console.log('打字管理模块已加载，尝试绑定添加文章按钮...');
                    
                    // 获取添加文章按钮
                    const addArticleBtn = document.getElementById('add-article-btn');
                    if (addArticleBtn) {
                        console.log('找到添加文章按钮，绑定事件');
                        
                        // 移除可能存在的事件监听器（如果有的话）
                        // addArticleBtn.removeEventListener('click', showArticleModal);
                        
                        // 绑定点击事件到typing.js中的方法
                        addArticleBtn.addEventListener('click', function() {
                            console.log('添加文章按钮被点击，调用typing.js方法');
                            if (window.TypingManager && window.TypingManager.showAddArticleModal) {
                                window.TypingManager.showAddArticleModal();
                            } else {
                                console.error('TypingManager.showAddArticleModal方法未找到');
                            }
                        });
                        
                        console.log('添加文章按钮事件已绑定');
                    } else {
                        console.error('未找到添加文章按钮元素');
                    }
                    
                    // 绑定弹窗相关事件
                    setupModalEvents();
                }
            }
            
            // 设置模态框事件
            function setupModalEvents() {
                // 关闭文章弹窗的处理
                const closeBtn = document.getElementById('close-article-modal');
                const cancelBtn = document.getElementById('cancel-article');
                
                if (closeBtn) {
                    closeBtn.addEventListener('click', function() {
                        if (window.TypingManager && window.TypingManager.hideAddArticleModal) {
                            window.TypingManager.hideAddArticleModal();
                        }
                    });
                }

                if (cancelBtn) {
                    cancelBtn.addEventListener('click', function() {
                        if (window.TypingManager && window.TypingManager.hideAddArticleModal) {
                            window.TypingManager.hideAddArticleModal();
                        }
                    });
                }

                // 保存文章的处理
                const saveBtn = document.getElementById('save-article');
                if (saveBtn) {
                    saveBtn.addEventListener('click', function() {
                        if (window.TypingManager && window.TypingManager.submitArticleForm) {
                            window.TypingManager.submitArticleForm();
                        }
                    });
                }
            }
            

            
            // 初始调用一次，检查当前状态
            checkAndBindArticleButton();
            
            // 监听可能触发模块显示的事件
            const typingBtn = document.getElementById('typing-btn');
            if (typingBtn) {
                typingBtn.addEventListener('click', function() {
                    // 等待模块加载完成
                    setTimeout(checkAndBindArticleButton, 500);
                });
            }
            
            // 创建一个MutationObserver来监听DOM变化
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && 
                        mutation.attributeName === 'style' && 
                        mutation.target.id === 'typing-module') {
                        checkAndBindArticleButton();
                    }
                });
            });
            
            // 开始观察打字模块的显示状态变化
            const typingModule = document.getElementById('typing-module');
            if (typingModule) {
                observer.observe(typingModule, { attributes: true });
            }
        });
    </script>
</body>
</html> 