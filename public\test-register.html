<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #4a90e2;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #357abd;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>教师注册功能测试</h1>
        <p>这是一个简单的测试页面，用于验证注册API是否正常工作。</p>
        
        <form id="test-form">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="testuser123" required>
            </div>

            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="test123456" required>
            </div>

            <div class="form-group">
                <label for="confirmPassword">确认密码:</label>
                <input type="password" id="confirmPassword" value="test123456" required>
            </div>
            
            <button type="submit">测试注册</button>
        </form>
        
        <div id="result" class="result"></div>
        
        <hr style="margin: 30px 0;">
        
        <h3>API测试</h3>
        <button onclick="testAPI()">测试API连接</button>
        <button onclick="testErrorHandling()">测试错误处理</button>
        <div id="api-result" class="result"></div>
    </div>

    <script>
        document.getElementById('test-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                confirmPassword: document.getElementById('confirmPassword').value
            };
            
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在注册...';
            
            try {
                console.log('=== 测试页面调试 ===');
                console.log('发送的数据:', formData);

                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);

                const responseText = await response.text();
                console.log('原始响应文本:', responseText);

                let result;
                try {
                    result = JSON.parse(responseText);
                    console.log('解析后的结果:', result);
                } catch (parseError) {
                    console.error('JSON解析错误:', parseError);
                    throw new Error(`无法解析响应: ${responseText}`);
                }

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>注册成功！</h4>
                        <p>消息: ${result.message}</p>
                        <p>用户ID: ${result.data?.id}</p>
                        <p>用户名: ${result.data?.username}</p>
                        <p>显示姓名: ${result.data?.display_name}</p>
                        <h5>完整响应:</h5>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>注册失败</h4>
                        <p>状态码: ${response.status}</p>
                        <p>错误: ${result.error}</p>
                        <h5>完整响应:</h5>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                console.error('=== 测试页面错误 ===');
                console.error('错误对象:', error);
                console.error('错误类型:', typeof error);
                console.error('错误消息:', error.message);

                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>请求失败</h4>
                    <p>错误类型: ${typeof error}</p>
                    <p>错误消息: ${error.message}</p>
                    <p>错误对象: ${JSON.stringify(error, null, 2)}</p>
                `;
            }
        });
        
        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '测试中...';

            try {
                const response = await fetch('/api/test-register');
                const result = await response.json();

                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>API连接正常</h4>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>API连接失败</h4>
                    <p>错误: ${error.message}</p>
                `;
            }
        }

        async function testErrorHandling() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '测试错误处理...';

            // 测试用重复用户名注册
            const duplicateData = {
                username: 'admin', // 使用已存在的用户名
                password: 'test123456',
                confirmPassword: 'test123456'
            };

            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(duplicateData)
                });

                const responseText = await response.text();
                console.log('错误测试响应文本:', responseText);

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    result = { error: `解析失败: ${responseText}` };
                }

                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>错误处理测试结果</h4>
                    <p>状态码: ${response.status}</p>
                    <p>错误信息: ${result.error}</p>
                    <h5>完整响应:</h5>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>错误处理测试失败</h4>
                    <p>错误: ${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
