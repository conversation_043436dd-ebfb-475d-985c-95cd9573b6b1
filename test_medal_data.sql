-- 测试奖章数据查询
-- 请在Supabase SQL Editor中执行此脚本来验证数据

-- 1. 查看学生表中的数据
SELECT 
  student_identifier,
  name,
  grade,
  class,
  school_id
FROM students 
ORDER BY school_id, grade, class, name
LIMIT 10;

-- 2. 查看奖章表中的数据
SELECT 
  student_identifier,
  count,
  created_at,
  updated_at
FROM medals 
ORDER BY student_identifier
LIMIT 10;

-- 3. 查看学生和奖章的关联查询（模拟后端查询）
SELECT 
  s.student_identifier,
  s.name,
  s.grade,
  s.class,
  s.school_id,
  COALESCE(m.count, 0) as medal_count
FROM students s
LEFT JOIN medals m ON s.student_identifier = m.student_identifier
ORDER BY s.school_id, s.grade, s.class, s.name
LIMIT 10;

-- 4. 统计信息
SELECT 
  '学生总数' as info,
  COUNT(*) as count
FROM students
UNION ALL
SELECT 
  '奖章记录数' as info,
  COUNT(*) as count
FROM medals
UNION ALL
SELECT 
  '有奖章的学生数' as info,
  COUNT(DISTINCT m.student_identifier) as count
FROM medals m
WHERE m.count > 0;

-- 5. 如果奖章表为空，插入一些测试数据
-- 取前5个学生，给他们一些奖章
INSERT INTO medals (student_identifier, count)
SELECT 
  student_identifier,
  (RANDOM() * 10)::INTEGER + 1 as count
FROM students 
WHERE student_identifier NOT IN (SELECT student_identifier FROM medals)
ORDER BY student_identifier
LIMIT 5
ON CONFLICT (student_identifier) DO NOTHING;

-- 6. 验证插入结果
SELECT 
  s.student_identifier,
  s.name,
  COALESCE(m.count, 0) as medal_count
FROM students s
LEFT JOIN medals m ON s.student_identifier = m.student_identifier
ORDER BY s.student_identifier
LIMIT 10;
