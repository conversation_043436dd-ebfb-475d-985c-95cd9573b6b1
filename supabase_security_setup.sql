-- 🔒 Supabase 紧急安全配置脚本
-- 请在Supabase控制台的SQL编辑器中逐步执行

-- ==========================================
-- 第一步：启用行级安全 (RLS)
-- ==========================================

-- 为所有重要表启用RLS（跳过视图）
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE medals ENABLE ROW LEVEL SECURITY;
ALTER TABLE typing_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE schools ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_class_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE articles ENABLE ROW LEVEL SECURITY;

-- 以下表可能存在，如果不存在会跳过错误
DO $$
BEGIN
    -- 日志表
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'logs') THEN
        ALTER TABLE logs ENABLE ROW LEVEL SECURITY;
    END IF;

    -- 学校年级配置表
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'school_grade_configs') THEN
        ALTER TABLE school_grade_configs ENABLE ROW LEVEL SECURITY;
    END IF;

    -- 学生导入日志表
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'student_import_logs') THEN
        ALTER TABLE student_import_logs ENABLE ROW LEVEL SECURITY;
    END IF;

    -- 学生会话表
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'student_sessions') THEN
        ALTER TABLE student_sessions ENABLE ROW LEVEL SECURITY;
    END IF;

    -- 学生登录表
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'student_signins') THEN
        ALTER TABLE student_signins ENABLE ROW LEVEL SECURITY;
    END IF;

    -- 教师班级表
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'teacher_classes') THEN
        ALTER TABLE teacher_classes ENABLE ROW LEVEL SECURITY;
    END IF;

    -- 教师学校分配表
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'teacher_school_assignments') THEN
        ALTER TABLE teacher_school_assignments ENABLE ROW LEVEL SECURITY;
    END IF;

    -- 最佳打字记录表
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'typing_best') THEN
        ALTER TABLE typing_best ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- ==========================================
-- 第二步：创建基本安全策略
-- ==========================================

-- 学生表安全策略
CREATE POLICY "学生数据查看策略" ON students
    FOR SELECT USING (
        -- 只允许认证用户查看
        auth.role() = 'authenticated'
    );

CREATE POLICY "学生数据插入策略" ON students
    FOR INSERT WITH CHECK (
        -- 只允许管理员插入
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "学生数据更新策略" ON students
    FOR UPDATE USING (
        -- 只允许管理员更新
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "学生数据删除策略" ON students
    FOR DELETE USING (
        -- 只允许管理员删除
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

-- 奖章表安全策略
CREATE POLICY "奖章数据查看策略" ON medals
    FOR SELECT USING (
        -- 只允许认证用户查看
        auth.role() = 'authenticated'
    );

CREATE POLICY "奖章数据修改策略" ON medals
    FOR INSERT WITH CHECK (
        -- 只允许认证用户插入
        auth.role() = 'authenticated'
    );

CREATE POLICY "奖章数据更新策略" ON medals
    FOR UPDATE USING (
        -- 只允许认证用户更新
        auth.role() = 'authenticated'
    );

CREATE POLICY "奖章数据删除策略" ON medals
    FOR DELETE USING (
        -- 只允许认证用户删除
        auth.role() = 'authenticated'
    );

-- 打字记录表安全策略
CREATE POLICY "打字记录查看策略" ON typing_records
    FOR SELECT USING (
        -- 只允许认证用户查看
        auth.role() = 'authenticated'
    );

CREATE POLICY "打字记录插入策略" ON typing_records
    FOR INSERT WITH CHECK (
        -- 只允许认证用户插入
        auth.role() = 'authenticated'
    );

CREATE POLICY "打字记录更新策略" ON typing_records
    FOR UPDATE USING (
        -- 只允许认证用户更新
        auth.role() = 'authenticated'
    );

CREATE POLICY "打字记录删除策略" ON typing_records
    FOR DELETE USING (
        -- 只允许认证用户删除
        auth.role() = 'authenticated'
    );

-- 学校表安全策略
CREATE POLICY "学校数据查看策略" ON schools
    FOR SELECT USING (
        -- 只允许认证用户查看
        auth.role() = 'authenticated'
    );

CREATE POLICY "学校数据插入策略" ON schools
    FOR INSERT WITH CHECK (
        -- 只允许管理员和教师插入
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role IN ('admin', 'teacher')
        )
    );

CREATE POLICY "学校数据更新策略" ON schools
    FOR UPDATE USING (
        -- 只允许管理员和教师更新
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role IN ('admin', 'teacher')
        )
    );

CREATE POLICY "学校数据删除策略" ON schools
    FOR DELETE USING (
        -- 只允许管理员和教师删除
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role IN ('admin', 'teacher')
        )
    );

-- 用户表安全策略
CREATE POLICY "用户查看自己信息策略" ON users
    FOR SELECT USING (
        -- 用户只能查看自己的信息
        id = auth.uid()::text::integer
    );

CREATE POLICY "管理员用户插入策略" ON users
    FOR INSERT WITH CHECK (
        -- 只允许管理员插入用户
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "管理员用户更新策略" ON users
    FOR UPDATE USING (
        -- 只允许管理员更新用户
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "管理员用户删除策略" ON users
    FOR DELETE USING (
        -- 只允许管理员删除用户
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

-- 教师权限表安全策略
CREATE POLICY "教师权限查看策略" ON teacher_class_permissions
    FOR SELECT USING (
        -- 教师只能查看自己的权限，管理员可以查看所有
        teacher_id = auth.uid()::text::integer OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'admin'
        )
    );

CREATE POLICY "教师权限插入策略" ON teacher_class_permissions
    FOR INSERT WITH CHECK (
        -- 只允许管理员插入教师权限
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "教师权限更新策略" ON teacher_class_permissions
    FOR UPDATE USING (
        -- 只允许管理员更新教师权限
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "教师权限删除策略" ON teacher_class_permissions
    FOR DELETE USING (
        -- 只允许管理员删除教师权限
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

-- 文章表安全策略
CREATE POLICY "文章查看策略" ON articles
    FOR SELECT USING (
        -- 只允许认证用户查看
        auth.role() = 'authenticated'
    );

CREATE POLICY "文章插入策略" ON articles
    FOR INSERT WITH CHECK (
        -- 只允许认证用户插入
        auth.role() = 'authenticated'
    );

CREATE POLICY "文章更新策略" ON articles
    FOR UPDATE USING (
        -- 只允许认证用户更新
        auth.role() = 'authenticated'
    );

CREATE POLICY "文章删除策略" ON articles
    FOR DELETE USING (
        -- 只允许认证用户删除
        auth.role() = 'authenticated'
    );

-- ==========================================
-- 第三步：验证安全策略
-- ==========================================

-- 检查RLS是否已启用
SELECT 
    schemaname,
    tablename,
    rowsecurity as "RLS启用状态"
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('students', 'medals', 'typing_records', 'schools', 'users', 'teacher_class_permissions', 'articles');

-- 检查安全策略
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd as "操作类型",
    qual as "策略条件"
FROM pg_policies 
WHERE schemaname = 'public';

-- ==========================================
-- 第四步：测试查询（可选）
-- ==========================================

-- 测试学生数据访问（需要认证）
-- SELECT COUNT(*) FROM students;

-- 测试奖章数据访问（需要认证）
-- SELECT COUNT(*) FROM medals;

-- ==========================================
-- 安全配置完成提示
-- ==========================================

-- ==========================================
-- 补充：其他表的安全策略
-- ==========================================

-- 日志表安全策略（只允许管理员访问）
CREATE POLICY "日志查看策略" ON logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

-- 学校年级配置表
CREATE POLICY "年级配置查看策略" ON school_grade_configs
    FOR SELECT USING (auth.role() = 'authenticated');

-- 学生导入日志表（只允许管理员访问）
CREATE POLICY "导入日志查看策略" ON student_import_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

-- 学生会话表
CREATE POLICY "学生会话查看策略" ON student_sessions
    FOR SELECT USING (auth.role() = 'authenticated');

-- 学生登录表
CREATE POLICY "学生登录查看策略" ON student_signins
    FOR SELECT USING (auth.role() = 'authenticated');

-- 教师班级表
CREATE POLICY "教师班级查看策略" ON teacher_classes
    FOR SELECT USING (auth.role() = 'authenticated');

-- 教师学校分配表
CREATE POLICY "教师学校分配查看策略" ON teacher_school_assignments
    FOR SELECT USING (auth.role() = 'authenticated');

-- 教师可见学生是视图，不需要创建策略
-- CREATE POLICY "教师可见学生查看策略" ON teacher_visible_students
--     FOR SELECT USING (auth.role() = 'authenticated');

-- 最佳打字记录表
CREATE POLICY "最佳打字记录查看策略" ON typing_best
    FOR SELECT USING (auth.role() = 'authenticated');

SELECT
    '🔒 安全配置已完成' as 状态,
    '请测试应用功能是否正常' as 下一步,
    '如有问题请检查认证状态' as 提示;
