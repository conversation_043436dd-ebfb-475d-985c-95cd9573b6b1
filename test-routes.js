/**
 * 路由测试脚本
 * 测试教师管理相关的路由是否正确配置
 */

const http = require('http');

const BASE_URL = 'http://localhost:3005';

// 测试路由列表
const routes = [
    { path: '/teacher/new', description: '新版教师管理页面' },
    { path: '/teacher/gl', description: '旧版教师管理页面（应该重定向）' },
    { path: '/api/teacher/schools', description: '教师学校API', needsAuth: true },
    { path: '/api/auth/validate', description: 'Token验证API', needsAuth: true },
    { path: '/test-api.html', description: 'API测试页面' }
];

/**
 * 测试单个路由
 */
function testRoute(route) {
    return new Promise((resolve) => {
        const options = {
            hostname: 'localhost',
            port: 3005,
            path: route.path,
            method: 'GET',
            headers: {}
        };

        // 如果需要认证，添加测试token
        if (route.needsAuth) {
            options.headers['Authorization'] = 'Bearer test-token';
        }

        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    path: route.path,
                    description: route.description,
                    status: res.statusCode,
                    statusText: res.statusMessage,
                    headers: res.headers,
                    contentType: res.headers['content-type'],
                    isRedirect: res.statusCode >= 300 && res.statusCode < 400,
                    redirectLocation: res.headers.location,
                    success: res.statusCode < 400 || (route.needsAuth && res.statusCode === 401)
                });
            });
        });

        req.on('error', (error) => {
            resolve({
                path: route.path,
                description: route.description,
                status: 'ERROR',
                error: error.message,
                success: false
            });
        });

        req.setTimeout(5000, () => {
            req.destroy();
            resolve({
                path: route.path,
                description: route.description,
                status: 'TIMEOUT',
                error: '请求超时',
                success: false
            });
        });

        req.end();
    });
}

/**
 * 运行所有路由测试
 */
async function runTests() {
    console.log('🧪 开始测试教师管理路由...\n');
    
    const results = [];
    
    for (const route of routes) {
        console.log(`测试: ${route.path} - ${route.description}`);
        const result = await testRoute(route);
        results.push(result);
        
        // 输出结果
        if (result.success) {
            console.log(`✅ ${result.status} ${result.statusText || ''}`);
            if (result.isRedirect) {
                console.log(`   重定向到: ${result.redirectLocation}`);
            }
            if (result.contentType) {
                console.log(`   内容类型: ${result.contentType}`);
            }
        } else {
            console.log(`❌ ${result.status} ${result.statusText || result.error || ''}`);
        }
        console.log('');
    }
    
    // 总结
    console.log('📊 测试总结:');
    console.log('='.repeat(50));
    
    const successful = results.filter(r => r.success).length;
    const total = results.length;
    
    console.log(`总计: ${total} 个路由`);
    console.log(`成功: ${successful} 个`);
    console.log(`失败: ${total - successful} 个`);
    
    if (successful === total) {
        console.log('\n🎉 所有路由测试通过！');
    } else {
        console.log('\n⚠️  部分路由测试失败，请检查配置');
        
        console.log('\n失败的路由:');
        results.filter(r => !r.success).forEach(r => {
            console.log(`- ${r.path}: ${r.status} ${r.error || r.statusText || ''}`);
        });
    }
    
    // 特别检查关键路由
    const teacherNewRoute = results.find(r => r.path === '/teacher/new');
    if (teacherNewRoute) {
        console.log('\n🎯 关键路由检查:');
        if (teacherNewRoute.success && teacherNewRoute.status === 200) {
            console.log('✅ /teacher/new 路由正常工作');
        } else {
            console.log('❌ /teacher/new 路由有问题，需要修复');
        }
    }
}

// 检查服务器是否运行
function checkServer() {
    return new Promise((resolve) => {
        const req = http.request({
            hostname: 'localhost',
            port: 3005,
            path: '/',
            method: 'GET'
        }, (res) => {
            resolve(true);
        });
        
        req.on('error', () => {
            resolve(false);
        });
        
        req.setTimeout(2000, () => {
            req.destroy();
            resolve(false);
        });
        
        req.end();
    });
}

// 主函数
async function main() {
    console.log('🔍 检查服务器状态...');
    
    const serverRunning = await checkServer();
    
    if (!serverRunning) {
        console.log('❌ 服务器未运行或无法连接到 localhost:3005');
        console.log('请先启动服务器: npm start');
        process.exit(1);
    }
    
    console.log('✅ 服务器正在运行\n');
    
    await runTests();
}

// 运行测试
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { testRoute, runTests };
