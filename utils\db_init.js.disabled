/**
 * 数据库初始化脚本
 * 这个脚本用于初始化数据库结构和基础数据
 */

const mysql = require('mysql2/promise');  // 修改为使用promise接口
const fs = require('fs');
const path = require('path');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// 创建数据库连接
async function initDatabase() {
  console.log('开始初始化数据库...');
  
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'Password',  // 使用环境变量中的密码
      multipleStatements: true // 允许执行多条SQL语句
    });
    
    // 读取基本表结构SQL文件
    const sqlFilePath = path.join(__dirname, 'create_tables.sql');
    const sqlScript = fs.readFileSync(sqlFilePath, 'utf8');
    
    // 执行SQL脚本
    console.log('执行基础表结构SQL脚本...');
    await connection.query(sqlScript);
    
    // 使用数据库
    await connection.query(`USE ${process.env.DB_NAME || 'syxxstudent'}`);
    
    // 读取并执行文章表SQL文件
    try {
      const articlesSqlPath = path.join(__dirname, '..', 'sql_files', 'create_articles_table.sql');
      if (fs.existsSync(articlesSqlPath)) {
        const articlesSqlScript = fs.readFileSync(articlesSqlPath, 'utf8');
        console.log('执行文章表SQL脚本...');
        await connection.query(articlesSqlScript);
        console.log('文章表创建成功！');
      } else {
        console.log('未找到文章表SQL文件，跳过创建文章表');
      }
    } catch (articleError) {
      console.error('创建文章表失败:', articleError);
      // 继续执行，不中断整个初始化过程
    }
    
    // 关闭连接
    await connection.end();
    
    console.log('数据库初始化完成！');
    console.log(`
    数据库名称: ${process.env.DB_NAME || 'syxxstudent'}
    
    默认用户:
    管理员 - 用户名: admin, 密码: admin123
    教师 - 用户名: teacher, 密码: teacher123
    
    可以通过API接口访问系统:
    - http://localhost:${process.env.PORT || 3001}/api/test
    `);
    
  } catch (error) {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  }
}

// 执行初始化
initDatabase(); 