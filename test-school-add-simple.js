/**
 * 简单的学校添加功能测试脚本
 * 测试修复后的学校添加功能
 */

async function testSchoolAdd() {
    const BASE_URL = 'http://localhost:3005';
    
    console.log('=== 学校添加功能测试开始 ===\n');

    try {
        // 1. 用户登录
        console.log('1. 正在登录...');
        const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: 'teacher',
                password: 'password'
            })
        });

        if (!loginResponse.ok) {
            throw new Error(`登录失败: ${loginResponse.status} ${loginResponse.statusText}`);
        }

        const loginResult = await loginResponse.json();
        const token = loginResult.token;
        
        if (!token) {
            throw new Error('登录成功但未获取到token');
        }

        console.log('✅ 登录成功');

        // 2. 添加新学校
        const testSchoolName = `测试学校_${Date.now()}`;
        console.log(`\n2. 添加新学校: ${testSchoolName}`);

        const addSchoolResponse = await fetch(`${BASE_URL}/api/teacher/schools`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                name: testSchoolName
            })
        });

        console.log(`   响应状态: ${addSchoolResponse.status} ${addSchoolResponse.statusText}`);

        const addSchoolResult = await addSchoolResponse.json();
        console.log('   响应内容:', JSON.stringify(addSchoolResult, null, 2));

        if (!addSchoolResponse.ok) {
            throw new Error(`添加学校失败: ${addSchoolResult.message || addSchoolResult.error}`);
        }

        if (addSchoolResult.success && addSchoolResult.data) {
            console.log('✅ 学校添加成功');
            console.log(`   学校ID: ${addSchoolResult.data.id}`);
            console.log(`   学校名称: ${addSchoolResult.data.name}`);
        } else {
            throw new Error('添加学校响应格式异常');
        }

        // 3. 验证学校是否添加到数据库
        console.log('\n3. 验证学校是否添加到数据库...');
        
        await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒

        const schoolsResponse = await fetch(`${BASE_URL}/api/teacher/schools`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!schoolsResponse.ok) {
            throw new Error(`获取学校列表失败: ${schoolsResponse.status}`);
        }

        const schools = await schoolsResponse.json();
        const foundSchool = schools.find(school => school.id === addSchoolResult.data.id);

        if (foundSchool) {
            console.log('✅ 学校已成功保存到数据库');
            console.log(`   数据库中的学校: ${foundSchool.name}`);
        } else {
            throw new Error('学校未在数据库中找到！');
        }

        console.log('\n=== 测试完成 ===');
        console.log('🎉 学校添加功能测试通过！');

        return {
            success: true,
            schoolId: addSchoolResult.data.id,
            schoolName: addSchoolResult.data.name
        };

    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        
        return {
            success: false,
            error: error.message
        };
    }
}

// 如果在Node.js环境中运行
if (typeof window === 'undefined') {
    // Node.js环境，需要fetch polyfill
    const { fetch } = require('undici');
    global.fetch = fetch;
    
    testSchoolAdd().then(result => {
        if (result.success) {
            console.log('\n✅ 所有测试通过！');
            process.exit(0);
        } else {
            console.log('\n❌ 测试失败！');
            process.exit(1);
        }
    }).catch(error => {
        console.error('测试脚本执行失败:', error);
        process.exit(1);
    });
} else {
    // 浏览器环境，直接运行
    window.testSchoolAdd = testSchoolAdd;
}
