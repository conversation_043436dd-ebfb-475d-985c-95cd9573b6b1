/* 学生打字练习系统样式 */

/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
}

body {
    background-color: #f0f4f8;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(74, 144, 226, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(103, 184, 255, 0.05) 0%, transparent 50%);
    color: #333;
    line-height: 1.6;
    position: relative;
    overflow-x: hidden;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

body::before {
    content: "";
    position: fixed;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(
        0deg,
        rgba(120, 120, 120, 0.03) 0px,
        rgba(120, 120, 120, 0.03) 1px,
        transparent 1px,
        transparent 50px
    ),
    repeating-linear-gradient(
        90deg,
        rgba(120, 120, 120, 0.03) 0px,
        rgba(120, 120, 120, 0.03) 1px,
        transparent 1px,
        transparent 50px
    );
    z-index: -1;
}

button {
    cursor: pointer;
    transition: all 0.3s ease;
}

input, select {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
}

input:focus, select:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 5px rgba(74, 144, 226, 0.3);
}

/* 签到界面样式 */
#signin-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 900;
}

.signin-box {
    width: 90%;
    max-width: 450px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 12px 36px rgba(31, 38, 135, 0.2);
    padding: 45px 40px;
    border-radius: 20px;
    text-align: center;
    transform: translateY(-5vh);
}

.signin-box h1 {
    color: #333;
    margin-bottom: 30px;
    font-size: 28px;
    font-weight: 600;
    position: relative;
}

.signin-box h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #4a90e2, #67b8ff);
    border-radius: 3px;
}

.signin-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    text-align: left;
    gap: 8px;
}

.form-group label {
    font-weight: 500;
    color: #444;
    font-size: 14px;
}

.form-group input, .form-group select {
    background: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 15px;
    transition: all 0.3s;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.form-group input:focus, .form-group select:focus {
    background: white;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.3);
    transform: translateY(-2px);
}

#signin-btn {
    margin-top: 10px;
    border: none;
    background: linear-gradient(135deg, #4a90e2, #67b8ff);
    color: white;
    padding: 14px 20px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
    transition: all 0.3s ease;
}

#signin-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
}

#signin-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
}

#signin-btn:hover::before {
    animation: shine 1.5s infinite;
}

/* 打字练习界面样式 */
#typing-practice {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 25px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    position: sticky;
    top: 10px;
    z-index: 100;
}

header h1 {
    font-size: 22px;
    color: #333;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

#student-info {
    font-size: 15px;
    color: #555;
    font-weight: 500;
}

#back-to-signin, #back-to-practice, #view-history {
    background: #f0f0f0;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #555;
    transition: all 0.2s;
}

#back-to-signin:hover, #back-to-practice:hover, #view-history:hover {
    background: #e0e0e0;
    color: #333;
}

.practice-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.practice-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.language-switch {
    display: flex;
    gap: 10px;
}

.lang-btn {
    background: #f0f0f0;
    border: none;
    padding: 8px 18px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #555;
    transition: all 0.2s;
}

.lang-btn.active {
    background: #4a90e2;
    color: white;
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.lang-btn:hover:not(.active) {
    background: #e0e0e0;
}

.typing-area {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    width: 100%;
    min-width: 850px;
    box-sizing: border-box;
    overflow-x: auto;
}

#typing-lines-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 12px;
}

.typing-line-pair {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 16px;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

.typing-text-line {
    font-size: 18px;
    line-height: 1.8;
    color: #444;
    background: #f5f8fa;
    padding: 12px 15px;
    border-radius: 8px 8px 0 0;
    white-space: nowrap;
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    border: 1px solid #e0e6eb;
    min-height: 44px;
    height: auto;
    display: block;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
    position: relative;
    width: 100%;
    text-align: left;
    box-sizing: border-box;
    min-width: 0;
    position: relative;
}

.typing-input-line {
    width: 100%;
    height: auto;
    min-height: 44px;
    padding: 12px 15px;
    font-size: 18px;
    line-height: 1.8;
    border: 1px solid #ddd;
    border-radius: 0 0 8px 8px;
    outline: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    background-color: #fff;
    transition: all 0.3s ease;
    resize: none;
    white-space: nowrap;
    overflow: hidden;
    box-sizing: border-box;
    min-width: 0;
}

.typing-input-line:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}

/* 自定义滚动条样式 */
.typing-text-line::-webkit-scrollbar,
.typing-input-line::-webkit-scrollbar {
    height: 6px;
}

.typing-text-line::-webkit-scrollbar-track,
.typing-input-line::-webkit-scrollbar-track {
    background: transparent;
}

.typing-text-line::-webkit-scrollbar-thumb,
.typing-input-line::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    border: 2px solid transparent;
}

.typing-text-line::-webkit-scrollbar-thumb:hover,
.typing-input-line::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3);
}

.typing-text-line span {
    position: relative;
}

.typing-text-line span.correct {
    color: #28a745;
}

.typing-text-line span.incorrect {
    color: #dc3545;
    text-decoration: underline wavy #dc3545;
}

.typing-text-line span.current {
    background-color: rgba(74, 144, 226, 0.2);
    border-radius: 2px;
    position: relative;
    padding: 2px 0;
    border-bottom: 2px solid #4a90e2;
    box-shadow: 0 0 5px rgba(74, 144, 226, 0.5);
    animation: currentCharPulse 1s infinite;
}

.typing-text-line span.current::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #4a90e2;
    animation: blink 1s infinite;
}

/* 添加当前字符脉冲动画 */
@keyframes currentCharPulse {
    0% { background-color: rgba(74, 144, 226, 0.2); }
    50% { background-color: rgba(74, 144, 226, 0.5); }
    100% { background-color: rgba(74, 144, 226, 0.2); }
}

/* 现有的闪烁动画 */
@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
}

.line-completed {
    background-color: #f0fdf0;
    border-color: #c3e6cb;
}

.current-line {
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 10px;
}

#start-practice, #finish-practice {
    padding: 12px 30px;
    font-size: 16px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s;
}

#start-practice {
    background: #4a90e2;
    color: white;
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

#start-practice:hover {
    background: #3a7bc8;
    transform: translateY(-2px);
}

#finish-practice {
    background: #e74c3c;
    color: white;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

#finish-practice:hover:not(:disabled) {
    background: #c0392b;
    transform: translateY(-2px);
}

#finish-practice:disabled {
    background: #ccc;
    color: #777;
    cursor: not-allowed;
    box-shadow: none;
}

/* 历史记录界面样式 */
#history-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.history-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.filter-panel {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 12px;
    align-items: center;
}

.filter-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-item label {
    font-size: 14px;
    color: #555;
    white-space: nowrap;
}

#apply-filter, #export-csv {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    margin-left: auto;
}

#apply-filter {
    background: #4a90e2;
    color: white;
}

#apply-filter:hover {
    background: #3a7bc8;
}

#export-csv {
    background-color: #28a745;
    color: white;
    font-weight: bold;
    padding: 8px 15px;
    border-radius: 4px;
    margin-left: 10px;
    cursor: pointer;
    transition: background-color 0.3s;
}

#export-csv:hover {
    background-color: #218838;
}

.history-records {
    padding: 20px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 12px;
}

.history-records h3 {
    font-size: 18px;
    color: #333;
    margin-bottom: 15px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.data-table th {
    background: #f0f4f8;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #444;
    border-bottom: 2px solid #ddd;
}

.data-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
}

.data-table tbody tr:hover {
    background-color: rgba(74, 144, 226, 0.05);
}

/* 动画背景 */
.animated-background {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
    overflow: hidden;
}

.shape {
    position: absolute;
    border-radius: 50%;
    filter: blur(80px);
    opacity: 0.3;
}

.shape1 {
    width: 400px;
    height: 400px;
    background: #4a90e2;
    top: -150px;
    left: -150px;
    animation: float 15s infinite ease-in-out;
}

.shape2 {
    width: 300px;
    height: 300px;
    background: #67b8ff;
    bottom: -100px;
    right: -100px;
    animation: float 20s infinite ease-in-out reverse;
}

.shape3 {
    width: 350px;
    height: 350px;
    background: #27ae60;
    bottom: 40%;
    left: -100px;
    animation: float 25s infinite ease-in-out;
}

.shape4 {
    width: 250px;
    height: 250px;
    background: #3498db;
    top: 30%;
    right: -50px;
    animation: float 18s infinite ease-in-out reverse;
}

@keyframes float {
    0% {
        transform: translate(0, 0) rotate(0deg);
    }
    25% {
        transform: translate(10px, 15px) rotate(5deg);
    }
    50% {
        transform: translate(5px, -10px) rotate(-5deg);
    }
    75% {
        transform: translate(-10px, 5px) rotate(3deg);
    }
    100% {
        transform: translate(0, 0) rotate(0deg);
    }
}

@keyframes shine {
    0% {
        transform: translateX(-100%);
    }
    20%, 100% {
        transform: translateX(100%);
    }
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(74, 144, 226, 0.2);
    border-radius: 50%;
    border-top-color: #4a90e2;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-text {
    margin-top: 15px;
    color: #4a90e2;
    font-weight: 500;
}

/* 消息框 */
.message-box {
    position: fixed;
    top: -100px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    padding: 15px 30px;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    transition: top 0.3s ease;
    z-index: 1000;
    max-width: 90%;
}

.message-box.show {
    top: 20px;
}

/* 对话框 */
.dialog-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.dialog {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 500px;
    padding: 20px;
    text-align: center;
}

.dialog-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.dialog-message {
    margin-bottom: 20px;
    line-height: 1.5;
    color: #555;
    text-align: left;
}

.dialog-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.dialog-button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

.confirm {
    background-color: #4CAF50;
    color: white;
}

.confirm:hover {
    background-color: #45a049;
}

.cancel {
    background-color: #f44336;
    color: white;
}

.cancel:hover {
    background-color: #d32f2f;
}

/* 玻璃效果 */
.glass-effect {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
}

/* 适配平板/手机 */
@media screen and (max-width: 768px) {
    .signin-box {
        padding: 30px 25px;
        max-width: 90%;
    }
    
    .practice-controls {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    /* statistics-panel的响应式样式已移至顶部 */
    
    header {
        padding: 12px 15px;
    }
    
    .filter-panel {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    #apply-filter, #export-csv {
        width: 100%;
        margin-top: 10px;
    }
    
    .data-table {
        display: block;
        overflow-x: auto;
    }
}

/* 结果弹窗样式 */
.result-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease-in-out;
}

.result-box {
    background: linear-gradient(135deg, #fff 0%, #f0f4f8 100%);
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
    padding: 35px;
    width: 90%;
    max-width: 550px;
    text-align: center;
    animation: scaleIn 0.4s ease-in-out;
    border: 1px solid rgba(255, 255, 255, 0.6);
}

.result-box h2 {
    color: #333;
    font-size: 28px;
    margin-bottom: 30px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    font-weight: 600;
    letter-spacing: 0.5px;
}

.result-content {
    display: flex;
    justify-content: space-around;
    margin-bottom: 25px;
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.result-item {
    text-align: center;
    padding: 20px 30px;
    position: relative;
    flex: 1;
}

.result-item:first-child::after {
    content: '';
    position: absolute;
    right: 0;
    top: 20%;
    height: 60%;
    width: 1px;
    background: rgba(0, 0, 0, 0.1);
}

.result-label {
    font-size: 18px;
    color: #555;
    margin-bottom: 15px;
    font-weight: 500;
}

.result-value {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #333 0%, #666 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.result-unit {
    font-size: 15px;
    color: #777;
    font-weight: 500;
}

#speed-result {
    color: #27ae60;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

#accuracy-result {
    color: #e74c3c;
    background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

#close-result {
    background-color: #4a90e2;
    color: white;
    font-size: 18px;
    padding: 12px 35px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
    font-weight: 600;
}

#close-result:hover {
    background-color: #3a7bca;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(74, 144, 226, 0.4);
}

#close-result:active {
    transform: translateY(-1px);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes scaleIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* 未同步记录的样式 */
.unsync-record {
    background-color: #fffde7 !important; /* 淡黄色背景 */
}

.sync-status {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.sync-status.synced {
    background-color: #4caf50; /* 绿色表示已同步 */
}

.sync-status.unsynced {
    background-color: #f44336; /* 红色表示未同步 */
    animation: pulse 2s infinite; /* 脉冲动画 */
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* 折线图容器样式 */
.chart-container {
    padding: 20px;
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    max-height: 450px; /* 添加最大高度限制 */
    height: auto; /* 自适应高度 */
    box-sizing: border-box; /* 确保padding不增加总高度 */
}

.chart-container h3 {
    font-size: 18px;
    color: #333;
    margin-bottom: 15px;
}

.chart-wrapper {
    height: 400px;
    width: 100%;
    position: relative;
    overflow: hidden;
}

#typing-speed-chart {
    width: 100%;
    height: 100%; /* 使用100%高度填充父容器 */
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    padding: 10px;
    overflow: hidden; /* 确保内容不会溢出 */
}

/* 添加行已满提示样式 */
.line-full {
    background-color: #fffde7; /* 淡黄色背景 */
    border-color: #ffeb3b; /* 黄色边框 */
    animation: flash 1s; /* 闪烁动画 */
}

@keyframes flash {
    0%, 100% { background-color: #fffde7; }
    50% { background-color: #fff9c4; }
}

/* 自动换行后的滑动动画 */
@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.typing-line-pair.new-active {
    animation: slideDown 0.3s ease-out;
}

/* 行高亮效果 */
.line-highlight {
    animation: highlight 0.5s ease-out;
}

@keyframes highlight {
    0% { background-color: rgba(74, 144, 226, 0.3); }
    100% { background-color: #f5f8fa; }
}

/* 自动换行通知 */
.auto-line-change-notification {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(74, 144, 226, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    animation: fadeInOut 1s forwards;
    z-index: 100;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(-50%) translateX(20px); }
    20% { opacity: 1; transform: translateY(-50%) translateX(0); }
    80% { opacity: 1; transform: translateY(-50%) translateX(0); }
    100% { opacity: 0; transform: translateY(-50%) translateX(-20px); }
}

/* 文章选择区域样式 */
.article-selection-desc {
    margin-bottom: 15px;
    color: #666;
    font-size: 16px;
    line-height: 1.4;
}

.article-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
    max-height: 450px;
    overflow-y: auto;
    padding: 10px 0;
}

.article-item {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.article-item:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.article-item.selected {
    border-color: #4a90e2;
    background: rgba(74, 144, 226, 0.1);
}

.article-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
}

.article-preview {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 60px;
}

@media (max-width: 768px) {
    .article-list {
        grid-template-columns: 1fr;
    }
}

/* 打字标题样式 */
.typing-title {
    font-size: 18px;
    color: #4a90e2;
    margin-bottom: 20px;
    text-align: center;
    font-weight: 500;
    padding: 10px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 打字问题界面样式 */
.question-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 600px;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 30px;
    z-index: 800;
    overflow: hidden;
}

.question-box {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.question-box h2 {
    font-size: 24px;
    color: #4a90e2;
    margin-bottom: 25px;
    width: 100%;
    position: relative;
    text-align: center;
}

.question-box h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #4a90e2, #67b8ff);
    border-radius: 3px;
}

.question-text {
    font-size: 20px;
    color: #333;
    margin-bottom: 30px;
    line-height: 1.5;
    max-width: 100%;
    background: rgba(255, 255, 255, 0.7);
    padding: 15px 25px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.question-hint {
    font-size: 16px;
    color: #666;
    margin-bottom: 20px;
    font-style: italic;
    background: rgba(255, 255, 255, 0.5);
    padding: 10px 15px;
    border-radius: 8px;
    border-left: 3px solid #4a90e2;
}

.question-input-container {
    width: 100%;
    margin-bottom: 30px;
}

#question-input {
    width: 100%;
    padding: 15px;
    font-size: 18px;
    border: 2px solid rgba(74, 144, 226, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    text-align: center;
    transition: all 0.3s ease;
}

#question-input:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
    transform: translateY(-2px);
}

.question-progress {
    width: 100%;
    margin-bottom: 25px;
}

.progress-text {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4a90e2, #67b8ff);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.question-actions {
    margin-top: 15px;
    display: flex;
    justify-content: center;
}

#skip-questions {
    background-color: #f0f0f0;
    color: #666;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#skip-questions:hover {
    background-color: #e0e0e0;
    color: #333;
}

/* 键盘按键提示样式 */
.key-highlight {
    display: inline-block;
    padding: 3px 8px;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    font-family: monospace;
    margin: 0 2px;
}

/* 问题结果反馈动画 */
@keyframes correct-answer {
    0% {background-color: rgba(40, 167, 69, 0.2);}
    50% {background-color: rgba(40, 167, 69, 0.4);}
    100% {background-color: rgba(40, 167, 69, 0.2);}
}

@keyframes wrong-answer {
    0% {background-color: rgba(220, 53, 69, 0.2);}
    50% {background-color: rgba(220, 53, 69, 0.4);}
    100% {background-color: rgba(220, 53, 69, 0.2);}
}

.answer-correct {
    animation: correct-answer 0.5s ease;
}

.answer-wrong {
    animation: wrong-answer 0.5s ease;
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
    .question-container {
        padding: 20px;
        min-height: 350px;
    }
    
    .question-box h2 {
        font-size: 20px;
    }
    
    .question-text {
        font-size: 18px;
        padding: 12px 18px;
    }
    
    #question-input {
        padding: 12px;
        font-size: 16px;
    }
}

/* 打字区域头部样式 */
.typing-area-header {
    padding: 10px;
    margin: 10px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* 文章选择样式 */
.article-select {
    width: 100%;
    height: 40px;
    padding: 8px 15px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    background-color: rgba(255, 255, 255, 0.8);
    color: #333;
    font-size: 15px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>');
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
    max-width: 100%;
}

.article-select:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}

/* 统计项目样式 */
.stat-item {
    text-align: center;
    padding: 3px 8px;
    border-radius: 6px;
    background-color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s;
    min-width: 80px;
}

.stat-label {
    font-size: 13px;
    color: #777;
    margin-bottom: 5px;
    white-space: nowrap;
}

.stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* 媒体查询已通过内联样式实现，以下仅供参考
@media screen and (max-width: 768px) {
    .typing-area-header {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 15px;
        justify-content: space-between;
    }
    
    .article-selection-controls {
        flex: 1 1 100%;
        margin-right: 0;
        margin-bottom: 10px;
        max-width: 100%;
    }
    
    .statistics-panel {
        width: auto;
        justify-content: space-between;
        flex-wrap: wrap;
        padding: 10px;
    }
    
    .stat-item {
        min-width: 70px;
        padding: 3px 5px;
    }
}
*/ 