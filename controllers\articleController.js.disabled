const mysql = require('mysql2');
const db = require('../config/db'); // 修改为正确的模块引入

/**
 * 获取所有文章
 * @param {Object} req - HTTP请求对象
 * @param {Object} res - HTTP响应对象
 */
exports.getAllArticles = async (req, res) => {
    try {
        const query = 'SELECT * FROM articles WHERE status = "active"';
        const results = await db.query(query);
        
        return res.status(200).json({
            success: true,
            articles: results
        });
    } catch (error) {
        console.error('获取文章列表失败:', error);
        return res.status(500).json({
            success: false,
            message: '获取文章列表失败',
            error: error.message
        });
    }
};

/**
 * 根据ID获取单个文章
 * @param {Object} req - HTTP请求对象
 * @param {Object} res - HTTP响应对象
 */
exports.getArticleById = async (req, res) => {
    try {
        const articleId = req.params.id;
        
        if (!articleId) {
            return res.status(400).json({
                success: false,
                message: '文章ID不能为空'
            });
        }
        
        const query = 'SELECT * FROM articles WHERE id = ? AND status = "active"';
        const results = await db.query(query, [articleId]);
        
        if (results.length === 0) {
            return res.status(404).json({
                success: false,
                message: '文章不存在'
            });
        }
        
        return res.status(200).json({
            success: true,
            article: results[0]
        });
    } catch (error) {
        console.error('获取文章失败:', error);
        return res.status(500).json({
            success: false,
            message: '获取文章失败',
            error: error.message
        });
    }
};

/**
 * 根据语言和年级获取文章
 * @param {Object} req - HTTP请求对象
 * @param {Object} res - HTTP响应对象
 */
exports.getArticlesByFilter = async (req, res) => {
    try {
        const { language, grade_level } = req.query;
        let query = 'SELECT * FROM articles WHERE status = "active"';
        const params = [];
        
        if (language) {
            query += ' AND language = ?';
            params.push(language);
        }
        
        if (grade_level) {
            query += ' AND grade_level = ?';
            params.push(grade_level);
        }
        
        const results = await db.query(query, params);
        
        return res.status(200).json({
            success: true,
            articles: results
        });
    } catch (error) {
        console.error('筛选文章失败:', error);
        return res.status(500).json({
            success: false,
            message: '筛选文章失败',
            error: error.message
        });
    }
};

/**
 * 创建新文章
 * @param {Object} req - HTTP请求对象
 * @param {Object} res - HTTP响应对象
 */
exports.createArticle = async (req, res) => {
    try {
        const { title, content, language, grade_level, difficulty } = req.body;
        
        if (!title || !content || !language) {
            return res.status(400).json({
                success: false,
                message: '标题、内容和语言不能为空'
            });
        }
        
        const query = 'INSERT INTO articles (title, content, language, grade_level, difficulty) VALUES (?, ?, ?, ?, ?)';
        const result = await db.execute(query, [title, content, language, grade_level || null, difficulty || 3]);
        
        return res.status(201).json({
            success: true,
            message: '文章创建成功',
            articleId: result.insertId
        });
    } catch (error) {
        console.error('创建文章失败:', error);
        return res.status(500).json({
            success: false,
            message: '创建文章失败',
            error: error.message
        });
    }
};

/**
 * 更新文章
 * @param {Object} req - HTTP请求对象
 * @param {Object} res - HTTP响应对象
 */
exports.updateArticle = async (req, res) => {
    try {
        const articleId = req.params.id;
        const { title, content, language, grade_level, difficulty, status } = req.body;
        
        if (!articleId) {
            return res.status(400).json({
                success: false,
                message: '文章ID不能为空'
            });
        }
        
        // 检查文章是否存在
        const checkQuery = 'SELECT * FROM articles WHERE id = ?';
        const articles = await db.query(checkQuery, [articleId]);
        
        if (articles.length === 0) {
            return res.status(404).json({
                success: false,
                message: '文章不存在'
            });
        }
        
        // 构建更新语句
        let updateQuery = 'UPDATE articles SET ';
        const params = [];
        const updates = [];
        
        if (title) {
            updates.push('title = ?');
            params.push(title);
        }
        
        if (content) {
            updates.push('content = ?');
            params.push(content);
        }
        
        if (language) {
            updates.push('language = ?');
            params.push(language);
        }
        
        if (grade_level !== undefined) {
            updates.push('grade_level = ?');
            params.push(grade_level);
        }
        
        if (difficulty !== undefined) {
            updates.push('difficulty = ?');
            params.push(difficulty);
        }
        
        if (status) {
            updates.push('status = ?');
            params.push(status);
        }
        
        if (updates.length === 0) {
            return res.status(400).json({
                success: false,
                message: '没有提供需要更新的字段'
            });
        }
        
        updateQuery += updates.join(', ') + ' WHERE id = ?';
        params.push(articleId);
        
        // 执行更新
        const result = await db.execute(updateQuery, params);
        
        return res.status(200).json({
            success: true,
            message: '文章更新成功',
            affectedRows: result.affectedRows
        });
    } catch (error) {
        console.error('更新文章失败:', error);
        return res.status(500).json({
            success: false,
            message: '更新文章失败',
            error: error.message
        });
    }
};

/**
 * 删除文章（逻辑删除，将状态改为inactive）
 * @param {Object} req - HTTP请求对象
 * @param {Object} res - HTTP响应对象
 */
exports.deleteArticle = async (req, res) => {
    try {
        const articleId = req.params.id;
        
        if (!articleId) {
            return res.status(400).json({
                success: false,
                message: '文章ID不能为空'
            });
        }
        
        const query = 'UPDATE articles SET status = "inactive" WHERE id = ?';
        const result = await db.execute(query, [articleId]);
        
        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: '文章不存在'
            });
        }
        
        return res.status(200).json({
            success: true,
            message: '文章删除成功'
        });
    } catch (error) {
        console.error('删除文章失败:', error);
        return res.status(500).json({
            success: false,
            message: '删除文章失败',
            error: error.message
        });
    }
};

/**
 * 获取随机文章
 * @param {Object} req - HTTP请求对象
 * @param {Object} res - HTTP响应对象
 */
exports.getRandomArticle = async (req, res) => {
    try {
        const { language, grade_level } = req.query;
        let query = 'SELECT * FROM articles WHERE status = "active"';
        const params = [];
        
        if (language) {
            query += ' AND language = ?';
            params.push(language);
        }
        
        if (grade_level) {
            query += ' AND grade_level = ?';
            params.push(grade_level);
        }
        
        query += ' ORDER BY RAND() LIMIT 1';
        
        const results = await db.query(query, params);
        
        if (results.length === 0) {
            return res.status(404).json({
                success: false,
                message: '未找到符合条件的文章'
            });
        }
        
        return res.status(200).json({
            success: true,
            article: results[0]
        });
    } catch (error) {
        console.error('获取随机文章失败:', error);
        return res.status(500).json({
            success: false,
            message: '获取随机文章失败',
            error: error.message
        });
    }
}; 