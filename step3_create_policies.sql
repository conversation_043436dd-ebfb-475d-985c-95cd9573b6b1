-- 🛡️ 第三步：创建安全策略
-- 在前两步完成后执行这个脚本

-- 学生表安全策略
CREATE POLICY "学生数据查看策略" ON students
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "学生数据插入策略" ON students
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "学生数据更新策略" ON students
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "学生数据删除策略" ON students
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

-- 奖章表安全策略
CREATE POLICY "奖章数据查看策略" ON medals
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "奖章数据修改策略" ON medals
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "奖章数据更新策略" ON medals
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "奖章数据删除策略" ON medals
    FOR DELETE USING (auth.role() = 'authenticated');

-- 打字记录表安全策略
CREATE POLICY "打字记录查看策略" ON typing_records
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "打字记录插入策略" ON typing_records
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "打字记录更新策略" ON typing_records
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "打字记录删除策略" ON typing_records
    FOR DELETE USING (auth.role() = 'authenticated');

-- 学校表安全策略
CREATE POLICY "学校数据查看策略" ON schools
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "学校数据插入策略" ON schools
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role IN ('admin', 'teacher')
        )
    );

CREATE POLICY "学校数据更新策略" ON schools
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role IN ('admin', 'teacher')
        )
    );

CREATE POLICY "学校数据删除策略" ON schools
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role IN ('admin', 'teacher')
        )
    );

-- 用户表安全策略
CREATE POLICY "用户查看自己信息策略" ON users
    FOR SELECT USING (id = auth.uid()::text::integer);

CREATE POLICY "管理员用户插入策略" ON users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "管理员用户更新策略" ON users
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "管理员用户删除策略" ON users
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

-- 教师权限表安全策略
CREATE POLICY "教师权限查看策略" ON teacher_class_permissions
    FOR SELECT USING (
        teacher_id = auth.uid()::text::integer OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'admin'
        )
    );

CREATE POLICY "教师权限插入策略" ON teacher_class_permissions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "教师权限更新策略" ON teacher_class_permissions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "教师权限删除策略" ON teacher_class_permissions
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

-- 文章表安全策略
CREATE POLICY "文章查看策略" ON articles
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "文章插入策略" ON articles
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "文章更新策略" ON articles
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "文章删除策略" ON articles
    FOR DELETE USING (auth.role() = 'authenticated');

SELECT 
    '🔒 安全配置已完成' as 状态,
    '所有安全策略已创建' as 说明,
    '请测试应用功能是否正常' as 下一步;
