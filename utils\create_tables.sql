-- 创建数据库
CREATE DATABASE IF NOT EXISTS syxxstudent;

-- 使用数据库
USE syxxstudent;

-- 创建学生表
CREATE TABLE IF NOT EXISTS students (
  id INT AUTO_INCREMENT PRIMARY KEY,
  student_identifier VARCHAR(100) NOT NULL UNIQUE,
  name VARCHAR(50) NOT NULL,
  grade VARCHAR(20) NOT NULL,
  class VARCHAR(20) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_students_name (name),
  INDEX idx_students_grade_class (grade, class)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建奖章表
CREATE TABLE IF NOT EXISTS medals (
  id INT AUTO_INCREMENT PRIMARY KEY,
  student_identifier VARCHAR(100) NOT NULL UNIQUE,
  count INT NOT NULL DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (student_identifier) REFERENCES students(student_identifier) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建打字记录表
CREATE TABLE IF NOT EXISTS typing_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  student_identifier VARCHAR(100) NOT NULL,
  speed INT NOT NULL,
  accuracy FLOAT DEFAULT 100,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (student_identifier) REFERENCES students(student_identifier) ON DELETE CASCADE,
  INDEX idx_typing_student (student_identifier),
  INDEX idx_typing_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建学生会话表
CREATE TABLE IF NOT EXISTS student_sessions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  student_identifier VARCHAR(100) NOT NULL,
  start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  end_time TIMESTAMP NULL,
  FOREIGN KEY (student_identifier) REFERENCES students(student_identifier) ON DELETE CASCADE,
  INDEX idx_session_student (student_identifier),
  INDEX idx_session_times (start_time, end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  role ENUM('admin', 'teacher', 'student') NOT NULL DEFAULT 'teacher',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_users_username (username)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建日志表
CREATE TABLE IF NOT EXISTS logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  action VARCHAR(50) NOT NULL,
  details TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_logs_action (action),
  INDEX idx_logs_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认管理员账号
INSERT INTO users (username, password, role)
VALUES ('admin', 'admin123', 'admin')
ON DUPLICATE KEY UPDATE
  password = VALUES(password),
  role = VALUES(role);

-- 插入默认教师账号
INSERT INTO users (username, password, role)
VALUES ('teacher', 'teacher123', 'teacher')
ON DUPLICATE KEY UPDATE
  password = VALUES(password),
  role = VALUES(role); 