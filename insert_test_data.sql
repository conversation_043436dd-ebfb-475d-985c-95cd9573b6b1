-- 插入测试数据脚本
-- 请在Supabase SQL Editor中执行此脚本

-- 1. 插入测试学校（如果不存在）
INSERT INTO schools (name, address, contact_phone) 
VALUES 
  ('测试小学', '测试地址123号', '010-12345678'),
  ('示例中学', '示例地址456号', '021-87654321')
ON CONFLICT (name) DO NOTHING;

-- 2. 插入测试学生数据
INSERT INTO students (student_identifier, name, grade, class, school_id) 
VALUES 
  ('TEST001', '张三', 3, 1, 1),
  ('TEST002', '李四', 3, 1, 1),
  ('TEST003', '王五', 3, 2, 1),
  ('TEST004', '赵六', 4, 1, 1),
  ('TEST005', '钱七', 4, 2, 1),
  ('TEST006', '孙八', 5, 1, 2),
  ('TEST007', '周九', 5, 2, 2),
  ('TEST008', '吴十', 6, 1, 2)
ON CONFLICT (student_identifier) DO NOTHING;

-- 3. 插入测试奖章数据
INSERT INTO medals (student_identifier, count) 
VALUES 
  ('TEST001', 5),
  ('TEST002', 3),
  ('TEST003', 8),
  ('TEST004', 2),
  ('TEST005', 6),
  ('TEST006', 4),
  ('TEST007', 7),
  ('TEST008', 1)
ON CONFLICT (student_identifier) DO NOTHING;

-- 4. 为teacher用户分配班级权限（假设teacher用户的ID是2）
-- 首先查看users表中teacher用户的ID
-- SELECT id, username FROM users WHERE username = 'teacher';

-- 根据实际的teacher用户ID插入权限（这里假设是ID=2）
INSERT INTO teacher_class_permissions (teacher_id, school_id, grade, class) 
VALUES 
  (2, 1, 3, 1),  -- 测试小学 3年级1班
  (2, 1, 3, 2),  -- 测试小学 3年级2班
  (2, 1, 4, 1),  -- 测试小学 4年级1班
  (2, 2, 5, 1)   -- 示例中学 5年级1班
ON CONFLICT (teacher_id, school_id, grade, class) DO NOTHING;

-- 5. 如果teacher用户ID不是2，请先查询正确的ID
-- 然后手动替换上面的teacher_id值

-- 6. 验证数据插入结果
SELECT 'Schools' as table_name, COUNT(*) as count FROM schools
UNION ALL
SELECT 'Students' as table_name, COUNT(*) as count FROM students
UNION ALL
SELECT 'Medals' as table_name, COUNT(*) as count FROM medals
UNION ALL
SELECT 'Teacher Permissions' as table_name, COUNT(*) as count FROM teacher_class_permissions;

-- 7. 查看teacher用户的权限分配
SELECT 
  u.username,
  tcp.school_id,
  s.name as school_name,
  tcp.grade,
  tcp.class
FROM teacher_class_permissions tcp
JOIN users u ON tcp.teacher_id = u.id
LEFT JOIN schools s ON tcp.school_id = s.id
WHERE u.username = 'teacher';

-- 8. 查看学生和奖章数据
SELECT 
  st.student_identifier,
  st.name,
  st.grade,
  st.class,
  sc.name as school_name,
  COALESCE(m.count, 0) as medal_count
FROM students st
LEFT JOIN schools sc ON st.school_id = sc.id
LEFT JOIN medals m ON st.student_identifier = m.student_identifier
ORDER BY st.grade, st.class, st.name;
