-- 数据库检查和权限设置脚本
-- 请在Supabase SQL Editor中执行此脚本来检查和修复权限问题

-- 1. 检查表是否存在
SELECT 
  table_name,
  table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN ('students', 'medals', 'schools', 'users', 'typing_records', 'typing_best', 'articles', 'logs', 'student_sessions')
ORDER BY table_name;

-- 2. 检查每个表的记录数
SELECT 'students' as table_name, COUNT(*) as record_count FROM students
UNION ALL
SELECT 'medals' as table_name, COUNT(*) as record_count FROM medals
UNION ALL
SELECT 'schools' as table_name, COUNT(*) as record_count FROM schools
UNION ALL
SELECT 'users' as table_name, COUNT(*) as record_count FROM users;

-- 3. 检查RLS状态
SELECT 
  schemaname,
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('students', 'medals', 'schools', 'users');

-- 4. 如果表为空，插入测试数据
-- 首先插入学校数据
INSERT INTO schools (name, address, contact_phone) 
VALUES 
  ('测试小学', '测试地址123号', '010-12345678')
ON CONFLICT (name) DO NOTHING;

-- 插入测试学生数据
INSERT INTO students (student_identifier, name, grade, class, school_id) 
VALUES 
  ('TEST001', '测试学生1', 3, 1, 1),
  ('TEST002', '测试学生2', 3, 1, 1),
  ('TEST003', '测试学生3', 3, 2, 1),
  ('TEST004', '测试学生4', 4, 1, 1),
  ('TEST005', '测试学生5', 4, 2, 1)
ON CONFLICT (student_identifier) DO NOTHING;

-- 插入测试奖章数据
INSERT INTO medals (student_identifier, count) 
VALUES 
  ('TEST001', 5),
  ('TEST002', 3),
  ('TEST003', 8),
  ('TEST004', 2),
  ('TEST005', 6)
ON CONFLICT (student_identifier) DO NOTHING;

-- 5. 临时禁用RLS以便调试（生产环境中应该启用）
ALTER TABLE students DISABLE ROW LEVEL SECURITY;
ALTER TABLE medals DISABLE ROW LEVEL SECURITY;
ALTER TABLE schools DISABLE ROW LEVEL SECURITY;
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- 6. 确保service_role可以访问所有表
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;

-- 7. 检查最终状态
SELECT 'Final Check - Students' as info, COUNT(*) as count FROM students
UNION ALL
SELECT 'Final Check - Medals' as info, COUNT(*) as count FROM medals
UNION ALL
SELECT 'Final Check - Schools' as info, COUNT(*) as count FROM schools;
