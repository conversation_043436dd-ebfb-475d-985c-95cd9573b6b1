/**
 * Supabase前端客户端配置
 * 用于实时数据订阅和前端数据操作
 */

// Supabase配置 - 需要从环境变量或配置中获取
const SUPABASE_CONFIG = {
    url: null,
    anonKey: null,
    initialized: false
};

// Supabase客户端实例
let supabaseClient = null;

// 实时订阅管理器
const RealtimeManager = {
    subscriptions: new Map(),
    
    /**
     * 初始化Supabase客户端
     */
    async init() {
        try {
            console.log('初始化Supabase前端客户端...');

            // 从服务器获取Supabase配置
            const configResponse = await fetch('/api/supabase-config');
            if (!configResponse.ok) {
                const errorText = await configResponse.text();
                console.error('获取Supabase配置失败:', {
                    status: configResponse.status,
                    statusText: configResponse.statusText,
                    error: errorText
                });
                throw new Error(`无法获取Supabase配置: ${configResponse.status} ${configResponse.statusText}`);
            }

            const config = await configResponse.json();
            console.log('获取到Supabase配置:', {
                url: config.url,
                anonKeyPrefix: config.anonKey ? config.anonKey.substring(0, 10) + '...' : 'undefined'
            });

            if (!config.url || !config.anonKey) {
                throw new Error('Supabase配置不完整');
            }

            SUPABASE_CONFIG.url = config.url;
            SUPABASE_CONFIG.anonKey = config.anonKey;

            // 检查Supabase库是否已加载
            if (typeof window.supabase === 'undefined') {
                console.log('动态加载Supabase客户端库...');
                // 动态加载Supabase客户端库
                await this.loadSupabaseLibrary();
            }

            // 创建Supabase客户端实例
            console.log('创建Supabase客户端实例...');
            supabaseClient = window.supabase.createClient(
                SUPABASE_CONFIG.url,
                SUPABASE_CONFIG.anonKey,
                {
                    realtime: {
                        params: {
                            eventsPerSecond: 10
                        }
                    }
                }
            );

            // 测试连接
            console.log('测试Supabase连接...');
            const { data, error } = await supabaseClient
                .from('schools')
                .select('count', { count: 'exact', head: true });

            if (error) {
                console.error('Supabase连接测试失败:', error);
                throw new Error(`Supabase连接失败: ${error.message}`);
            }

            SUPABASE_CONFIG.initialized = true;
            console.log('Supabase前端客户端初始化成功');

            return true;
        } catch (error) {
            console.error('Supabase前端客户端初始化失败:', error);
            SUPABASE_CONFIG.initialized = false;
            return false;
        }
    },
    
    /**
     * 动态加载Supabase客户端库
     */
    async loadSupabaseLibrary() {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.39.0/dist/umd/supabase.min.js';
            script.onload = () => {
                console.log('Supabase客户端库加载成功');
                resolve();
            };
            script.onerror = () => {
                console.warn('Supabase CDN加载失败，实时功能将不可用');
                reject(new Error('无法加载Supabase客户端库'));
            };
            document.head.appendChild(script);
        });
    },
    
    /**
     * 订阅表的实时变更
     * @param {string} table - 表名
     * @param {Function} callback - 变更回调函数
     * @param {Object} options - 订阅选项
     */
    subscribe(table, callback, options = {}) {
        if (!SUPABASE_CONFIG.initialized || !supabaseClient) {
            console.error('Supabase客户端未初始化');
            return null;
        }
        
        console.log(`订阅表 ${table} 的实时变更...`);
        
        // 创建订阅
        const subscription = supabaseClient
            .channel(`${table}_changes`)
            .on(
                'postgres_changes',
                {
                    event: '*', // 监听所有事件 (INSERT, UPDATE, DELETE)
                    schema: 'public',
                    table: table,
                    ...options.filter
                },
                (payload) => {
                    console.log(`表 ${table} 发生变更:`, payload);
                    callback(payload);
                }
            )
            .subscribe();
        
        // 保存订阅引用
        this.subscriptions.set(`${table}_subscription`, subscription);
        
        return subscription;
    },
    
    /**
     * 取消订阅
     * @param {string} table - 表名
     */
    unsubscribe(table) {
        const subscriptionKey = `${table}_subscription`;
        const subscription = this.subscriptions.get(subscriptionKey);
        
        if (subscription) {
            console.log(`取消订阅表 ${table}`);
            supabaseClient.removeChannel(subscription);
            this.subscriptions.delete(subscriptionKey);
        }
    },
    
    /**
     * 取消所有订阅
     */
    unsubscribeAll() {
        console.log('取消所有实时订阅...');
        this.subscriptions.forEach((subscription, key) => {
            supabaseClient.removeChannel(subscription);
        });
        this.subscriptions.clear();
    }
};

// 学校数据实时同步管理器
const SchoolRealtimeSync = {
    isActive: false,
    
    /**
     * 启动学校数据实时同步
     */
    async start() {
        if (this.isActive) {
            console.log('学校实时同步已经启动');
            return true;
        }

        console.log('启动学校数据实时同步...');

        try {
            // 确保Supabase客户端已初始化
            if (!SUPABASE_CONFIG.initialized) {
                console.log('Supabase客户端未初始化，正在初始化...');
                const initialized = await RealtimeManager.init();
                if (!initialized) {
                    console.error('无法启动实时同步：Supabase客户端初始化失败');
                    return false;
                }
            }

            // 订阅schools表的变更
            const subscription = RealtimeManager.subscribe('schools', (payload) => {
                this.handleSchoolChange(payload);
            });

            if (!subscription) {
                console.error('创建实时订阅失败');
                return false;
            }

            this.isActive = true;
            console.log('学校数据实时同步已启动');
            return true;
        } catch (error) {
            console.error('启动学校数据实时同步失败:', error);
            this.isActive = false;
            return false;
        }
    },
    
    /**
     * 停止学校数据实时同步
     */
    stop() {
        if (!this.isActive) {
            return;
        }
        
        console.log('停止学校数据实时同步...');
        RealtimeManager.unsubscribe('schools');
        this.isActive = false;
    },
    
    /**
     * 处理学校数据变更
     * @param {Object} payload - 变更数据
     */
    handleSchoolChange(payload) {
        const { eventType, new: newRecord, old: oldRecord } = payload;
        
        console.log('学校数据变更:', { eventType, newRecord, oldRecord });
        
        switch (eventType) {
            case 'INSERT':
                this.handleSchoolInsert(newRecord);
                break;
            case 'UPDATE':
                this.handleSchoolUpdate(newRecord, oldRecord);
                break;
            case 'DELETE':
                this.handleSchoolDelete(oldRecord);
                break;
        }
    },
    
    /**
     * 处理学校新增
     * @param {Object} school - 新增的学校数据
     */
    handleSchoolInsert(school) {
        console.log('检测到新学校添加:', school);
        
        // 更新本地学校数据
        if (typeof teacherSchools !== 'undefined' && Array.isArray(teacherSchools)) {
            // 检查是否已存在（避免重复添加）
            const exists = teacherSchools.find(s => s.id === school.id);
            if (!exists) {
                teacherSchools.push(school);
                console.log('本地学校数据已更新');
                
                // 更新界面显示
                if (typeof updateSchoolManagementDisplay === 'function') {
                    updateSchoolManagementDisplay();
                }
                
                // 显示通知
                if (typeof showMessage === 'function') {
                    showMessage(`新学校"${school.name}"已添加`, 'success');
                }
            }
        }
    },
    
    /**
     * 处理学校更新
     * @param {Object} newSchool - 更新后的学校数据
     * @param {Object} oldSchool - 更新前的学校数据
     */
    handleSchoolUpdate(newSchool, oldSchool) {
        console.log('检测到学校更新:', { newSchool, oldSchool });
        
        // 更新本地学校数据
        if (typeof teacherSchools !== 'undefined' && Array.isArray(teacherSchools)) {
            const index = teacherSchools.findIndex(s => s.id === newSchool.id);
            if (index !== -1) {
                teacherSchools[index] = newSchool;
                console.log('本地学校数据已更新');
                
                // 更新界面显示
                if (typeof updateSchoolManagementDisplay === 'function') {
                    updateSchoolManagementDisplay();
                }
                
                // 显示通知
                if (typeof showMessage === 'function') {
                    showMessage(`学校"${newSchool.name}"信息已更新`, 'info');
                }
            }
        }
    },
    
    /**
     * 处理学校删除
     * @param {Object} school - 被删除的学校数据
     */
    handleSchoolDelete(school) {
        console.log('检测到学校删除:', school);
        
        // 更新本地学校数据
        if (typeof teacherSchools !== 'undefined' && Array.isArray(teacherSchools)) {
            const index = teacherSchools.findIndex(s => s.id === school.id);
            if (index !== -1) {
                teacherSchools.splice(index, 1);
                console.log('本地学校数据已更新');
                
                // 更新界面显示
                if (typeof updateSchoolManagementDisplay === 'function') {
                    updateSchoolManagementDisplay();
                }
                
                // 显示通知
                if (typeof showMessage === 'function') {
                    showMessage(`学校"${school.name}"已被删除`, 'warning');
                }
            }
        }
    }
};

// 导出到全局作用域
window.RealtimeManager = RealtimeManager;
window.SchoolRealtimeSync = SchoolRealtimeSync;
window.supabaseClient = () => supabaseClient;

console.log('Supabase实时客户端模块已加载');
