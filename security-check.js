#!/usr/bin/env node

/**
 * 安全配置检查脚本
 * 检查系统的安全配置是否符合最佳实践
 */

const fs = require('fs');
const path = require('path');

console.log('🔒 开始安全配置检查...\n');

let securityScore = 100;
const issues = [];
const warnings = [];

// 检查环境变量
console.log('📋 检查环境变量配置...');

// 检查JWT_SECRET
const jwtSecret = process.env.JWT_SECRET;
if (!jwtSecret) {
    issues.push('JWT_SECRET环境变量未设置');
    securityScore -= 30;
} else if (jwtSecret.length < 32) {
    warnings.push('JWT_SECRET长度不足32字符，建议使用更长的密钥');
    securityScore -= 10;
} else if (jwtSecret === 'fallback-secret-key-change-in-production') {
    issues.push('JWT_SECRET使用默认值，存在严重安全风险');
    securityScore -= 25;
} else {
    console.log('✅ JWT_SECRET配置正确');
}

// 检查Supabase配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl) {
    issues.push('SUPABASE_URL环境变量未设置');
    securityScore -= 20;
} else {
    console.log('✅ SUPABASE_URL已设置');
}

if (!supabaseKey) {
    issues.push('SUPABASE_SERVICE_ROLE_KEY环境变量未设置');
    securityScore -= 20;
} else if (supabaseKey.length < 100) {
    warnings.push('SUPABASE_SERVICE_ROLE_KEY长度异常');
    securityScore -= 5;
} else {
    console.log('✅ SUPABASE_SERVICE_ROLE_KEY已设置');
}

// 检查文件权限和配置
console.log('\n📁 检查文件安全配置...');

// 检查.env文件是否存在且不在版本控制中
if (fs.existsSync('.env')) {
    console.log('✅ .env文件存在');
    
    // 检查.gitignore
    if (fs.existsSync('.gitignore')) {
        const gitignore = fs.readFileSync('.gitignore', 'utf8');
        if (!gitignore.includes('.env')) {
            warnings.push('.env文件可能未被.gitignore忽略');
            securityScore -= 5;
        } else {
            console.log('✅ .env文件已被.gitignore忽略');
        }
    } else {
        warnings.push('.gitignore文件不存在');
        securityScore -= 5;
    }
} else {
    warnings.push('.env文件不存在，请确保环境变量已正确设置');
    securityScore -= 5;
}

// 检查配置文件中的硬编码凭据
console.log('\n🔍 检查硬编码凭据...');

const configFiles = [
    'public/js/config.js',
    'database_init.sql',
    'database_fix_simple.sql'
];

configFiles.forEach(file => {
    if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        
        // 检查常见的不安全模式
        const unsafePatterns = [
            /password.*[:=]\s*['"`][^'"`]{1,20}['"`]/gi,
            /admin123/gi,
            /password123/gi,
            /fallback-secret-key/gi
        ];
        
        unsafePatterns.forEach(pattern => {
            if (pattern.test(content)) {
                warnings.push(`${file} 可能包含硬编码凭据`);
                securityScore -= 5;
            }
        });
    }
});

// 生成安全报告
console.log('\n📊 安全评估报告');
console.log('='.repeat(50));

if (issues.length === 0 && warnings.length === 0) {
    console.log('🎉 恭喜！未发现安全问题');
} else {
    if (issues.length > 0) {
        console.log('\n❌ 严重安全问题:');
        issues.forEach((issue, index) => {
            console.log(`   ${index + 1}. ${issue}`);
        });
    }
    
    if (warnings.length > 0) {
        console.log('\n⚠️  安全警告:');
        warnings.forEach((warning, index) => {
            console.log(`   ${index + 1}. ${warning}`);
        });
    }
}

console.log(`\n🏆 安全评分: ${securityScore}/100`);

if (securityScore >= 90) {
    console.log('✅ 安全配置优秀');
} else if (securityScore >= 70) {
    console.log('⚠️  安全配置良好，但有改进空间');
} else if (securityScore >= 50) {
    console.log('❌ 安全配置需要改进');
} else {
    console.log('🚨 安全配置存在严重问题，请立即修复');
}

console.log('\n📖 安全建议:');
console.log('1. 使用强密码和随机生成的JWT密钥');
console.log('2. 定期更新密码和密钥');
console.log('3. 不要在代码中硬编码敏感信息');
console.log('4. 使用环境变量管理配置');
console.log('5. 定期进行安全审计');

console.log('\n🔒 安全检查完成');

// 如果安全评分过低，退出时返回错误代码
process.exit(securityScore < 70 ? 1 : 0);
