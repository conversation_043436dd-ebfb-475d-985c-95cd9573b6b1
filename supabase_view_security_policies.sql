-- 🔒 Supabase 视图安全策略配置脚本
-- 解决 teacher_permissions_view、teacher_visible_students、student_best_typing_records 的安全策略问题

-- ==========================================
-- 重要说明
-- ==========================================
-- 在 Supabase 中，视图的安全性是通过底层表的 RLS 策略来控制的
-- 视图本身不能直接设置 RLS 策略
-- 我们需要确保底层表的安全策略已正确配置

-- ==========================================
-- 第一步：检查底层表的 RLS 状态
-- ==========================================

-- 检查相关表是否已启用 RLS
SELECT
    schemaname,
    tablename,
    rowsecurity as "RLS启用状态"
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN ('students', 'teacher_class_permissions', 'typing_records', 'schools', 'users');

-- ==========================================
-- 第二步：确保底层表已启用 RLS
-- ==========================================

-- 为相关表启用 RLS（如果尚未启用）
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_class_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE typing_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE schools ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- ==========================================
-- 第三步：创建或更新底层表的安全策略
-- ==========================================

-- 删除可能存在的旧策略，避免冲突
DROP POLICY IF EXISTS "教师班级权限查看策略" ON teacher_class_permissions;
DROP POLICY IF EXISTS "教师班级权限插入策略" ON teacher_class_permissions;
DROP POLICY IF EXISTS "教师班级权限更新策略" ON teacher_class_permissions;
DROP POLICY IF EXISTS "教师班级权限删除策略" ON teacher_class_permissions;

-- 为 teacher_class_permissions 表创建安全策略
CREATE POLICY "教师班级权限查看策略" ON teacher_class_permissions
    FOR SELECT USING (
        auth.role() = 'authenticated' AND (
            -- 管理员可以查看所有权限
            EXISTS (
                SELECT 1 FROM users
                WHERE users.id = auth.uid()::text::integer
                AND users.role = 'admin'
            )
            OR
            -- 教师只能查看自己的权限
            teacher_id = auth.uid()::text::integer
        )
    );

CREATE POLICY "教师班级权限插入策略" ON teacher_class_permissions
    FOR INSERT WITH CHECK (
        -- 只允许管理员插入教师权限
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "教师班级权限更新策略" ON teacher_class_permissions
    FOR UPDATE USING (
        -- 只允许管理员更新教师权限
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "教师班级权限删除策略" ON teacher_class_permissions
    FOR DELETE USING (
        -- 只允许管理员删除教师权限
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

-- ==========================================
-- 第四步：验证安全策略设置
-- ==========================================

-- 检查相关表的 RLS 状态
SELECT
    schemaname,
    tablename,
    rowsecurity as "RLS启用状态"
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN ('students', 'teacher_class_permissions', 'typing_records', 'schools', 'users');

-- 检查相关表的安全策略
SELECT
    schemaname,
    tablename,
    policyname,
    cmd as "操作类型"
FROM pg_policies
WHERE schemaname = 'public'
AND tablename IN ('students', 'teacher_class_permissions', 'typing_records', 'schools', 'users')
ORDER BY tablename, policyname;

-- 检查视图是否存在
SELECT
    schemaname,
    viewname,
    definition
FROM pg_views
WHERE schemaname = 'public'
AND viewname IN ('teacher_permissions_view', 'teacher_visible_students', 'student_best_typing_records');

-- ==========================================
-- 第五步：测试查询（可选）
-- ==========================================

-- 测试教师权限视图访问（需要认证）
-- SELECT COUNT(*) FROM teacher_permissions_view;

-- 测试教师可见学生视图访问（需要认证）
-- SELECT COUNT(*) FROM teacher_visible_students;

-- 测试学生最佳打字记录视图访问（需要认证）
-- SELECT COUNT(*) FROM student_best_typing_records;

-- ==========================================
-- 完成提示
-- ==========================================

SELECT
    '🔒 底层表安全策略配置已完成' as 状态,
    '视图将通过底层表的 RLS 策略进行安全控制' as 详情,
    '请在 Supabase 控制台测试应用功能' as 下一步,
    '如果仍有问题，请检查视图定义和底层表策略' as 备注;
