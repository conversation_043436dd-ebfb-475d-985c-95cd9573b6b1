/**
 * db.js - 数据库服务模块
 * 负责与后端API交互
 * 版本: 2.1 (修复process引用错误)
 */

const DB = {
    // API基础URL - 不需要后缀的斜杠
    baseUrl: '/api',
    
    // 是否已初始化
    initialized: false,
    
    // 初始化状态
    status: {
        connected: false,
        error: null
    },
    
    /**
     * 初始化数据库服务
     */
    init: function() {
        // 检查是否从文件系统直接访问
        if (window.location.protocol === 'file:') {
            this.status.error = '检测到从文件系统直接访问。API请求需要通过HTTP服务器运行。';
            console.error(this.status.error);
            return;
        }
        
        // 尝试连接API
        this.testConnection()
            .then(() => {
                this.status.connected = true;
            })
            .catch(error => {
                this.status.error = `API连接失败: ${error.message}`;
                console.error(this.status.error);
            });
        
        this.initialized = true;
    },
    
    /**
     * 检查数据库服务是否已准备好
     * @returns {boolean} 是否已准备好
     */
    isReady: function() {
        return this.initialized;
    },
    
    /**
     * 确保数据库服务已初始化
     * @returns {Promise} 返回初始化完成的Promise
     */
    ensureInitialized: async function() {
        if (!this.initialized) {
            await this.init();
        }
        return Promise.resolve();
    },
    
    /**
     * 测试API连接
     * @returns {Promise} 连接测试结果
     */
    testConnection: async function() {
        try {
            // 使用简单的GET请求测试连接
            const response = await fetch(`${this.baseUrl}/test`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                },
                credentials: 'same-origin'
            });
            
            // 即使返回404也认为API可用，因为意味着服务器在运行
            if (response.status === 404) {
                return true;
            }
            
            const result = await response.json();
            return true;
        } catch (error) {
            throw new Error(`无法连接到API服务器: ${error.message}`);
        }
    },
    
    /**
     * 发送API请求
     * @param {string} method - HTTP方法：GET, POST, PUT, DELETE
     * @param {string} endpoint - API端点
     * @param {Object} data - 请求数据
     * @returns {Promise} 返回响应数据的Promise
     */
    request: async function(method, endpoint, data = null) {
        // 检查初始化状态
        if (!this.initialized) {
            await this.init();
        }
        
        // 如果有连接错误，立即抛出
        if (this.status.error) {
            throw new Error(this.status.error);
        }
        
        // 构建API URL
        let url = `${this.baseUrl}/${endpoint}`;
        
        // 对于GET请求，将参数添加到URL中
        if (method === 'GET' && data) {
            const params = new URLSearchParams();
            for (const key in data) {
                if (data[key] !== undefined && data[key] !== null) {
                    params.append(key, data[key]);
                }
            }
            if (params.toString()) {
                url += `?${params.toString()}`;
            }
        }
        

        
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            // 添加凭证，确保cookie能够被发送和接收
            credentials: 'same-origin'
        };

        // 添加JWT认证头
        const token = localStorage.getItem('token');
        if (token) {
            options.headers['Authorization'] = `Bearer ${token}`;
        }
        
        if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {
            options.body = JSON.stringify(data);
        }
        
        try {
            const response = await fetch(url, options);
            
            // 对于204 No Content响应，不尝试解析JSON
            if (response.status === 204) {
                return { success: true };
            }
            
            // 获取响应文本
            const responseText = await response.text();
            
            // 尝试解析JSON
            let result;
            try {
                // 只有在响应文本非空时才尝试解析JSON
                if (responseText.trim()) {
                    result = JSON.parse(responseText);
                } else {
                    // 空响应但状态码正常，视为成功
                    if (response.ok) {
                        return { success: true };
                    }
                    result = {};
                }
            } catch (parseError) {
                console.error('JSON解析错误:', parseError);
                // 如果响应状态码是成功的，即使解析失败也视为成功
                if (response.ok) {
                    console.log('响应状态码正常，尽管JSON解析失败，仍视为成功');
                    return { success: true, responseText };
                }
                throw new Error(`解析响应失败: ${parseError.message}, 状态码: ${response.status}, 响应内容: ${responseText.substring(0, 100)}`);
            }
            
            if (response.ok) {
                return result.data || result;
            } else {
                // 处理不同格式的错误响应
                let errorMsg = `请求失败，状态码: ${response.status}`;

                if (result.error) {
                    if (typeof result.error === 'string') {
                        errorMsg = result.error;
                    } else if (result.error.message) {
                        errorMsg = result.error.message;
                    } else if (typeof result.error === 'object') {
                        errorMsg = JSON.stringify(result.error);
                    }
                } else if (result.message) {
                    errorMsg = result.message;
                }

                console.error('处理后的错误消息:', errorMsg);
                throw new Error(errorMsg);
            }
        } catch (error) {
            console.error('=== DB.js API请求错误调试 ===');
            console.error('原始错误:', error);
            console.error('错误类型:', typeof error);
            console.error('错误消息:', error?.message);

            // 提供更友好的错误消息
            if (error.message && error.message.includes('Failed to fetch')) {
                const networkError = new Error('无法连接到API服务器。请确保服务器正在运行，且不是通过文件系统直接访问。');
                console.error('抛出网络错误:', networkError);
                throw networkError;
            }

            console.error('重新抛出原始错误:', error);
            throw error;
        }
    },
    
    /**
     * 用户登录
     * @param {string} username - 用户名
     * @param {string} password - 密码
     * @returns {Promise} 返回登录结果的Promise
     */
    login: async function(username, password) {
        return this.request('POST', 'login', { username, password });
    },

    /**
     * 教师注册
     * @param {Object} userData - 用户注册数据
     * @returns {Promise} 返回注册结果的Promise
     */
    register: async function(userData) {
        return this.request('POST', 'register', userData);
    },
    
    /**
     * 查找学生
     * @param {Object} filters - 筛选条件
     * @returns {Promise} 返回学生列表的Promise
     */
    findStudent: async function(filters = {}) {
        return this.request('GET', 'students', filters);
    },
    
    /**
     * 获取单个学生
     * @param {string} studentId - 学生ID
     * @returns {Promise} 返回学生信息的Promise
     */
    getStudent: async function(studentId) {
        return this.request('GET', `students/${studentId}`);
    },
    
    /**
     * 创建学生
     * @param {Object} studentData - 学生数据
     * @returns {Promise} 返回创建结果的Promise
     */
    createStudent: async function(studentData) {
        return this.request('POST', 'students', studentData);
    },
    
    /**
     * 更新学生
     * @param {string} studentId - 学生ID
     * @param {Object} studentData - 更新数据
     * @returns {Promise} 返回更新结果的Promise
     */
    updateStudent: async function(studentId, studentData) {
        return this.request('PUT', `students/${studentId}`, studentData);
    },
    
    /**
     * 删除学生
     * @param {string} studentId - 学生ID
     * @returns {Promise} 返回删除结果的Promise
     */
    deleteStudent: async function(studentId) {
        return this.request('DELETE', `students/${studentId}`);
    },
    
    /**
     * 获取学生打字记录
     * @param {Object} filters - 筛选条件
     * @returns {Promise} 返回打字记录列表的Promise
     */
    getTypingHistory: async function(filters = {}) {
        return this.request('GET', 'typing-records', filters);
    },
    
    /**
     * 获取最佳打字记录
     * @param {Object} filters - 筛选条件 (school, grade, class, search)
     * @returns {Promise} 返回最佳打字记录列表的Promise
     */
    getBestTypingRecords: async function(filters = {}) {
        console.log('请求打字记录数据，筛选条件:', filters);

        // 确保筛选条件格式正确
        const cleanFilters = {};
        if (filters.school) cleanFilters.school = filters.school;
        if (filters.grade) cleanFilters.grade = parseInt(filters.grade);
        if (filters.class) cleanFilters.class = parseInt(filters.class);
        if (filters.search) cleanFilters.search = filters.search;

        console.log('清理后的筛选条件:', cleanFilters);
        console.time('获取最佳打字记录');

        try {
            const result = await this.request('GET', 'best-typing-records', cleanFilters);
            console.timeEnd('获取最佳打字记录');
            // 数据保护：只输出统计信息，不输出敏感数据
            console.log('获取打字记录成功，记录数:', result?.length || 0);
            return result;
        } catch (error) {
            console.timeEnd('获取最佳打字记录');
            console.error('获取打字记录失败，详细错误:', error);

            // 如果是格式错误，返回空数组而不是抛出错误
            if (error.message && error.message.includes('invalid input syntax')) {
                console.warn('数据格式错误，返回空数组');
                return [];
            }
            
            // 如果API不可用，尝试使用模拟数据
            if (typeof CONFIG !== 'undefined' && CONFIG.DEMO_DATA && CONFIG.DEMO_DATA.TYPING) {
                console.log('使用模拟数据作为备选');
                // 将模拟数据转换为API响应格式
                return CONFIG.DEMO_DATA.TYPING.map(item => ({
                    student_identifier: item.id,
                    name: item.name,
                    grade: item.grade,
                    class: item.class,
                    best_speed: item.speed,
                    best_accuracy: 100
                }));
            }
            
            throw error;
        }
    },
    
    /**
     * 添加打字记录
     * @param {Object} recordData - 打字记录数据
     * @returns {Promise} 返回添加结果的Promise
     */
    addTypingRecord: async function(recordData) {
        return this.request('POST', 'typing-records', recordData);
    },
    
    /**
     * 获取学生的打字记录
     * @param {Object} filters - 筛选条件，如student_identifier
     * @returns {Promise} 返回打字记录列表的Promise
     */
    getTypingRecords: async function(filters = {}) {
        try {
            return await this.request('GET', 'typing-records', filters);
        } catch (error) {
            console.error('获取打字记录失败:', error);
            // 如果API请求失败，返回空数组而不是抛出错误，以便更优雅地降级
            return [];
        }
    },

    /**
     * 批量获取多个学生的打字记录 - 优化版本
     * @param {Array} studentIdentifiers - 学生标识符数组
     * @returns {Promise} 返回按学生分组的打字记录
     */
    getBatchTypingRecords: async function(studentIdentifiers = []) {
        if (!studentIdentifiers.length) {
            return {};
        }

        try {
            console.time('批量获取打字记录');
            console.log(`批量获取${studentIdentifiers.length}个学生的打字记录`);

            const result = await this.request('GET', 'batch-typing-records', {
                student_identifiers: studentIdentifiers.join(',')
            });

            console.timeEnd('批量获取打字记录');
            console.log('批量获取打字记录成功，学生数:', result?.students_count || 0, '总记录数:', result?.total_records || 0);

            return result?.data || {};
        } catch (error) {
            console.timeEnd('批量获取打字记录');
            console.error('批量获取打字记录失败:', error);
            return {};
        }
    },
    
    /**
     * 获取奖章信息
     * @param {Object} filters - 筛选条件
     * @returns {Promise} 返回奖章信息的Promise
     */
    getMedals: async function(filters = {}) {
        console.log('请求奖章数据，筛选条件:', filters);

        // 确保筛选条件格式正确
        const cleanFilters = {};
        if (filters.school_id) cleanFilters.school_id = filters.school_id;
        if (filters.grade) cleanFilters.grade = parseInt(filters.grade);
        if (filters.class) cleanFilters.class = parseInt(filters.class);
        if (filters.search) cleanFilters.search = filters.search;

        console.log('清理后的筛选条件:', cleanFilters);

        try {
            const result = await this.request('GET', 'medals', cleanFilters);
            // 数据保护：只输出统计信息，不输出敏感数据
            console.log('获取奖章数据成功，记录数:', result?.length || 0);
            return result;
        } catch (error) {
            console.error('获取奖章数据失败，详细错误:', error);
            // 如果是格式错误，返回空数组而不是抛出错误
            if (error.message && error.message.includes('invalid input syntax')) {
                console.warn('数据格式错误，返回空数组');
                return [];
            }
            throw error;
        }
    },
    
    /**
     * 更新奖章数量
     * @param {string} studentId - 学生ID
     * @param {number} count - 奖章数量
     * @returns {Promise} 返回更新结果的Promise
     */
    updateMedals: async function(studentId, count) {
        console.log(`更新奖章 - 学生ID: ${studentId}, 数量: ${count}`);
        try {
            const result = await this.request('POST', 'medals', { 
                student_id: studentId,
                count: count
            });
            console.log('奖章更新成功:', result);
            return result;
        } catch (error) {
            console.error('奖章更新请求失败:', error);
            
            // 检查是否是网络错误但数据可能已更新
            if (error.message.includes('解析响应失败') || error.message.includes('无法连接')) {
                console.log('数据可能已更新，但无法确认。请刷新页面查看最新数据。');
                // 将错误转换为更友好的消息，但仍然抛出以便调用者知道有问题
                error.isHandled = true;
            }
            throw error;
        }
    },

    /**
     * 批量更新奖章数量
     * @param {Array} studentIds - 学生ID数组
     * @param {number} increment - 增量值（正数为增加，负数为减少）
     * @returns {Promise} 返回批量更新结果的Promise
     */
    batchUpdateMedals: async function(studentIds, increment) {
        console.log(`批量更新奖章 - 学生数量: ${studentIds.length}, 增量: ${increment}`);
        try {
            const result = await this.request('POST', 'batch-update-medals', {
                student_identifiers: studentIds,
                increment: increment
            });
            console.log('批量奖章更新成功:', result);
            return result;
        } catch (error) {
            console.error('批量奖章更新请求失败:', error);
            throw error;
        }
    },

    /**
     * 获取打字数据统计
     * @param {Object} filters - 筛选条件
     * @returns {Promise} 返回打字数据统计的Promise
     */
    getTypingData: async function(filters = {}) {
        const records = await this.getTypingHistory(filters);
        const students = await this.findStudent(filters);
        const medals = await this.getMedals(filters);
        
        // 数据聚合
        const result = {
            records: records,
            students: students,
            medals: medals,
            stats: this._calculateStats(records)
        };
        
        return result;
    },
    
    /**
     * 计算打字统计数据
     * @private
     * @param {Array} records - 打字记录列表
     * @returns {Object} 统计数据
     */
    _calculateStats: function(records) {
        if (!records || records.length === 0) {
            return {
                avgSpeed: 0,
                avgAccuracy: 0,
                maxSpeed: 0,
                maxAccuracy: 0
            };
        }
        
        let totalSpeed = 0;
        let totalAccuracy = 0;
        let maxSpeed = 0;
        let maxAccuracy = 0;
        
        records.forEach(record => {
            totalSpeed += record.speed;
            totalAccuracy += record.accuracy;
            
            if (record.speed > maxSpeed) {
                maxSpeed = record.speed;
            }
            
            if (record.accuracy > maxAccuracy) {
                maxAccuracy = record.accuracy;
            }
        });
        
        return {
            avgSpeed: Math.round(totalSpeed / records.length),
            avgAccuracy: parseFloat((totalAccuracy / records.length).toFixed(2)),
            maxSpeed: maxSpeed,
            maxAccuracy: maxAccuracy
        };
    },
    
    /**
     * 创建学生会话
     * @param {string} studentId - 学生ID
     * @param {string} seat - 座位号
     * @returns {Promise} 返回创建结果的Promise
     */
    createStudentSession: async function(studentId, seat) {
        return this.request('POST', 'sessions/start', {
            student_id: studentId,
            seat: seat
        });
    },
    
    /**
     * 结束学生会话
     * @param {string} studentId - 学生ID
     * @returns {Promise} 返回结束结果的Promise
     */
    endStudentSession: async function(studentId) {
        return this.request('POST', 'sessions/end', {
            student_identifier: studentId
        });
    },
    
    /**
     * 记录操作日志
     * @param {string} action - 操作类型
     * @param {Object} data - 操作数据
     * @returns {Promise} 返回记录结果的Promise
     */
    logAction: async function(action, data = {}) {
        const logData = {
            action: action,
            ...data
        };
        
        return this.request('POST', 'logs', logData);
    },
    
    /**
     * 获取操作日志
     * @param {Object} filters - 筛选条件
     * @returns {Promise} 返回日志列表的Promise
     */
    getLogs: async function(filters = {}) {
        return this.request('GET', 'logs', filters);
    },

    /**
     * 获取所有学校信息
     * @returns {Promise} 返回学校列表的Promise
     */
    getSchools: async function() {
        try {
            return await this.request('GET', 'schools');
        } catch (error) {
            console.error('获取学校信息失败:', error);
            return [];
        }
    },

    /**
     * 根据学校获取学生信息
     * @param {string} schoolId - 学校ID
     * @returns {Promise} 返回学生列表的Promise
     */
    getStudentsBySchool: async function(schoolId) {
        try {
            return await this.request('GET', 'students', { school_id: schoolId });
        } catch (error) {
            console.error('获取学校学生信息失败:', error);
            return [];
        }
    },

    /**
     * 根据学校和年级获取学生信息
     * @param {string} schoolId - 学校ID
     * @param {string} grade - 年级
     * @returns {Promise} 返回学生列表的Promise
     */
    getStudentsBySchoolAndGrade: async function(schoolId, grade) {
        try {
            return await this.request('GET', 'students', {
                school_id: schoolId,
                grade: grade
            });
        } catch (error) {
            console.error('获取学校年级学生信息失败:', error);
            return [];
        }
    },

    /**
     * 根据学校、年级、班级获取学生信息
     * @param {string} schoolId - 学校ID
     * @param {string} grade - 年级
     * @param {string} classNum - 班级
     * @returns {Promise} 返回学生列表的Promise
     */
    getStudentsBySchoolGradeClass: async function(schoolId, grade, classNum) {
        try {
            return await this.request('GET', 'students', {
                school_id: schoolId,
                grade: grade,
                class: classNum
            });
        } catch (error) {
            console.error('获取班级学生信息失败:', error);
            return [];
        }
    },

    /**
     * 记录学生签到
     * @param {Object} signinData - 签到数据
     * @returns {Promise} 返回签到结果的Promise
     */
    recordStudentSignin: async function(signinData) {
        try {
            return await this.request('POST', 'student-signin', signinData);
        } catch (error) {
            console.error('记录学生签到失败:', error);
            throw error;
        }
    },

    /**
     * 获取打字文章列表
     * @returns {Promise} 返回文章列表的Promise
     */
    getTypingArticles: async function() {
        try {
            return await this.request('GET', 'typing-articles');
        } catch (error) {
            console.error('获取打字文章失败:', error);
            return [];
        }
    }
};

// 在页面加载后初始化数据库服务
document.addEventListener('DOMContentLoaded', () => {
    DB.init();
}); 