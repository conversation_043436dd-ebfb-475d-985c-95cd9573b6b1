<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置密码 - 班级成绩管理系统</title>
    <link rel="stylesheet" href="/css/style.css">
    <style>
        /* 密码类型选择器样式 - 与主界面保持一致 */
        .password-type-selector {
            display: flex;
            gap: 15px;
            margin: 0 0 30px 0;
            justify-content: center;
        }

        .password-option {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            backdrop-filter: blur(10px);
            min-width: 140px;
            justify-content: center;
        }

        .password-option:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.2);
        }

        .password-option input[type="radio"] {
            margin-right: 8px;
            transform: scale(1.2);
            accent-color: #4a90e2;
        }

        .password-option input[type="radio"]:checked + .option-text {
            color: #4a90e2;
            font-weight: 600;
        }

        .password-option:has(input[type="radio"]:checked) {
            background: rgba(74, 144, 226, 0.1);
            border-color: rgba(74, 144, 226, 0.3);
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.2);
        }

        .option-text {
            color: #333;
            font-size: 14px;
            transition: all 0.3s ease;
            user-select: none;
        }

        /* 随机密码提示样式 */
        .random-note {
            text-align: center;
            padding: 12px;
            background: rgba(74, 144, 226, 0.1);
            border-radius: 12px;
            color: #4a90e2;
            font-size: 14px;
            margin: 15px 0;
            border: 1px solid rgba(74, 144, 226, 0.2);
        }

        /* 响应式调整 */
        @media screen and (max-width: 480px) {
            .password-type-selector {
                flex-direction: column;
                gap: 10px;
            }

            .password-option {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <!-- 重置密码界面 -->
    <div id="login-container">
        <div class="login-box glass-effect">
            <h1>重置密码</h1>
            <div class="login-form">
                <div id="reset-step-1">
                    <!-- 密码设置方式选择 - 放在最上面 -->
                    <div class="password-type-selector">
                        <label class="password-option">
                            <input type="radio" name="passwordType" id="customPassword" value="custom" checked>
                            <span class="option-text">自定义密码</span>
                        </label>
                        <label class="password-option">
                            <input type="radio" name="passwordType" id="randomPassword" value="random">
                            <span class="option-text">自动生成随机密码</span>
                        </label>
                    </div>

                    <input type="text" id="reset-username" placeholder="请输入用户名" required>

                    <!-- 自定义密码输入区域 -->
                    <div id="customPasswordInputs">
                        <input type="password" id="reset-new-password" placeholder="请输入新密码（至少6位）" required>
                        <input type="password" id="reset-confirm-password" placeholder="请确认新密码" required>
                    </div>

                    <!-- 随机密码提示 -->
                    <div id="randomPasswordNote" class="random-note" style="display: none;">
                        <span>系统将自动生成8位随机密码</span>
                    </div>

                    <button id="reset-request-btn" class="login-btn">重置密码</button>
                    <div class="register-link">
                        <p><a href="/">返回登录</a></p>
                    </div>
                </div>
                <div id="reset-step-2" style="display: none;">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #e9ecef;">
                        <h3 style="color: #28a745; margin-bottom: 15px; text-align: center;">
                            密码重置成功
                        </h3>
                        <p style="color: #333; margin-bottom: 15px; text-align: center;">
                            您的新密码是：
                        </p>
                        <div style="background: #fff; padding: 15px; border-radius: 6px; margin-bottom: 15px; border: 2px solid #28a745; text-align: center;">
                            <span id="new-password-display" style="font-family: 'Courier New', monospace; font-size: 20px; color: #333; font-weight: bold; letter-spacing: 2px;"></span>
                            <br>
                            <button id="copy-password-btn" style="margin-top: 10px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                                复制密码
                            </button>
                        </div>
                        <div style="background: #fff3cd; padding: 12px; border-radius: 6px; border-left: 4px solid #ffc107;">
                            <p style="color: #856404; font-size: 14px; margin: 0;">
                                <strong>重要提示：</strong> 请妥善保管您的新密码，建议首次登录后立即修改为您熟悉的密码。
                            </p>
                        </div>
                    </div>
                    <button id="reset-login-btn" class="login-btn">返回登录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="/js/config.js"></script>
    <script src="/js/utils.js"></script>
    <script>
        // 重置密码功能
        document.addEventListener('DOMContentLoaded', function() {
            const resetRequestBtn = document.getElementById('reset-request-btn');
            const resetLoginBtn = document.getElementById('reset-login-btn');
            const copyPasswordBtn = document.getElementById('copy-password-btn');
            const customPasswordRadio = document.getElementById('customPassword');
            const randomPasswordRadio = document.getElementById('randomPassword');
            const customPasswordInputs = document.getElementById('customPasswordInputs');
            const randomPasswordNote = document.getElementById('randomPasswordNote');
            const newPasswordInput = document.getElementById('reset-new-password');
            const confirmPasswordInput = document.getElementById('reset-confirm-password');

            if (resetRequestBtn) {
                resetRequestBtn.addEventListener('click', handleResetPassword);
            }

            if (resetLoginBtn) {
                resetLoginBtn.addEventListener('click', () => {
                    window.location.href = '/';
                });
            }

            if (copyPasswordBtn) {
                copyPasswordBtn.addEventListener('click', copyPassword);
            }

            // 密码类型选择事件
            document.querySelectorAll('input[name="passwordType"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'custom') {
                        customPasswordInputs.style.display = 'block';
                        randomPasswordNote.style.display = 'none';
                        newPasswordInput.disabled = false;
                        confirmPasswordInput.disabled = false;
                        newPasswordInput.placeholder = '请输入新密码（至少6位）';
                        confirmPasswordInput.placeholder = '请确认新密码';
                    } else {
                        customPasswordInputs.style.display = 'none';
                        randomPasswordNote.style.display = 'block';
                        // 清空自定义密码输入
                        newPasswordInput.value = '';
                        confirmPasswordInput.value = '';
                    }
                });
            });
        });

        /**
         * 处理重置密码
         */
        async function handleResetPassword() {
            const username = document.getElementById('reset-username').value.trim();
            const isCustomPassword = document.getElementById('customPassword').checked;
            const isRandomPassword = document.getElementById('randomPassword').checked;

            if (!username) {
                showMessage('请输入用户名', 'error');
                return;
            }

            let newPassword;
            if (isRandomPassword) {
                // 生成随机密码
                newPassword = generateRandomPassword();
            } else {
                const password = document.getElementById('reset-new-password').value;
                const confirmPassword = document.getElementById('reset-confirm-password').value;

                if (!password || !confirmPassword) {
                    showMessage('请输入新密码和确认密码', 'error');
                    return;
                }

                if (password.length < 6) {
                    showMessage('密码长度至少6位', 'error');
                    return;
                }

                if (password !== confirmPassword) {
                    showMessage('两次输入的密码不一致', 'error');
                    return;
                }

                newPassword = password;
            }

            try {
                const response = await fetch('/api/reset-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username,
                        newPassword
                    })
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    // 显示新密码
                    document.getElementById('new-password-display').textContent = result.newPassword;
                    document.getElementById('reset-step-1').style.display = 'none';
                    document.getElementById('reset-step-2').style.display = 'block';

                    showMessage('密码重置成功', 'success');
                } else {
                    showMessage(result.message || '重置密码失败', 'error');
                }
            } catch (error) {
                console.error('重置密码错误:', error);
                showMessage('重置密码失败，请重试', 'error');
            }
        }

        /**
         * 生成随机密码
         */
        function generateRandomPassword() {
            const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
            let password = '';
            for (let i = 0; i < 8; i++) {
                password += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return password;
        }

        /**
         * 复制密码
         */
        function copyPassword() {
            const passwordText = document.getElementById('new-password-display').textContent;
            navigator.clipboard.writeText(passwordText).then(() => {
                showMessage('密码已复制到剪贴板', 'success');
            }).catch(() => {
                showMessage('复制失败，请手动复制', 'error');
            });
        }

        /**
         * 显示消息
         */
        function showMessage(message, type = 'info') {
            // 移除现有消息
            const existingMessage = document.querySelector('.message');
            if (existingMessage) {
                existingMessage.remove();
            }

            // 创建新消息
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
            `;

            document.body.appendChild(messageDiv);

            // 3秒后自动移除
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
