/**
 * 性能监控中间件
 * 监控API请求性能，记录慢查询
 */

const { cache } = require('../utils/cache');

// 慢查询阈值（毫秒）
const SLOW_QUERY_THRESHOLD = 1000;

// 性能统计
const performanceStats = {
    totalRequests: 0,
    slowRequests: 0,
    averageResponseTime: 0,
    requestTimes: []
};

/**
 * 性能监控中间件
 */
const performanceMonitor = (req, res, next) => {
    const startTime = Date.now();
    const originalSend = res.send;
    
    // 重写res.send方法以捕获响应时间
    res.send = function(data) {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        // 更新统计信息
        updatePerformanceStats(req, responseTime);
        
        // 记录慢查询
        if (responseTime > SLOW_QUERY_THRESHOLD) {
            logSlowQuery(req, responseTime);
        }
        
        // 添加性能头信息
        res.set('X-Response-Time', `${responseTime}ms`);
        
        // 调用原始send方法
        originalSend.call(this, data);
    };
    
    next();
};

/**
 * 更新性能统计信息
 */
function updatePerformanceStats(req, responseTime) {
    performanceStats.totalRequests++;
    performanceStats.requestTimes.push(responseTime);
    
    // 保持最近1000个请求的记录
    if (performanceStats.requestTimes.length > 1000) {
        performanceStats.requestTimes.shift();
    }
    
    // 计算平均响应时间
    const sum = performanceStats.requestTimes.reduce((a, b) => a + b, 0);
    performanceStats.averageResponseTime = Math.round(sum / performanceStats.requestTimes.length);
    
    // 统计慢请求
    if (responseTime > SLOW_QUERY_THRESHOLD) {
        performanceStats.slowRequests++;
    }
}

/**
 * 记录慢查询
 */
function logSlowQuery(req, responseTime) {
    const logEntry = {
        method: req.method,
        url: req.originalUrl,
        responseTime,
        timestamp: new Date().toISOString(),
        userAgent: req.get('User-Agent'),
        ip: req.ip || req.connection.remoteAddress
    };
    
    console.log(`慢请求: ${req.method} ${req.originalUrl} - ${responseTime}ms`);
    
    // 可以考虑将慢查询日志存储到数据库或文件
    // 这里暂时只输出到控制台
}

/**
 * 获取性能统计信息
 */
function getPerformanceStats() {
    const now = Date.now();
    const recentRequests = performanceStats.requestTimes.filter(time => 
        now - time < 60 * 1000 // 最近1分钟
    );
    
    return {
        ...performanceStats,
        recentRequestsCount: recentRequests.length,
        slowRequestPercentage: performanceStats.totalRequests > 0 
            ? Math.round((performanceStats.slowRequests / performanceStats.totalRequests) * 100)
            : 0,
        cacheStats: cache.getStats()
    };
}

/**
 * 性能报告API端点
 */
const performanceReport = (req, res) => {
    const stats = getPerformanceStats();
    
    res.json({
        success: true,
        data: {
            performance: stats,
            recommendations: generateRecommendations(stats)
        }
    });
};

/**
 * 生成性能优化建议
 */
function generateRecommendations(stats) {
    const recommendations = [];
    
    if (stats.averageResponseTime > 500) {
        recommendations.push({
            type: 'warning',
            message: '平均响应时间较高，建议检查数据库查询优化'
        });
    }
    
    if (stats.slowRequestPercentage > 10) {
        recommendations.push({
            type: 'error',
            message: '慢请求比例过高，需要立即优化'
        });
    }
    
    if (stats.cacheStats.valid < 10) {
        recommendations.push({
            type: 'info',
            message: '缓存命中率较低，建议增加缓存策略'
        });
    }
    
    if (recommendations.length === 0) {
        recommendations.push({
            type: 'success',
            message: '系统性能良好'
        });
    }
    
    return recommendations;
}

/**
 * 重置性能统计
 */
function resetPerformanceStats() {
    performanceStats.totalRequests = 0;
    performanceStats.slowRequests = 0;
    performanceStats.averageResponseTime = 0;
    performanceStats.requestTimes = [];
    
    console.log('性能统计已重置');
}

/**
 * 数据库查询性能装饰器
 */
function measureQueryTime(queryName) {
    return function(target, propertyName, descriptor) {
        const method = descriptor.value;
        
        descriptor.value = async function(...args) {
            const startTime = Date.now();
            
            try {
                const result = await method.apply(this, args);
                const endTime = Date.now();
                const queryTime = endTime - startTime;
                
                if (queryTime > SLOW_QUERY_THRESHOLD) {
                    console.log(`慢查询检测: ${queryName} - ${queryTime}ms`);
                }
                
                return result;
            } catch (error) {
                const endTime = Date.now();
                const queryTime = endTime - startTime;
                console.error(`查询失败: ${queryName} - ${queryTime}ms - ${error.message}`);
                throw error;
            }
        };
        
        return descriptor;
    };
}

module.exports = {
    performanceMonitor,
    performanceReport,
    getPerformanceStats,
    resetPerformanceStats,
    measureQueryTime,
    SLOW_QUERY_THRESHOLD
};
