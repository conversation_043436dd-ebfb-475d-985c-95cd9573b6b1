/**
 * 修复教师权限数据脚本
 * 确保教师权限表数据的一致性
 *
 * 这个脚本会：
 * 1. 检查所有教师的班级权限
 * 2. 为缺少学校分配的教师自动创建学校分配记录
 * 3. 验证修复结果
 */

const db = require('./config/db');

async function fixTeacherPermissions() {
  console.log('=== 开始修复教师权限数据 ===\n');

  try {
    // 1. 获取所有教师
    console.log('1. 获取所有教师...');
    const { data: teachers, error: teachersError } = await db.supabase
      .from('users')
      .select('id, username, display_name')
      .eq('role', 'teacher');

    if (teachersError) {
      console.error('获取教师失败:', teachersError);
      return;
    }

    console.log(`找到 ${teachers.length} 个教师\n`);

    // 2. 为每个教师检查和修复权限
    for (const teacher of teachers) {
      console.log(`处理教师: ${teacher.username} (ID: ${teacher.id})`);

      // 获取该教师的班级权限
      const { data: classPermissions, error: classError } = await db.supabase
        .from('teacher_class_permissions')
        .select('school_id, grade, class')
        .eq('teacher_id', teacher.id);

      if (classError) {
        console.error(`  获取班级权限失败:`, classError);
        continue;
      }

      if (!classPermissions || classPermissions.length === 0) {
        console.log(`  该教师没有班级权限，跳过`);
        continue;
      }

      // 获取该教师涉及的所有学校ID
      const schoolIds = [...new Set(classPermissions.map(cp => cp.school_id))];
      console.log(`  涉及学校: ${schoolIds.join(', ')}`);

      // 检查每个学校是否有对应的学校分配
      for (const schoolId of schoolIds) {
        const { data: existingAssignment, error: assignmentError } = await db.supabase
          .from('teacher_school_assignments')
          .select('id')
          .eq('teacher_id', teacher.id)
          .eq('school_id', schoolId)
          .single();

        if (assignmentError && assignmentError.code !== 'PGRST116') {
          console.error(`    检查学校分配失败 (学校ID: ${schoolId}):`, assignmentError);
          continue;
        }

        if (!existingAssignment) {
          console.log(`    缺少学校分配，正在创建 (学校ID: ${schoolId})`);
          
          // 创建缺失的学校分配
          const { data: newAssignment, error: createError } = await db.supabase
            .from('teacher_school_assignments')
            .insert({
              teacher_id: teacher.id,
              school_id: schoolId,
              created_at: new Date().toISOString()
            })
            .select()
            .single();

          if (createError) {
            console.error(`    创建学校分配失败:`, createError);
          } else {
            console.log(`    ✓ 成功创建学校分配 (ID: ${newAssignment.id})`);
          }
        } else {
          console.log(`    ✓ 学校分配已存在 (学校ID: ${schoolId})`);
        }
      }

      console.log(`  处理完成\n`);
    }

    // 3. 验证修复结果
    console.log('=== 验证修复结果 ===');
    
    const { data: orphanedPermissions, error: orphanError } = await db.supabase
      .rpc('check_orphaned_permissions');

    if (orphanError) {
      console.log('无法验证孤立权限（可能是因为RPC函数不存在），手动检查...');
      
      // 手动检查孤立权限
      const { data: allClassPermissions } = await db.supabase
        .from('teacher_class_permissions')
        .select(`
          id,
          teacher_id,
          school_id,
          grade,
          class,
          users(username),
          schools(name)
        `);

      let orphanedCount = 0;
      for (const cp of allClassPermissions || []) {
        const { data: assignment } = await db.supabase
          .from('teacher_school_assignments')
          .select('id')
          .eq('teacher_id', cp.teacher_id)
          .eq('school_id', cp.school_id)
          .single();

        if (!assignment) {
          orphanedCount++;
          console.log(`  孤立权限: ${cp.users?.username} - ${cp.schools?.name} - ${cp.grade}年级${cp.class}班`);
        }
      }

      if (orphanedCount === 0) {
        console.log('✓ 没有发现孤立的班级权限');
      } else {
        console.log(`⚠ 仍有 ${orphanedCount} 个孤立的班级权限`);
      }
    }

    // 4. 显示最终统计
    console.log('\n=== 最终统计 ===');
    
    const { data: finalStats } = await db.supabase
      .rpc('get_teacher_permission_stats');

    if (finalStats) {
      console.log('权限统计:', finalStats);
    } else {
      // 手动统计
      const { data: schoolAssignments } = await db.supabase
        .from('teacher_school_assignments')
        .select('id');
      
      const { data: classPermissions } = await db.supabase
        .from('teacher_class_permissions')
        .select('id');

      console.log(`学校分配总数: ${schoolAssignments?.length || 0}`);
      console.log(`班级权限总数: ${classPermissions?.length || 0}`);
    }

  } catch (error) {
    console.error('修复过程中发生错误:', error);
  }
}

// 运行修复
fixTeacherPermissions().then(() => {
  console.log('\n=== 修复完成 ===');
  process.exit(0);
}).catch(error => {
  console.error('修复失败:', error);
  process.exit(1);
});
