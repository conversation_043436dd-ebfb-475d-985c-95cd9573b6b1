# 🔒 Supabase 数据安全配置指南

## ⚠️ 当前安全风险

### 严重问题：数据表设置为 "unrestricted"
您的Supabase数据表当前设置为unrestricted（无限制），这意味着：
- **任何人都可以读取、写入、删除数据**
- **学生个人信息完全暴露**
- **没有任何访问控制**
- **存在严重的数据泄露风险**

## 🛡️ 立即安全措施

### 1. 启用行级安全 (RLS)
在Supabase控制台中为每个表启用RLS：

```sql
-- 为所有重要表启用RLS
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE medals ENABLE ROW LEVEL SECURITY;
ALTER TABLE typing_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE schools ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_class_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE articles ENABLE ROW LEVEL SECURITY;
```

### 2. 创建安全策略

#### 学生表安全策略
```sql
-- 学生表：只允许认证用户查看，管理员可以修改
CREATE POLICY "学生数据查看策略" ON students
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "学生数据修改策略" ON students
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'admin'
        )
    );
```

#### 奖章表安全策略
```sql
-- 奖章表：只允许认证用户访问
CREATE POLICY "奖章数据访问策略" ON medals
    FOR ALL USING (auth.role() = 'authenticated');
```

#### 打字记录表安全策略
```sql
-- 打字记录表：只允许认证用户访问
CREATE POLICY "打字记录访问策略" ON typing_records
    FOR ALL USING (auth.role() = 'authenticated');
```

#### 学校表安全策略
```sql
-- 学校表：只允许认证用户查看
CREATE POLICY "学校数据查看策略" ON schools
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "学校数据修改策略" ON schools
    FOR INSERT, UPDATE, DELETE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role IN ('admin', 'teacher')
        )
    );
```

### 3. 用户认证策略
```sql
-- 用户表：只允许查看自己的信息
CREATE POLICY "用户自己信息策略" ON users
    FOR SELECT USING (id = auth.uid()::text::integer);

CREATE POLICY "管理员用户管理策略" ON users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'admin'
        )
    );
```

### 4. 教师权限表安全策略
```sql
-- 教师权限表：只允许相关教师和管理员访问
CREATE POLICY "教师权限访问策略" ON teacher_class_permissions
    FOR SELECT USING (
        teacher_id = auth.uid()::text::integer OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'admin'
        )
    );
```

## 🔧 实施步骤

### 步骤1：备份数据
```sql
-- 在实施安全策略前，先备份重要数据
-- 在Supabase控制台的SQL编辑器中运行
SELECT * FROM students;
SELECT * FROM medals;
SELECT * FROM typing_records;
```

### 步骤2：逐步启用RLS
1. 先为一个测试表启用RLS
2. 创建相应的安全策略
3. 测试应用功能是否正常
4. 逐步为其他表启用

### 步骤3：测试访问控制
1. 使用不同角色的用户测试
2. 确认只能访问授权的数据
3. 验证所有功能正常工作

### 步骤4：监控和调整
1. 监控应用日志
2. 根据需要调整策略
3. 定期审查安全设置

## 🚨 紧急安全检查清单

- [ ] **立即启用RLS** - 防止未授权访问
- [ ] **创建基本安全策略** - 限制数据访问
- [ ] **测试应用功能** - 确保正常工作
- [ ] **审查API密钥** - 确保使用正确的密钥
- [ ] **检查环境变量** - 确保生产环境安全
- [ ] **监控访问日志** - 发现异常访问

## 📞 如果遇到问题

1. **应用无法访问数据**：检查RLS策略是否正确
2. **用户无法登录**：检查用户表的安全策略
3. **功能异常**：逐步禁用RLS找出问题表

## 🎯 最佳实践

1. **最小权限原则**：只给用户必需的权限
2. **定期审查**：定期检查和更新安全策略
3. **监控日志**：监控数据库访问日志
4. **测试环境**：在测试环境中验证安全策略
5. **备份策略**：定期备份数据和配置

---

**⚠️ 重要提醒**：当前的unrestricted设置是严重的安全风险，请立即实施上述安全措施！
