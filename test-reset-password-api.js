/**
 * 测试重置密码API
 */

async function testResetPasswordAPI() {
    try {
        console.log('=== 测试重置密码API ===');
        
        // 测试重置密码
        console.log('1. 测试重置密码...');
        const resetResponse = await fetch('http://localhost:3005/api/reset-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: 'admin'  // 测试admin用户
            })
        });
        
        console.log('重置密码响应状态:', resetResponse.status, resetResponse.statusText);
        
        if (!resetResponse.ok) {
            const errorText = await resetResponse.text();
            console.error('重置密码失败，响应内容:', errorText);
            throw new Error(`重置密码失败: ${resetResponse.status} ${resetResponse.statusText}`);
        }
        
        const resetResult = await resetResponse.json();
        console.log('重置密码成功:', resetResult);
        
        if (resetResult.success) {
            console.log('新密码:', resetResult.newPassword);
            
            // 测试使用新密码登录
            console.log('2. 测试使用新密码登录...');
            const loginResponse = await fetch('http://localhost:3005/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: 'admin',
                    password: resetResult.newPassword
                })
            });
            
            console.log('登录响应状态:', loginResponse.status, loginResponse.statusText);
            
            if (loginResponse.ok) {
                const loginResult = await loginResponse.json();
                console.log('使用新密码登录成功:', loginResult.data.user);
            } else {
                const loginError = await loginResponse.text();
                console.error('使用新密码登录失败:', loginError);
            }
        }
        
        console.log('=== 测试完成 ===');
        
    } catch (error) {
        console.error('测试失败:', error.message);
        console.error('详细错误:', error);
    }
}

// 运行测试
testResetPasswordAPI();
