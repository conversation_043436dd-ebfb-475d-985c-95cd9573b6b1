# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov
coverage/
.nyc_output

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 临时文件
.tmp/
temp/

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 构建输出
dist/
build/
out/

# 缓存文件
.cache/
.parcel-cache/

# 备份文件
*.bak
*.backup
*.old

# 上传的文件
uploads/
