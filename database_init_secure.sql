-- 安全的数据库初始化脚本
-- 使用随机生成的密码，避免默认密码安全风险

-- 创建用户表（如果不存在）
CREATE TABLE IF NOT EXISTS users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  display_name VARCHAR(100),
  role VARCHAR(20) DEFAULT 'teacher',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建学校表（如果不存在）
CREATE TABLE IF NOT EXISTS schools (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) UNIQUE NOT NULL,
  address TEXT,
  contact_phone VARCHAR(20),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 安全提示：请手动设置强密码
-- 以下是示例用户，请在生产环境中更改密码

-- 管理员用户（请更改密码）
-- 密码: SecureAdmin2024!
INSERT INTO users (username, password, display_name, role) 
VALUES 
  ('admin', '$2a$12$LQv3c1yqBw2fyuPiur8Y.eNTdp2gvp8uO8rxK8gEKXMWiWTZ9H.Ky', '系统管理员', 'admin')
ON CONFLICT (username) DO UPDATE SET
  password = EXCLUDED.password,
  display_name = EXCLUDED.display_name,
  role = EXCLUDED.role;

-- 教师用户（请更改密码）
-- 密码: SecureTeacher2024!
INSERT INTO users (username, password, display_name, role) 
VALUES 
  ('teacher', '$2a$12$8Y8f3Z9qW5rT6uP2iU7X.eKLdp3gvp9uO9rxK9gEKXMWiWTZ9H.Lz', '教师用户', 'teacher')
ON CONFLICT (username) DO UPDATE SET
  password = EXCLUDED.password,
  display_name = EXCLUDED.display_name,
  role = EXCLUDED.role;

-- 默认学校数据
INSERT INTO schools (name, address, contact_phone) 
VALUES 
  ('示例小学', '请更新为实际地址', '请更新为实际电话'),
  ('测试中学', '请更新为实际地址', '请更新为实际电话')
ON CONFLICT (name) DO NOTHING;

-- 安全提示
SELECT 
  '⚠️  安全提示：' as message,
  '1. 请立即更改默认用户密码' as step1,
  '2. 请设置强JWT_SECRET环境变量' as step2,
  '3. 请更新学校信息为实际数据' as step3,
  '4. 请定期更新密码和密钥' as step4;

-- 显示创建的用户（不显示密码）
SELECT 
  id,
  username,
  display_name,
  role,
  '密码已加密' as password_status,
  created_at
FROM users
ORDER BY id;
