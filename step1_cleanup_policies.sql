-- 🧹 第一步：仅清理现有策略
-- 请先执行这个脚本清理所有现有策略

-- 删除学生表现有策略
DROP POLICY IF EXISTS "学生数据查看策略" ON students;
DROP POLICY IF EXISTS "学生数据插入策略" ON students;
DROP POLICY IF EXISTS "学生数据更新策略" ON students;
DROP POLICY IF EXISTS "学生数据删除策略" ON students;

-- 删除奖章表现有策略
DROP POLICY IF EXISTS "奖章数据查看策略" ON medals;
DROP POLICY IF EXISTS "奖章数据修改策略" ON medals;
DROP POLICY IF EXISTS "奖章数据更新策略" ON medals;
DROP POLICY IF EXISTS "奖章数据删除策略" ON medals;

-- 删除打字记录表现有策略
DROP POLICY IF EXISTS "打字记录查看策略" ON typing_records;
DROP POLICY IF EXISTS "打字记录插入策略" ON typing_records;
DROP POLICY IF EXISTS "打字记录更新策略" ON typing_records;
DROP POLICY IF EXISTS "打字记录删除策略" ON typing_records;

-- 删除学校表现有策略
DROP POLICY IF EXISTS "学校数据查看策略" ON schools;
DROP POLICY IF EXISTS "学校数据插入策略" ON schools;
DROP POLICY IF EXISTS "学校数据更新策略" ON schools;
DROP POLICY IF EXISTS "学校数据删除策略" ON schools;

-- 删除用户表现有策略
DROP POLICY IF EXISTS "用户查看自己信息策略" ON users;
DROP POLICY IF EXISTS "管理员用户插入策略" ON users;
DROP POLICY IF EXISTS "管理员用户更新策略" ON users;
DROP POLICY IF EXISTS "管理员用户删除策略" ON users;

-- 删除教师权限表现有策略
DROP POLICY IF EXISTS "教师权限查看策略" ON teacher_class_permissions;
DROP POLICY IF EXISTS "教师权限插入策略" ON teacher_class_permissions;
DROP POLICY IF EXISTS "教师权限更新策略" ON teacher_class_permissions;
DROP POLICY IF EXISTS "教师权限删除策略" ON teacher_class_permissions;

-- 删除文章表现有策略
DROP POLICY IF EXISTS "文章查看策略" ON articles;
DROP POLICY IF EXISTS "文章插入策略" ON articles;
DROP POLICY IF EXISTS "文章更新策略" ON articles;
DROP POLICY IF EXISTS "文章删除策略" ON articles;

-- 删除其他表的策略
DROP POLICY IF EXISTS "日志查看策略" ON logs;
DROP POLICY IF EXISTS "年级配置查看策略" ON school_grade_configs;
DROP POLICY IF EXISTS "导入日志查看策略" ON student_import_logs;
DROP POLICY IF EXISTS "学生会话查看策略" ON student_sessions;
DROP POLICY IF EXISTS "学生登录查看策略" ON student_signins;
DROP POLICY IF EXISTS "教师班级查看策略" ON teacher_classes;
DROP POLICY IF EXISTS "教师学校分配查看策略" ON teacher_school_assignments;
DROP POLICY IF EXISTS "最佳打字记录查看策略" ON typing_best;

SELECT '✅ 所有现有策略已清理完成' as 状态;
