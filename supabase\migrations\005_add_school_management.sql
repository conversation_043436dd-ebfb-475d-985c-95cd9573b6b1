-- 添加学校管理相关表结构
-- 这个迁移用于支持教师自主管理学校、年级、班级和学生导入功能

-- 创建学校表
CREATE TABLE IF NOT EXISTS schools (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL UNIQUE,
  address TEXT,
  contact_phone VARCHAR(50),
  contact_email VARCHAR(255),
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建学校索引
CREATE INDEX IF NOT EXISTS idx_schools_name ON schools(name);

-- 创建教师学校关联表（教师可以在多个学校任教）
CREATE TABLE IF NOT EXISTS teacher_schools (
  id SERIAL PRIMARY KEY,
  teacher_id INTEGER NOT NULL,
  school_id INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 外键约束
  CONSTRAINT fk_teacher_schools_teacher FOREIGN KEY (teacher_id) 
    REFERENCES users(id) ON DELETE CASCADE,
  CONSTRAINT fk_teacher_schools_school FOREIGN KEY (school_id) 
    REFERENCES schools(id) ON DELETE CASCADE,
  
  -- 唯一约束：防止重复关联同一教师到同一学校
  CONSTRAINT unique_teacher_school UNIQUE (teacher_id, school_id)
);

-- 创建教师学校关联表索引
CREATE INDEX IF NOT EXISTS idx_teacher_schools_teacher ON teacher_schools(teacher_id);
CREATE INDEX IF NOT EXISTS idx_teacher_schools_school ON teacher_schools(school_id);

-- 修改现有的teacher_classes表，添加学校关联
ALTER TABLE teacher_classes ADD COLUMN IF NOT EXISTS school_id INTEGER;

-- 添加外键约束（使用DO块来处理IF NOT EXISTS逻辑）
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_teacher_classes_school'
        AND table_name = 'teacher_classes'
    ) THEN
        ALTER TABLE teacher_classes ADD CONSTRAINT fk_teacher_classes_school
        FOREIGN KEY (school_id) REFERENCES schools(id) ON DELETE SET NULL;
    END IF;
END $$;

-- 创建学校相关索引
CREATE INDEX IF NOT EXISTS idx_teacher_classes_school ON teacher_classes(school_id);

-- 修改students表，添加学校关联
ALTER TABLE students ADD COLUMN IF NOT EXISTS school_id INTEGER;

-- 添加学生表的外键约束
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_students_school'
        AND table_name = 'students'
    ) THEN
        ALTER TABLE students ADD CONSTRAINT fk_students_school
        FOREIGN KEY (school_id) REFERENCES schools(id) ON DELETE SET NULL;
    END IF;
END $$;

-- 创建学生学校索引
CREATE INDEX IF NOT EXISTS idx_students_school ON students(school_id);
CREATE INDEX IF NOT EXISTS idx_students_school_grade_class ON students(school_id, grade, class);

-- 创建学生导入日志表
CREATE TABLE IF NOT EXISTS student_import_logs (
  id SERIAL PRIMARY KEY,
  teacher_id INTEGER NOT NULL,
  school_id INTEGER,
  grade INTEGER,
  class INTEGER,
  total_count INTEGER NOT NULL DEFAULT 0,
  success_count INTEGER NOT NULL DEFAULT 0,
  error_count INTEGER NOT NULL DEFAULT 0,
  file_name VARCHAR(255),
  import_data JSONB, -- 存储导入的原始数据
  error_details JSONB, -- 存储错误详情
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 外键约束
  CONSTRAINT fk_import_logs_teacher FOREIGN KEY (teacher_id) 
    REFERENCES users(id) ON DELETE CASCADE,
  CONSTRAINT fk_import_logs_school FOREIGN KEY (school_id) 
    REFERENCES schools(id) ON DELETE SET NULL
);

-- 创建导入日志索引
CREATE INDEX IF NOT EXISTS idx_import_logs_teacher ON student_import_logs(teacher_id);
CREATE INDEX IF NOT EXISTS idx_import_logs_school ON student_import_logs(school_id);
CREATE INDEX IF NOT EXISTS idx_import_logs_created ON student_import_logs(created_at);

-- 创建更新时间触发器（使用DO块处理IF NOT EXISTS逻辑）
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers
        WHERE trigger_name = 'update_schools_updated_at'
        AND event_object_table = 'schools'
    ) THEN
        CREATE TRIGGER update_schools_updated_at
        BEFORE UPDATE ON schools
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 插入一些示例学校数据
INSERT INTO schools (name, address, description) VALUES
('示例小学', '北京市朝阳区示例街道123号', '这是一所示例小学'),
('实验中学', '上海市浦东新区实验路456号', '这是一所实验中学'),
('希望学校', '广州市天河区希望大道789号', '这是一所希望学校')
ON CONFLICT (name) DO NOTHING;

-- 创建函数：获取教师可管理的学校列表
CREATE OR REPLACE FUNCTION get_teacher_schools(p_teacher_id INTEGER)
RETURNS TABLE (
  id INTEGER,
  name VARCHAR(255),
  address TEXT,
  contact_phone VARCHAR(50),
  contact_email VARCHAR(255),
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  -- 管理员可以查看所有学校
  IF EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = p_teacher_id AND role = 'admin'
  ) THEN
    RETURN QUERY
    SELECT s.id, s.name, s.address, s.contact_phone, s.contact_email, s.description, s.created_at
    FROM schools s
    ORDER BY s.name;
  ELSE
    -- 普通教师只能查看自己关联的学校
    RETURN QUERY
    SELECT s.id, s.name, s.address, s.contact_phone, s.contact_email, s.description, s.created_at
    FROM schools s
    JOIN teacher_schools ts ON s.id = ts.school_id
    WHERE ts.teacher_id = p_teacher_id
    ORDER BY s.name;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- 创建函数：获取教师在指定学校的班级列表
CREATE OR REPLACE FUNCTION get_teacher_classes_by_school(
  p_teacher_id INTEGER,
  p_school_id INTEGER
)
RETURNS TABLE (
  id INTEGER,
  grade INTEGER,
  class INTEGER,
  student_count BIGINT
) AS $$
BEGIN
  -- 管理员可以查看指定学校的所有班级
  IF EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = p_teacher_id AND role = 'admin'
  ) THEN
    RETURN QUERY
    SELECT 
      tc.id,
      tc.grade,
      tc.class,
      COUNT(s.id) as student_count
    FROM teacher_classes tc
    LEFT JOIN students s ON tc.grade = s.grade AND tc.class = s.class AND tc.school_id = s.school_id
    WHERE tc.school_id = p_school_id
    GROUP BY tc.id, tc.grade, tc.class
    ORDER BY tc.grade, tc.class;
  ELSE
    -- 普通教师只能查看自己在该学校任教的班级
    RETURN QUERY
    SELECT 
      tc.id,
      tc.grade,
      tc.class,
      COUNT(s.id) as student_count
    FROM teacher_classes tc
    LEFT JOIN students s ON tc.grade = s.grade AND tc.class = s.class AND tc.school_id = s.school_id
    WHERE tc.teacher_id = p_teacher_id AND tc.school_id = p_school_id
    GROUP BY tc.id, tc.grade, tc.class
    ORDER BY tc.grade, tc.class;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- 创建函数：批量导入学生
CREATE OR REPLACE FUNCTION batch_import_students(
  p_teacher_id INTEGER,
  p_school_id INTEGER,
  p_grade INTEGER,
  p_class INTEGER,
  p_students JSONB
)
RETURNS TABLE (
  success_count INTEGER,
  error_count INTEGER,
  error_details JSONB
) AS $$
DECLARE
  v_student JSONB;
  v_success_count INTEGER := 0;
  v_error_count INTEGER := 0;
  v_error_details JSONB := '[]'::JSONB;
  v_error_info JSONB;
BEGIN
  -- 遍历学生数据
  FOR v_student IN SELECT * FROM jsonb_array_elements(p_students)
  LOOP
    BEGIN
      -- 尝试插入学生
      INSERT INTO students (student_identifier, name, grade, class, school_id, created_at)
      VALUES (
        v_student->>'student_identifier',
        v_student->>'name',
        p_grade,
        p_class,
        p_school_id,
        NOW()
      );
      
      v_success_count := v_success_count + 1;
      
    EXCEPTION WHEN OTHERS THEN
      v_error_count := v_error_count + 1;
      v_error_info := jsonb_build_object(
        'student_identifier', v_student->>'student_identifier',
        'name', v_student->>'name',
        'error', SQLERRM
      );
      v_error_details := v_error_details || v_error_info;
    END;
  END LOOP;
  
  -- 记录导入日志
  INSERT INTO student_import_logs (
    teacher_id, school_id, grade, class,
    total_count, success_count, error_count,
    import_data, error_details
  ) VALUES (
    p_teacher_id, p_school_id, p_grade, p_class,
    jsonb_array_length(p_students), v_success_count, v_error_count,
    p_students, v_error_details
  );
  
  RETURN QUERY SELECT v_success_count, v_error_count, v_error_details;
END;
$$ LANGUAGE plpgsql;
