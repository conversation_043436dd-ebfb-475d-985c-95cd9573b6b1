/**
 * 数据库配置和连接
 * 使用@supabase/supabase-js连接Supabase PostgreSQL数据库
 */

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const path = require('path');

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// 创建Supabase客户端
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('错误: 缺少必要的Supabase环境变量');
  console.error('SUPABASE_URL:', supabaseUrl ? '已设置' : '未设置');
  console.error('SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '已设置' : '未设置');

  // 在Vercel环境中不能使用process.exit，抛出错误让调用者处理
  throw new Error('缺少必要的Supabase环境变量');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// 测试数据库连接
const testConnection = async () => {
  try {
    console.log('=== 数据库连接诊断开始 ===');
    console.log('Supabase URL:', supabaseUrl);
    console.log('Service Key前10位:', supabaseServiceKey.substring(0, 10) + '...');
    console.log('正在测试数据库连接...');

    const { data, error } = await supabase
      .from('users')
      .select('count', { count: 'exact', head: true });

    if (error) {
      console.error('数据库查询错误:', error);
      console.error('错误代码:', error.code);
      console.error('错误消息:', error.message);
      console.error('错误详情:', error.details);
      console.error('错误提示:', error.hint);
    } else {
      console.log('Supabase数据库连接成功!');
    }
    console.log('=== 数据库连接诊断结束 ===');
  } catch (err) {
    console.error('=== 数据库连接失败 ===');
    console.error('错误类型:', err.constructor.name);
    console.error('错误消息:', err.message);
    console.error('完整错误:', err);
    console.error('=== 诊断结束 ===');
  }
};

// 只在非生产环境下执行连接测试
if (process.env.NODE_ENV !== 'production') {
  testConnection();
}

/**
 * 获取Supabase客户端实例
 * @returns {Object} Supabase客户端
 */
const getConnection = () => {
  return supabase;
};

/**
 * 执行原生SQL查询（用于复杂查询）
 * @param {string} sql SQL查询语句
 * @param {Array} params 查询参数
 * @returns {Promise<Object>} 查询结果
 */
const query = async (sql, params = []) => {
  try {
    // 使用Supabase的rpc功能执行原生SQL
    const { data, error } = await supabase.rpc('execute_sql', {
      query: sql,
      parameters: params
    });

    if (error) {
      console.error('SQL错误:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('SQL执行错误:', error);
    throw error;
  }
};

/**
 * 执行SQL语句（插入、更新、删除）
 * 注意：这是为了兼容现有代码，建议使用Supabase的标准方法
 * @param {string} sql SQL语句
 * @param {Array} params 查询参数
 * @returns {Promise<Object>} 执行结果
 */
const execute = async (sql, params = []) => {
  try {
    const result = await query(sql, params);
    return result;
  } catch (error) {
    console.error('SQL执行错误:', error);
    throw error;
  }
};

/**
 * 获取最后插入的ID（PostgreSQL使用RETURNING子句）
 * @param {string} table 表名
 * @returns {Promise<number>} 最后插入的ID
 */
const getLastInsertId = async (table = '') => {
  // PostgreSQL中通常使用RETURNING子句获取插入的ID
  // 这个函数主要用于兼容，实际使用中建议直接使用RETURNING
  console.warn('getLastInsertId已弃用，请在INSERT语句中使用RETURNING子句');
  return null;
};

/**
 * 事务处理（Supabase自动处理事务）
 * @param {Function} callback 事务回调函数
 * @returns {Promise<any>} 事务执行结果
 */
const transaction = async (callback) => {
  try {
    // Supabase会自动处理事务，这里主要用于兼容现有代码
    const result = await callback(supabase);
    return result;
  } catch (error) {
    console.error('事务执行错误:', error);
    throw error;
  }
};

module.exports = {
  supabase,
  getConnection,
  query,
  execute,
  getLastInsertId,
  transaction
};