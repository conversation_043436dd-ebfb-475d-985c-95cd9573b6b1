# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

- **Start development**: `npm run dev` (uses nodemon for auto-restart)
- **Start production**: `npm start` (runs server.js directly)
- **Install dependencies**: `npm install`
- **Build**: `npm run build` (echoes build completion)
- **Test deployment locally**: Use Vercel CLI: `vercel dev`

## Architecture Overview

This is a Student Class Management System (班级成绩管理系统 V2.0) built with:

- **Backend**: Node.js + Express.js
- **Database**: Supabase PostgreSQL (cloud-hosted)
- **Frontend**: Vanilla HTML/CSS/JavaScript with Bootstrap 5.3.0
- **Deployment**: Vercel (serverless)

### Key Architecture Patterns

- **MVC Structure**: Controllers handle business logic, routes define endpoints, config manages database connections
- **Supabase Integration**: Uses `@supabase/supabase-js` client with service role key for admin operations
- **JWT Authentication**: Token-based auth for teacher/admin sessions with fallback secret
- **Role-based Access Control**: Admin can manage everything, teachers have limited class access
- **Serverless Deployment**: Designed for Vercel with proper environment handling and read-only filesystem considerations
- **Multi-tenant Support**: School-based organization with hierarchical permissions

### Core Components

- **Server Entry**: `server.js` - Express app configuration, routing, and performance monitoring
- **Database**: `config/db.js` - Supabase client setup with connection testing and error handling
- **API Gateway**: `api/index.js` - Vercel serverless function entry point
- **Routes**: 
  - `routes/api.js` - Main API endpoints with JWT middleware
  - `routes/teacher.js` - Teacher-specific routes
- **Controllers**: Modular business logic handlers
  - `studentController.js` - Student management with role-based access
  - `typingController.js` - Typing speed records and rankings
  - `medalController.js` - Medal/award system
  - `authController.js` - Login/registration with bcryptjs hashing
  - `sessionController.js` - Student login sessions
  - `logController.js` - System activity logging
  - `teacherController.js` - Teacher management and permissions
  - `schoolController.js` - Multi-school management

### Database Schema

Uses PostgreSQL with these main tables:
- `users` - Teacher/admin accounts with email support
- `students` - Student records with grade/class info and school references
- `typing_records` - Individual typing test results with language support
- `typing_best` - Best typing scores per student per language
- `medals` - Student awards/medals with foreign key constraints
- `student_sessions` - Student login tracking with session tokens
- `logs` - System operation logs with JSONB metadata
- `schools` - School management (multi-tenant support)
- `articles` - Typing practice content with difficulty levels

Database initialization is handled through:
- `database_init.sql` - Complete schema with sample data
- `supabase/migrations/` - Incremental migration files for schema updates

### Frontend Structure

- **Teacher Interface**: `/index.html` - Student and performance management with Chart.js visualizations
- **Student Interface**: `/student.html` - Typing practice and personal stats
- **Admin Interface**: `/gl.html` - System administration
- **Teacher Management**: `/teacher/new` - Modern teacher management system
- **Styles**: Bootstrap 5.3.0 + custom CSS in `/public/css/`
- **Scripts**: Modular JavaScript in `/public/js/`
  - `db.js` - Database connection and API utilities
  - `student.js` - Student interface logic
  - `typing.js` - Typing practice functionality
  - `main.js` - Teacher interface logic
  - Libraries: jQuery 3.6.0, ECharts, XLSX for Excel export

### Environment Configuration

Required environment variables:
```
SUPABASE_URL=https://xxx.supabase.co
SUPABASE_SERVICE_ROLE_KEY=xxx
JWT_SECRET=xxx
PORT=3001 (optional, defaults to 3001)
NODE_ENV=production (for Vercel)
VERCEL=1 (automatically set in Vercel environment)
```

### Development Notes

- Database migrations are in `supabase/migrations/` directory with numbered sequence
- Uses service role key for admin operations (bypasses RLS)
- Vercel deployment configured via `vercel.json` with function rewrites
- Log files are disabled in Vercel environment (read-only filesystem) with fallback to console
- Performance monitoring with slow request logging (>500ms)
- Frontend uses modern ES6+ features and jQuery 3.6.0
- ECharts library for data visualization and XLSX for Excel operations
- Health check endpoints: `/api/ping` and `/api/db-test`
- Admin account creation endpoint: `/api/create-admin`

### API Structure

All API endpoints are prefixed with `/api/`:
- **Health**: `/api/ping`, `/api/db-test`
- **Authentication**: `/api/login`, `/api/register`
- **Students**: `/api/students/*`, `/api/admin/students/*`
- **Typing**: `/api/typing-records/*`
- **Medals**: `/api/medals/*`
- **Sessions**: `/api/student-sessions/*`
- **Admin routes**: `/api/admin/*` for system management
- **Teacher routes**: Separate teacher-specific endpoints

JWT middleware (`authenticateToken`) protects secure endpoints with Bearer token format.

### Deployment Architecture

- **Vercel Serverless**: All routes funnel through `api/index.js`
- **Static Assets**: Served directly from `/public/` directory
- **CORS**: Configured for cross-origin requests
- **Security Headers**: X-Content-Type-Options, X-Frame-Options, X-XSS-Protection
- **Error Handling**: Centralized error middleware with environment-aware responses

The system supports both teacher and admin roles with different permission levels for student and class management, plus multi-school tenant isolation.