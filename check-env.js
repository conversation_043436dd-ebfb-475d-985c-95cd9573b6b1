/**
 * 环境变量检查脚本
 * 用于验证所有必要的环境变量是否正确配置
 */

const dotenv = require('dotenv');
const path = require('path');

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '.env') });

console.log('=== 环境变量检查 ===');

// 必需的环境变量
const requiredEnvVars = [
    'SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY',
    'JWT_SECRET'
];

// 可选的环境变量
const optionalEnvVars = [
    'SUPABASE_ANON_KEY',
    'PORT',
    'NODE_ENV'
];

let allValid = true;

console.log('\n📋 必需的环境变量:');
requiredEnvVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
        console.log(`✅ ${varName}: ${value.substring(0, 20)}...`);
    } else {
        console.log(`❌ ${varName}: 未设置`);
        allValid = false;
    }
});

console.log('\n📋 可选的环境变量:');
optionalEnvVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
        console.log(`✅ ${varName}: ${value}`);
    } else {
        console.log(`⚠️  ${varName}: 未设置 (使用默认值)`);
    }
});

console.log('\n🔍 Supabase配置验证:');

// 验证Supabase URL格式
const supabaseUrl = process.env.SUPABASE_URL;
if (supabaseUrl) {
    if (supabaseUrl.startsWith('https://') && supabaseUrl.includes('.supabase.co')) {
        console.log('✅ Supabase URL格式正确');
    } else {
        console.log('❌ Supabase URL格式可能不正确');
        allValid = false;
    }
} else {
    console.log('❌ Supabase URL未设置');
    allValid = false;
}

// 验证Service Role Key格式
const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
if (serviceKey) {
    if (serviceKey.startsWith('eyJ') && serviceKey.length > 100) {
        console.log('✅ Supabase Service Role Key格式正确');
    } else {
        console.log('❌ Supabase Service Role Key格式可能不正确');
        allValid = false;
    }
} else {
    console.log('❌ Supabase Service Role Key未设置');
    allValid = false;
}

// 验证Anon Key格式（如果设置了）
const anonKey = process.env.SUPABASE_ANON_KEY;
if (anonKey) {
    if (anonKey.startsWith('eyJ') && anonKey.length > 100) {
        console.log('✅ Supabase Anon Key格式正确');
    } else {
        console.log('⚠️  Supabase Anon Key格式可能不正确');
    }
} else {
    console.log('⚠️  Supabase Anon Key未设置，将使用Service Role Key');
}

// 验证JWT Secret
const jwtSecret = process.env.JWT_SECRET;
if (jwtSecret) {
    if (jwtSecret.length >= 32) {
        console.log('✅ JWT Secret长度足够');
    } else {
        console.log('⚠️  JWT Secret长度可能不够安全（建议至少32字符）');
    }
} else {
    console.log('❌ JWT Secret未设置');
    allValid = false;
}

console.log('\n🎯 总结:');
if (allValid) {
    console.log('✅ 所有必需的环境变量都已正确配置！');
    console.log('🚀 系统应该可以正常运行');
} else {
    console.log('❌ 存在配置问题，请检查上述错误');
    console.log('📝 请确保.env文件包含所有必需的环境变量');
}

console.log('\n📖 如果需要帮助，请参考README.md文件中的环境变量配置说明');
console.log('=== 检查完成 ===\n');

// 如果是直接运行此脚本，则退出
if (require.main === module) {
    process.exit(allValid ? 0 : 1);
}

module.exports = { allValid };
