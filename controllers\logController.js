/**
 * 日志控制器
 * 处理系统日志记录和检索
 */

const db = require('../config/db');

/**
 * 获取日志列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.getLogs = async (req, res) => {
  try {
    const { action, startDate, endDate, limit, offset } = req.query;
    
    // 构建查询条件
    const conditions = [];
    const params = [];
    
    if (action) {
      conditions.push('action = ?');
      params.push(action);
    }
    
    if (startDate) {
      conditions.push('created_at >= ?');
      params.push(new Date(startDate));
    }
    
    if (endDate) {
      conditions.push('created_at <= ?');
      params.push(new Date(endDate));
    }
    
    // 构建查询语句
    let sql = 'SELECT * FROM logs';
    if (conditions.length > 0) {
      sql += ' WHERE ' + conditions.join(' AND ');
    }
    sql += ' ORDER BY created_at DESC';
    
    // 添加分页
    const pageLimit = limit ? parseInt(limit) : 100;
    const pageOffset = offset ? parseInt(offset) : 0;
    sql += ' LIMIT ? OFFSET ?';
    params.push(pageLimit, pageOffset);
    
    // 执行查询
    const logs = await db.query(sql, params);
    
    // 获取总记录数
    let countSql = 'SELECT COUNT(*) as count FROM logs';
    if (conditions.length > 0) {
      countSql += ' WHERE ' + conditions.join(' AND ');
    }
    
    const countResult = await db.query(countSql, params.slice(0, -2));
    const total = countResult[0].count;
    
    res.status(200).json({
      data: {
        logs,
        pagination: {
          total,
          limit: pageLimit,
          offset: pageOffset,
          pages: Math.ceil(total / pageLimit)
        }
      }
    });
  } catch (error) {
    console.error('获取日志错误:', error);
    res.status(500).json({ error: '获取日志失败: ' + error.message });
  }
};

/**
 * 添加日志记录
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.addLog = async (req, res) => {
  try {
    const { action, details } = req.body;
    
    // 验证必填字段
    if (!action || !details) {
      return res.status(400).json({ error: '操作类型和详情为必填项' });
    }
    
    // 插入日志
    await db.execute(
      'INSERT INTO logs (action, details, created_at) VALUES (?, ?, NOW())',
      [action, details]
    );
    
    // 获取新日志
    const newLog = await db.query(
      'SELECT * FROM logs ORDER BY id DESC LIMIT 1'
    );
    
    res.status(201).json({ data: newLog[0] });
  } catch (error) {
    console.error('添加日志错误:', error);
    res.status(500).json({ error: '添加日志失败: ' + error.message });
  }
};

/**
 * 删除日志
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.deleteLogs = async (req, res) => {
  try {
    const { before } = req.body;
    
    // 验证必填字段
    if (!before) {
      return res.status(400).json({ error: '日期参数为必填项' });
    }
    
    const beforeDate = new Date(before);
    
    // 检查日期是否有效
    if (isNaN(beforeDate.getTime())) {
      return res.status(400).json({ error: '日期格式无效' });
    }
    
    // 删除指定日期之前的日志
    const result = await db.execute(
      'DELETE FROM logs WHERE created_at < ?',
      [beforeDate]
    );
    
    const deletedCount = result.affectedRows;
    
    res.status(200).json({
      data: {
        message: `已删除${deletedCount}条日志记录`,
        deletedCount
      }
    });
  } catch (error) {
    console.error('删除日志错误:', error);
    res.status(500).json({ error: '删除日志失败: ' + error.message });
  }
}; 