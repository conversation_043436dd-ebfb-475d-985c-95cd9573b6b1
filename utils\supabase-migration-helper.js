/**
 * Supabase迁移辅助工具
 * 用于快速转换MySQL查询为Supabase查询
 */

/**
 * 将MySQL的IFNULL转换为PostgreSQL的COALESCE
 * @param {string} sql - 原始SQL语句
 * @returns {string} 转换后的SQL语句
 */
function convertIfnullToCoalesce(sql) {
  return sql.replace(/IFNULL\s*\(/gi, 'COALESCE(');
}

/**
 * 将MySQL的LIMIT语法转换为PostgreSQL语法
 * @param {string} sql - 原始SQL语句
 * @returns {string} 转换后的SQL语句
 */
function convertLimitSyntax(sql) {
  // MySQL: LIMIT offset, count
  // PostgreSQL: LIMIT count OFFSET offset
  return sql.replace(/LIMIT\s+(\d+)\s*,\s*(\d+)/gi, 'LIMIT $2 OFFSET $1');
}

/**
 * 将MySQL的ON DUPLICATE KEY UPDATE转换为PostgreSQL的ON CONFLICT
 * @param {string} sql - 原始SQL语句
 * @returns {string} 转换后的SQL语句
 */
function convertOnDuplicateKey(sql) {
  // 这个转换比较复杂，需要根据具体情况处理
  return sql.replace(/ON DUPLICATE KEY UPDATE/gi, 'ON CONFLICT DO UPDATE SET');
}

/**
 * 通用的MySQL到PostgreSQL语法转换
 * @param {string} sql - 原始SQL语句
 * @returns {string} 转换后的SQL语句
 */
function convertMySQLToPostgreSQL(sql) {
  let convertedSql = sql;
  
  // 转换IFNULL为COALESCE
  convertedSql = convertIfnullToCoalesce(convertedSql);
  
  // 转换LIMIT语法
  convertedSql = convertLimitSyntax(convertedSql);
  
  // 转换ON DUPLICATE KEY UPDATE
  convertedSql = convertOnDuplicateKey(convertedSql);
  
  // 转换NOW()为CURRENT_TIMESTAMP
  convertedSql = convertedSql.replace(/NOW\(\)/gi, 'CURRENT_TIMESTAMP');
  
  return convertedSql;
}

/**
 * 创建Supabase查询的辅助函数
 */
const SupabaseQueryHelper = {
  /**
   * 获取单个记录
   */
  async findOne(supabase, table, conditions) {
    let query = supabase.from(table).select('*');
    
    Object.entries(conditions).forEach(([key, value]) => {
      query = query.eq(key, value);
    });
    
    const { data, error } = await query.single();
    return { data, error };
  },

  /**
   * 获取多个记录
   */
  async findMany(supabase, table, conditions = {}, options = {}) {
    let query = supabase.from(table).select(options.select || '*');
    
    Object.entries(conditions).forEach(([key, value]) => {
      query = query.eq(key, value);
    });
    
    if (options.orderBy) {
      query = query.order(options.orderBy.column, { 
        ascending: options.orderBy.ascending !== false 
      });
    }
    
    if (options.limit) {
      query = query.limit(options.limit);
    }
    
    const { data, error } = await query;
    return { data, error };
  },

  /**
   * 插入记录
   */
  async insert(supabase, table, data) {
    const { data: result, error } = await supabase
      .from(table)
      .insert(data)
      .select()
      .single();
    
    return { data: result, error };
  },

  /**
   * 更新记录
   */
  async update(supabase, table, conditions, data) {
    let query = supabase.from(table).update(data);
    
    Object.entries(conditions).forEach(([key, value]) => {
      query = query.eq(key, value);
    });
    
    const { data: result, error } = await query.select().single();
    return { data: result, error };
  },

  /**
   * 删除记录
   */
  async delete(supabase, table, conditions) {
    let query = supabase.from(table).delete();
    
    Object.entries(conditions).forEach(([key, value]) => {
      query = query.eq(key, value);
    });
    
    const { error } = await query;
    return { error };
  },

  /**
   * Upsert操作（插入或更新）
   */
  async upsert(supabase, table, data, conflictColumns) {
    const { data: result, error } = await supabase
      .from(table)
      .upsert(data, { 
        onConflict: conflictColumns.join(','),
        ignoreDuplicates: false 
      })
      .select()
      .single();
    
    return { data: result, error };
  }
};

module.exports = {
  convertMySQLToPostgreSQL,
  convertIfnullToCoalesce,
  convertLimitSyntax,
  convertOnDuplicateKey,
  SupabaseQueryHelper
};
