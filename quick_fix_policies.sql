-- 🚨 快速修复：删除现有策略并重新创建
-- 请在Supabase控制台的SQL编辑器中执行

-- ==========================================
-- 第一步：删除所有现有策略
-- ==========================================

-- 删除学生表策略
DROP POLICY IF EXISTS "学生数据查看策略" ON students;
DROP POLICY IF EXISTS "学生数据插入策略" ON students;
DROP POLICY IF EXISTS "学生数据更新策略" ON students;
DROP POLICY IF EXISTS "学生数据删除策略" ON students;

-- 删除奖章表策略
DROP POLICY IF EXISTS "奖章数据查看策略" ON medals;
DROP POLICY IF EXISTS "奖章数据修改策略" ON medals;
DROP POLICY IF EXISTS "奖章数据更新策略" ON medals;
DROP POLICY IF EXISTS "奖章数据删除策略" ON medals;

-- 删除打字记录表策略
DROP POLICY IF EXISTS "打字记录查看策略" ON typing_records;
DROP POLICY IF EXISTS "打字记录插入策略" ON typing_records;
DROP POLICY IF EXISTS "打字记录更新策略" ON typing_records;
DROP POLICY IF EXISTS "打字记录删除策略" ON typing_records;

-- 删除学校表策略
DROP POLICY IF EXISTS "学校数据查看策略" ON schools;
DROP POLICY IF EXISTS "学校数据插入策略" ON schools;
DROP POLICY IF EXISTS "学校数据更新策略" ON schools;
DROP POLICY IF EXISTS "学校数据删除策略" ON schools;

-- 删除用户表策略
DROP POLICY IF EXISTS "用户查看自己信息策略" ON users;
DROP POLICY IF EXISTS "管理员用户插入策略" ON users;
DROP POLICY IF EXISTS "管理员用户更新策略" ON users;
DROP POLICY IF EXISTS "管理员用户删除策略" ON users;

-- 删除教师权限表策略
DROP POLICY IF EXISTS "教师权限查看策略" ON teacher_class_permissions;
DROP POLICY IF EXISTS "教师权限插入策略" ON teacher_class_permissions;
DROP POLICY IF EXISTS "教师权限更新策略" ON teacher_class_permissions;
DROP POLICY IF EXISTS "教师权限删除策略" ON teacher_class_permissions;

-- 删除文章表策略
DROP POLICY IF EXISTS "文章查看策略" ON articles;
DROP POLICY IF EXISTS "文章插入策略" ON articles;
DROP POLICY IF EXISTS "文章更新策略" ON articles;
DROP POLICY IF EXISTS "文章删除策略" ON articles;

-- 删除其他表策略
DROP POLICY IF EXISTS "日志查看策略" ON logs;
DROP POLICY IF EXISTS "年级配置查看策略" ON school_grade_configs;
DROP POLICY IF EXISTS "导入日志查看策略" ON student_import_logs;
DROP POLICY IF EXISTS "学生会话查看策略" ON student_sessions;
DROP POLICY IF EXISTS "学生登录查看策略" ON student_signins;
DROP POLICY IF EXISTS "教师班级查看策略" ON teacher_classes;
DROP POLICY IF EXISTS "教师学校分配查看策略" ON teacher_school_assignments;
DROP POLICY IF EXISTS "最佳打字记录查看策略" ON typing_best;

-- ==========================================
-- 第二步：重新创建核心策略
-- ==========================================

-- 学生表策略
CREATE POLICY "学生数据查看策略" ON students
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "学生数据插入策略" ON students
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "学生数据更新策略" ON students
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

CREATE POLICY "学生数据删除策略" ON students
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()::text::integer
            AND users.role = 'admin'
        )
    );

-- 奖章表策略
CREATE POLICY "奖章数据查看策略" ON medals
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "奖章数据修改策略" ON medals
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "奖章数据更新策略" ON medals
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "奖章数据删除策略" ON medals
    FOR DELETE USING (auth.role() = 'authenticated');

-- 打字记录表策略
CREATE POLICY "打字记录查看策略" ON typing_records
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "打字记录插入策略" ON typing_records
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "打字记录更新策略" ON typing_records
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "打字记录删除策略" ON typing_records
    FOR DELETE USING (auth.role() = 'authenticated');

-- 学校表策略
CREATE POLICY "学校数据查看策略" ON schools
    FOR SELECT USING (auth.role() = 'authenticated');

-- 用户表策略
CREATE POLICY "用户查看自己信息策略" ON users
    FOR SELECT USING (id = auth.uid()::text::integer);

-- 教师权限表策略
CREATE POLICY "教师权限查看策略" ON teacher_class_permissions
    FOR SELECT USING (
        teacher_id = auth.uid()::text::integer OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'admin'
        )
    );

-- 文章表策略
CREATE POLICY "文章查看策略" ON articles
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "文章插入策略" ON articles
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "文章更新策略" ON articles
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "文章删除策略" ON articles
    FOR DELETE USING (auth.role() = 'authenticated');

-- ==========================================
-- 完成提示
-- ==========================================

SELECT 
    '✅ 策略冲突已解决' as 状态,
    '核心安全策略已重新创建' as 说明,
    '请测试应用功能' as 下一步;
