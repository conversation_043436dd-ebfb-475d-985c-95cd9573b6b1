# 开发规范和规则

- 管理后台功能优化要求：教师管理增强（任教学校多选、所教年级多选、任课班级动态显示、重置密码、删除功能），学校管理简化（只保留名称字段），学生管理增强（所属学校、任课教师匹配），移除班级管理和权限分配菜单，所有操作使用Bootstrap模态框，实时同步Supabase数据库
- 用户要求修改打字管理功能：1.将URL后缀改为/type 2.将数据库从MySQL迁移到Supabase 3.在筛选条件中添加学校选项 4.实现教师权限控制，只能查看本人所教学生的打字成绩，逻辑与奖章管理一致
- 用户要求删除打字管理中的增加、减少功能，学生打字速度不需要教师手动增减，教师只需要查看数据即可
- 用户要求：1.优化筛选功能用户体验，减少每次点击都显示加载中的问题 2.创建学生端独立HTML页面，URL为/stusign（签到界面）和/stutype（打字练习界面），从原有index.html中分离学生功能
- 教师端学生管理功能修复：1.修复API返回数据格式不一致问题（统一返回数组格式）2.增强前端数据字段兼容性处理（支持多种字段名称）3.添加详细的调试日志帮助诊断权限问题 4.创建权限修复脚本fix_teacher_permissions.sql自动分配教师权限 5.创建API测试脚本test_teacher_api.js验证功能 6.修复编辑功能中的数据字段映射问题
- 教师端学生管理显示问题：添加学生使用teacher_class_permissions表权限检查，获取学生列表使用teacher_school_assignments表权限过滤，两表数据不同步导致添加的学生无法显示。需要统一权限查询逻辑或确保两表数据一致性。
- 管理员教师管理功能完善：1.在教师列表中显示每个教师的任教学校和年级班级信息 2.在编辑教师时可以配置和修改任教信息（学校、年级、班级） 3.显示教师自己已配置的权限，并允许管理员查看和修改 4.所有功能都需要实现
- 安全规则：所有管理员API路由必须添加authenticateToken中间件进行身份验证，并在需要时添加管理员权限检查(req.user.role === 'admin')，防止未授权访问
- 教师管理重置密码功能修复：PUT /api/admin/teachers/:id 路由缺少authenticateToken中间件和管理员权限检查，导致500错误。需要添加身份验证和权限验证。
- 教师端筛选功能修复：1.学校筛选器必须基于教师权限表(teacher_school_assignments)获取学校列表，而不是基于当前数据 2.年级数据格式必须统一为数字格式，API调用时禁止使用中文年级 3.前端年级显示可以使用中文，但传递给后端的参数必须是数字 4.增强错误处理避免格式转换错误
- 静态资源路径修复：所有HTML文件中的CSS和JS文件引用必须使用绝对路径（以/开头），避免在子路径访问时出现404错误。相对路径会导致浏览器在/teacher/type等子路径下错误解析为/teacher/css/style.css
- JavaScript函数引用错误修复：在移除事件监听器时，必须确保引用的函数名称正确存在，否则会导致ReferenceError。应该注释掉或删除不存在的函数引用
- 数据保护规则：禁止在控制台输出包含学生姓名、学号、成绩等敏感信息的完整数据。只能输出统计信息（如记录数量）和数据结构信息。使用Utils.safeLog()函数安全地输出日志，避免敏感信息泄露
- 安全修复规则：1.禁止硬编码密码和密钥 2.JWT_SECRET必须通过环境变量设置且不能使用默认值 3.诊断端点不能暴露敏感数据 4.前端配置文件不能包含凭据信息 5.使用security-check.js定期检查安全配置 6.使用database_init_secure.sql进行安全的数据库初始化
- 数据库安全规则：1.Supabase数据表必须启用RLS行级安全 2.禁止使用unrestricted设置 3.后端日志不能输出学生真实姓名，只能使用student_identifier 4.前端控制台不能显示完整的学生个人信息 5.必须为每个表创建适当的安全策略限制访问权限
- 用户要求修改学校删除功能，允许级联删除：删除学校时同时删除关联的学生数据，而不是阻止删除操作
- Vercel环境路由修复：在vercel.json中添加/student/stutype路径配置，解决学生签到后点击开始练习按钮跳转到主界面的问题
- 教师管理界面布局优化：1.在侧边栏底部添加返回选择按钮，回到奖章管理、打字管理等模块选择界面 2.将退出登录按钮移到左下角，与系统管理员界面保持一致的布局风格
- URL路由优化：1.教师注册URL改为/teacher/reg 2.忘记密码URL设为/respass 3.移除教师管理界面返回模块选择的确认对话框，直接跳转 4.更新相关路由配置和链接
- 界面风格统一：注册界面和重置密码界面已修改为与主界面一致的风格，包括glass-effect毛玻璃效果、相同的背景、边框、输入框样式、按钮样式和悬停效果
- 界面风格统一修正完成：1.教师注册页面(/teacher/reg)和重置密码页面(/respass)已完全统一主界面样式 2.添加了与主界面一致的背景网格纹理(body::before伪元素) 3.统一使用login-form类而非register-form或reset-password-form 4.添加了标题下划线效果(h1::after) 5.统一链接容器类名为register-link 6.复选框样式与主界面输入框保持一致 7.所有页面现在具有相同的毛玻璃效果、背景、输入框样式、按钮样式和悬停效果
