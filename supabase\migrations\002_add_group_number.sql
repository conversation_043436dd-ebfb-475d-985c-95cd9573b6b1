-- 添加组号字段到学生表
-- 迁移文件：002_add_group_number.sql

-- 添加组号字段（可选）
ALTER TABLE students 
ADD COLUMN IF NOT EXISTS group_number INTEGER;

-- 添加性别字段（如果不存在）
ALTER TABLE students 
ADD COLUMN IF NOT EXISTS gender VARCHAR(10);

-- 添加座位号字段（如果不存在）
ALTER TABLE students 
ADD COLUMN IF NOT EXISTS seat_number INTEGER;

-- 添加状态字段（如果不存在）
ALTER TABLE students 
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active';

-- 添加索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_students_group ON students(group_number);
CREATE INDEX IF NOT EXISTS idx_students_status ON students(status);
CREATE INDEX IF NOT EXISTS idx_students_school_grade_class ON students(school_id, grade, class);

-- 添加注释
COMMENT ON COLUMN students.group_number IS '组号（可选）';
COMMENT ON COLUMN students.gender IS '性别';
COMMENT ON COLUMN students.seat_number IS '座位号';
COMMENT ON COLUMN students.status IS '状态：active-活跃，inactive-非活跃';
