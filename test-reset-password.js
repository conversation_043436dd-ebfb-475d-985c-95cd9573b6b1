/**
 * 测试重置密码功能
 */

// 使用Node.js内置的fetch API (Node.js 18+)

async function testResetPassword() {
    try {
        console.log('=== 测试重置密码功能 ===');
        
        // 首先登录获取token
        console.log('1. 登录获取token...');
        const loginResponse = await fetch('http://localhost:3005/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: 'admin',
                password: 'admin123'
            })
        });
        
        if (!loginResponse.ok) {
            throw new Error(`登录失败: ${loginResponse.status} ${loginResponse.statusText}`);
        }
        
        const loginResult = await loginResponse.json();
        const token = loginResult.data.token;
        console.log('登录成功，token获取成功');
        
        // 获取教师列表
        console.log('2. 获取教师列表...');
        const teachersResponse = await fetch('http://localhost:3005/api/admin/teachers', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (!teachersResponse.ok) {
            throw new Error(`获取教师列表失败: ${teachersResponse.status} ${teachersResponse.statusText}`);
        }
        
        const teachersResult = await teachersResponse.json();
        console.log('教师列表获取成功，教师数量:', teachersResult.data?.length || 0);
        
        if (!teachersResult.data || teachersResult.data.length === 0) {
            console.log('没有教师数据，无法测试重置密码');
            return;
        }
        
        // 选择第一个教师进行测试
        const teacher = teachersResult.data[0];
        console.log('选择教师进行测试:', teacher.username, teacher.display_name);
        
        // 测试重置密码
        console.log('3. 测试重置密码...');
        const newPassword = Math.random().toString(36).slice(-8);
        console.log('生成的新密码: [已隐藏]');
        
        const resetResponse = await fetch(`http://localhost:3005/api/admin/teachers/${teacher.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                new_password: newPassword
            })
        });
        
        console.log('重置密码响应状态:', resetResponse.status, resetResponse.statusText);
        
        if (!resetResponse.ok) {
            const errorText = await resetResponse.text();
            console.error('重置密码失败，响应内容:', errorText);
            throw new Error(`重置密码失败: ${resetResponse.status} ${resetResponse.statusText}`);
        }
        
        const resetResult = await resetResponse.json();
        console.log('重置密码成功:', resetResult);
        
        console.log('=== 测试完成 ===');
        
    } catch (error) {
        console.error('测试失败:', error.message);
        console.error('详细错误:', error);
    }
}

// 运行测试
testResetPassword();
