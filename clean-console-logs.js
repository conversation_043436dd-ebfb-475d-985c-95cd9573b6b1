/**
 * 清理前端JavaScript文件中的console.log调试语句
 * 保留console.error和console.warn，只删除console.log
 */

const fs = require('fs');
const path = require('path');

// 需要清理的文件列表
const filesToClean = [
    'public/js/teacher-management-new.js',
    'public/js/teacher-permission-management.js',
    'public/js/school-hierarchy-management.js',
    'public/js/typing.js',
    'public/js/db.js',
    'public/js/supabase-client.js',
    'public/js/permission-system-test.js'
];

// 清理单个文件
function cleanFile(filePath) {
    try {
        if (!fs.existsSync(filePath)) {
            console.log(`文件不存在: ${filePath}`);
            return;
        }

        let content = fs.readFileSync(filePath, 'utf8');
        const originalLength = content.length;
        
        // 匹配console.log语句的正则表达式
        // 匹配单行和多行的console.log
        const consoleLogRegex = /console\.log\s*\([^;]*\);?/g;
        
        // 更复杂的正则，处理多行console.log
        const multiLineConsoleLogRegex = /console\.log\s*\(\s*[^)]*(?:\([^)]*\)[^)]*)*\)\s*;?/g;
        
        // 先处理简单的单行console.log
        content = content.replace(consoleLogRegex, '');
        
        // 处理多行console.log（更复杂的情况）
        const lines = content.split('\n');
        const cleanedLines = [];
        let inConsoleLog = false;
        let consoleLogBuffer = '';
        let braceCount = 0;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const trimmedLine = line.trim();
            
            // 检查是否是console.log的开始
            if (trimmedLine.includes('console.log(') && !inConsoleLog) {
                inConsoleLog = true;
                consoleLogBuffer = line;
                
                // 计算括号数量
                braceCount = (line.match(/\(/g) || []).length - (line.match(/\)/g) || []).length;
                
                // 如果这一行就结束了console.log
                if (braceCount <= 0 && (line.includes(');') || line.endsWith(')'))) {
                    inConsoleLog = false;
                    consoleLogBuffer = '';
                    continue; // 跳过这一行
                }
            } else if (inConsoleLog) {
                consoleLogBuffer += '\n' + line;
                braceCount += (line.match(/\(/g) || []).length - (line.match(/\)/g) || []).length;
                
                // 如果括号平衡了，说明console.log结束了
                if (braceCount <= 0 && (line.includes(');') || line.includes('}'))) {
                    inConsoleLog = false;
                    consoleLogBuffer = '';
                    continue; // 跳过这一行
                }
            } else {
                // 不在console.log中，保留这一行
                cleanedLines.push(line);
            }
        }
        
        content = cleanedLines.join('\n');
        
        // 清理多余的空行（连续超过2个空行的情况）
        content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
        
        // 写回文件
        fs.writeFileSync(filePath, content, 'utf8');
        
        const newLength = content.length;
        const reduction = originalLength - newLength;
        
        if (reduction > 0) {
            console.log(`✅ ${filePath}: 清理了 ${reduction} 个字符的调试代码`);
        } else {
            console.log(`✅ ${filePath}: 无需清理`);
        }
        
    } catch (error) {
        console.error(`❌ 清理文件失败 ${filePath}:`, error.message);
    }
}

// 主函数
function main() {
    console.log('🧹 开始清理前端JavaScript文件中的console.log...\n');
    
    let totalCleaned = 0;
    
    filesToClean.forEach(filePath => {
        cleanFile(filePath);
        totalCleaned++;
    });
    
    console.log(`\n🎉 清理完成！共处理了 ${totalCleaned} 个文件`);
    console.log('\n📝 注意事项：');
    console.log('- 只删除了 console.log 语句');
    console.log('- 保留了 console.error 和 console.warn');
    console.log('- 保留了所有功能代码');
    console.log('- 建议测试功能是否正常');
}

// 运行清理
main();
