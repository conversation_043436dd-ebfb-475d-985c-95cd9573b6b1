-- 数据库修复脚本
-- 添加缺少的字段和索引

-- 1. 为students表添加缺少的字段
ALTER TABLE students ADD COLUMN IF NOT EXISTS gender TEXT;
ALTER TABLE students ADD COLUMN IF NOT EXISTS group_number INTEGER;
ALTER TABLE students ADD COLUMN IF NOT EXISTS student_identifier TEXT;

-- 2. 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_students_student_identifier ON students(student_identifier);
CREATE INDEX IF NOT EXISTS idx_students_grade_class ON students(grade, class);
CREATE INDEX IF NOT EXISTS idx_students_school_id ON students(school_id);
CREATE INDEX IF NOT EXISTS idx_students_gender ON students(gender);
CREATE INDEX IF NOT EXISTS idx_students_group_number ON students(group_number);

-- 3. 确保school_grade_configs表存在（如果不存在则创建）
CREATE TABLE IF NOT EXISTS school_grade_configs (
    id SERIAL PRIMARY KEY,
    school_id INTEGER NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
    grade INTEGER NOT NULL CHECK (grade >= 1 AND grade <= 6),
    class_count INTEGER NOT NULL CHECK (class_count >= 1),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(school_id, grade)
);

-- 4. 为school_grade_configs表添加索引
CREATE INDEX IF NOT EXISTS idx_school_grade_configs_school_id ON school_grade_configs(school_id);
CREATE INDEX IF NOT EXISTS idx_school_grade_configs_grade ON school_grade_configs(grade);

-- 5. 确保teacher_school_assignments表存在（如果不存在则创建）
CREATE TABLE IF NOT EXISTS teacher_school_assignments (
    id SERIAL PRIMARY KEY,
    teacher_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    school_id INTEGER NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(teacher_id, school_id)
);

-- 6. 为teacher_school_assignments表添加索引
CREATE INDEX IF NOT EXISTS idx_teacher_school_assignments_teacher_id ON teacher_school_assignments(teacher_id);
CREATE INDEX IF NOT EXISTS idx_teacher_school_assignments_school_id ON teacher_school_assignments(school_id);

-- 7. 添加一些有用的约束和默认值
-- 确保学号唯一（如果有值的话）
CREATE UNIQUE INDEX IF NOT EXISTS idx_students_student_identifier_unique 
ON students(student_identifier) 
WHERE student_identifier IS NOT NULL AND student_identifier != '';

-- 8. 更新现有记录的student_identifier（如果为空）
UPDATE students 
SET student_identifier = CONCAT(grade, LPAD(class::text, 2, '0'), LPAD(id::text, 4, '0'))
WHERE student_identifier IS NULL OR student_identifier = '';

-- 完成
SELECT 'Database migration completed successfully!' as status;
