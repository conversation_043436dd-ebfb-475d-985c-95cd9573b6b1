-- 为users表添加email字段（如果不存在）
-- 这个迁移是为了支持教师注册功能

-- 添加email字段
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'email'
    ) THEN
        ALTER TABLE users ADD COLUMN email VARCHAR(255);
    END IF;
END $$;

-- 添加email字段的索引（可选，用于快速查找）
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- 添加email唯一性约束（可选，如果需要邮箱唯一）
-- ALTER TABLE users ADD CONSTRAINT unique_email UNIQUE (email);
