/**
 * 主应用脚本
 * 处理应用的初始化和导航
 */
document.addEventListener('DOMContentLoaded', function() {
    /**
     * 初始化应用
     */
    function initApp() {
        // 初始化数据库
        DB.init().then(() => {
            // 检查登录状态
            checkLoginStatus();
        }).catch(error => {
            console.error('数据库初始化失败:', error);
            Utils.showMessage('系统初始化失败，请刷新页面重试');
        });
        
        // 注册事件监听
        document.getElementById('logout-btn').addEventListener('click', handleLogout);
        document.getElementById('login-form').addEventListener('submit', handleLogin);
        document.querySelectorAll('.module-card').forEach(card => {
            card.addEventListener('click', handleModuleSelection);
        });
        
        // 初始化示例数据
        Utils.initSampleData();
        
        // 初始化认证系统
        Auth.init();
        
        // 绑定事件
        bindEvents();
    }
    
    /**
     * 绑定事件处理
     */
    function bindEvents() {
        // 绑定模块选择按钮事件
        document.getElementById('medal-btn').addEventListener('click', () => {
            showModule('medal');
        });
        
        document.getElementById('typing-btn').addEventListener('click', () => {
            showModule('typing');
        });
        
        // 绑定返回按钮事件
        document.getElementById('back-btn').addEventListener('click', () => {
            showModuleSelector();
        });
        
        // 绑定重置数据按钮事件
        document.getElementById('reset-data').addEventListener('click', () => {
            Utils.resetAllData();
        });
    }
    
    /**
     * 显示指定模块
     * @param {string} moduleName - 模块名称（medal或typing）
     */
    function showModule(moduleName) {
        // 隐藏选择界面
        document.getElementById('module-selector').style.display = 'none';
        // 显示模块容器
        document.getElementById('module-container').style.display = 'flex';
        
        // 设置模块标题
        const title = moduleName === 'medal' ? '奖章管理' : '打字管理';
        document.getElementById('module-title').textContent = title;
        
        // 隐藏所有模块内容
        document.querySelectorAll('.module-content').forEach(el => {
            el.style.display = 'none';
        });
        
        // 显示指定模块
        if (moduleName === 'medal') {
            document.getElementById('medal-module').style.display = 'block';
            // 初始化奖章模块
            MedalModule.init();
        } else if (moduleName === 'typing') {
            document.getElementById('typing-module').style.display = 'block';
            // 初始化打字模块
            TypingModule.init();
        }
    }
    
    /**
     * 显示模块选择界面
     */
    function showModuleSelector() {
        // 显示选择界面
        document.getElementById('module-selector').style.display = 'flex';
        // 隐藏模块容器
        document.getElementById('module-container').style.display = 'none';
    }
    
    /**
     * 检查登录状态
     */
    function checkLoginStatus() {
        if (Auth.isLoggedIn()) {
            showWelcomeScreen();
        } else {
            showLoginScreen();
        }
    }
    
    /**
     * 处理登录表单提交
     * @param {Event} e - 表单提交事件
     */
    function handleLogin(e) {
        e.preventDefault();
        
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');
        const loginButton = document.getElementById('login-btn');
        
        // 获取输入值
        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();
        
        // 简单校验
        if (!username || !password) {
            Utils.showMessage('请输入用户名和密码');
            return;
        }
        
        // 禁用登录按钮，防止重复点击
        loginButton.disabled = true;
        loginButton.textContent = '登录中...';
        
        // 执行登录
        Auth.login(username, password)
            .then(success => {
                if (success) {
                    // 登录成功，显示欢迎界面
                    showWelcomeScreen();
                    // 重置表单
                    passwordInput.value = '';
                } else {
                    // 登录失败
                    Utils.showMessage('用户名或密码不正确');
                }
            })
            .catch(error => {
                Utils.showMessage('登录失败，请稍后重试');
            })
            .finally(() => {
                // 恢复登录按钮状态
                loginButton.disabled = false;
                loginButton.textContent = '登录';
            });
    }
    
    /**
     * 处理登出操作
     */
    function handleLogout() {
        // 执行登出
        Auth.logout();
        
        // 显示登录界面
        showLoginScreen();
    }
    
    /**
     * 处理模块选择
     * @param {Event} e - 点击事件
     */
    function handleModuleSelection(e) {
        const moduleId = e.currentTarget.getAttribute('data-module');
        
        // 根据模块ID跳转到对应页面
        switch(moduleId) {
            case 'medal':
                window.location.href = 'medal.html';
                break;
            case 'typing':
                window.location.href = 'typing.html';
                break;
            case 'student':
                window.location.href = 'student.html';
                break;
        }
    }
    
    /**
     * 显示登录界面
     */
    function showLoginScreen() {
        document.getElementById('login-container').style.display = 'flex';
        document.getElementById('welcome-container').style.display = 'none';
    }
    
    /**
     * 显示欢迎界面
     */
    function showWelcomeScreen() {
        const user = Auth.getCurrentUser();
        document.getElementById('login-container').style.display = 'none';
        document.getElementById('welcome-container').style.display = 'flex';
        
        // 更新欢迎信息
        if (user) {
            document.getElementById('welcome-message').textContent = `欢迎回来，${user.displayName || user.username}！`;
        }
    }
    
    // 初始化应用
    initApp();
}); 