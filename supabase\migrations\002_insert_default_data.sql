-- 插入默认用户数据
-- 注意：在生产环境中，密码应该使用bcrypt等方式加密

-- 插入默认管理员账号
INSERT INTO users (username, password, role, display_name)
VALUES ('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', '系统管理员')
ON CONFLICT (username) DO UPDATE SET
  password = EXCLUDED.password,
  role = EXCLUDED.role,
  display_name = EXCLUDED.display_name;

-- 插入默认教师账号
INSERT INTO users (username, password, role, display_name)
VALUES ('teacher', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'teacher', '默认教师')
ON CONFLICT (username) DO UPDATE SET
  password = EXCLUDED.password,
  role = EXCLUDED.role,
  display_name = EXCLUDED.display_name;

-- 插入示例文章数据
INSERT INTO articles (title, content, language, difficulty, grade) VALUES
('中文打字练习 - 基础', '春天来了，花儿开了，鸟儿在枝头歌唱。小朋友们在公园里快乐地玩耍，他们有的在踢球，有的在跳绳，还有的在画画。', 'zh', 1, 4),
('中文打字练习 - 进阶', '科技的发展改变了我们的生活方式。现在我们可以通过互联网与世界各地的人们交流，可以在线学习各种知识，也可以享受便捷的购物体验。', 'zh', 2, 5),
('English Typing Practice - Basic', 'The quick brown fox jumps over the lazy dog. This sentence contains all the letters of the alphabet and is commonly used for typing practice.', 'en', 1, 4),
('English Typing Practice - Advanced', 'Technology has revolutionized the way we communicate, learn, and work. From smartphones to artificial intelligence, these innovations continue to shape our future.', 'en', 2, 6);

-- 插入示例学生数据
INSERT INTO students (student_identifier, name, grade, class) VALUES
('4_1_张三', '张三', 4, 1),
('4_1_李四', '李四', 4, 1),
('4_2_王五', '王五', 4, 2),
('5_1_赵六', '赵六', 5, 1),
('5_2_钱七', '钱七', 5, 2),
('6_1_孙八', '孙八', 6, 1);

-- 为示例学生插入奖章数据
INSERT INTO medals (student_identifier, count) VALUES
('4_1_张三', 3),
('4_1_李四', 1),
('4_2_王五', 2),
('5_1_赵六', 5),
('5_2_钱七', 2),
('6_1_孙八', 4);

-- 插入示例打字记录
INSERT INTO typing_records (student_identifier, speed, accuracy, language) VALUES
('4_1_张三', 45, 95.50, 'zh'),
('4_1_张三', 52, 98.20, 'zh'),
('4_1_李四', 38, 92.10, 'zh'),
('4_2_王五', 41, 96.80, 'zh'),
('5_1_赵六', 68, 97.50, 'zh'),
('5_2_钱七', 55, 94.20, 'zh'),
('6_1_孙八', 72, 98.90, 'zh');

-- 插入最佳成绩记录
INSERT INTO typing_best (student_identifier, language, best_speed, record_date) VALUES
('4_1_张三', 'zh', 52, NOW()),
('4_1_李四', 'zh', 38, NOW()),
('4_2_王五', 'zh', 41, NOW()),
('5_1_赵六', 'zh', 68, NOW()),
('5_2_钱七', 'zh', 55, NOW()),
('6_1_孙八', 'zh', 72, NOW());
