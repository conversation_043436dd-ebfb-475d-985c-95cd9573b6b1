-- 班级成绩管理系统V2.0 - 简化数据库修复脚本
-- 解决学校添加功能失败问题（不使用RLS）
-- 请在Supabase SQL Editor中执行此脚本

-- ==================== 第一部分：清理冗余表结构 ====================

-- 1. 删除冗余的teacher_schools表（如果存在）
DROP TABLE IF EXISTS teacher_schools CASCADE;

-- 2. 确保teacher_school_assignments表存在且结构正确
CREATE TABLE IF NOT EXISTS teacher_school_assignments (
  id SERIAL PRIMARY KEY,
  teacher_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  school_id INTEGER NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(teacher_id, school_id)
);

-- 3. 确保teacher_class_permissions表存在
CREATE TABLE IF NOT EXISTS teacher_class_permissions (
  id SERIAL PRIMARY KEY,
  teacher_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  school_id INTEGER NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
  grade INTEGER NOT NULL,
  class INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(teacher_id, school_id, grade, class)
);

-- 4. 确保school_grade_configs表存在
CREATE TABLE IF NOT EXISTS school_grade_configs (
  id SERIAL PRIMARY KEY,
  school_id INTEGER NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
  grade INTEGER NOT NULL,
  class_count INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(school_id, grade)
);

-- ==================== 第二部分：禁用RLS（使用应用层权限控制） ====================

-- 禁用所有表的RLS，使用service role key和应用层权限控制
ALTER TABLE schools DISABLE ROW LEVEL SECURITY;
ALTER TABLE students DISABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_school_assignments DISABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_class_permissions DISABLE ROW LEVEL SECURITY;
ALTER TABLE school_grade_configs DISABLE ROW LEVEL SECURITY;
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE typing_records DISABLE ROW LEVEL SECURITY;
ALTER TABLE typing_best DISABLE ROW LEVEL SECURITY;
ALTER TABLE medals DISABLE ROW LEVEL SECURITY;
ALTER TABLE student_sessions DISABLE ROW LEVEL SECURITY;
ALTER TABLE logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE articles DISABLE ROW LEVEL SECURITY;

-- ==================== 第三部分：创建辅助函数 ====================

-- 1. 创建自动分配学校权限的函数
CREATE OR REPLACE FUNCTION assign_school_to_teacher(teacher_id INTEGER, school_id INTEGER)
RETURNS BOOLEAN AS $$
BEGIN
  INSERT INTO teacher_school_assignments (teacher_id, school_id)
  VALUES (teacher_id, school_id)
  ON CONFLICT (teacher_id, school_id) DO NOTHING;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. 创建检查用户是否为管理员的函数
CREATE OR REPLACE FUNCTION is_admin(user_id INTEGER)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = user_id 
    AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ==================== 第四部分：创建索引优化 ====================

-- 创建必要的索引
CREATE INDEX IF NOT EXISTS idx_teacher_school_assignments_teacher ON teacher_school_assignments(teacher_id);
CREATE INDEX IF NOT EXISTS idx_teacher_school_assignments_school ON teacher_school_assignments(school_id);
CREATE INDEX IF NOT EXISTS idx_teacher_class_permissions_teacher ON teacher_class_permissions(teacher_id);
CREATE INDEX IF NOT EXISTS idx_teacher_class_permissions_school ON teacher_class_permissions(school_id);
CREATE INDEX IF NOT EXISTS idx_school_grade_configs_school ON school_grade_configs(school_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_schools_name ON schools(name);

-- ==================== 第五部分：数据完整性检查 ====================

-- 确保默认用户存在
INSERT INTO users (username, password, display_name, role) 
VALUES 
  ('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 'admin'),
  ('teacher', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '教师用户', 'teacher')
ON CONFLICT (username) DO NOTHING;

-- 确保默认学校存在
INSERT INTO schools (name, address, contact_phone) 
VALUES 
  ('示例小学', '北京市朝阳区示例路123号', '010-12345678'),
  ('测试中学', '上海市浦东新区测试大道456号', '021-87654321')
ON CONFLICT (name) DO NOTHING;

-- 为默认教师分配学校权限
DO $$
DECLARE
    teacher_user_id INTEGER;
    school1_id INTEGER;
BEGIN
    SELECT id INTO teacher_user_id FROM users WHERE username = 'teacher' AND role = 'teacher';
    SELECT id INTO school1_id FROM schools WHERE name = '示例小学';
    
    IF teacher_user_id IS NOT NULL AND school1_id IS NOT NULL THEN
        INSERT INTO teacher_school_assignments (teacher_id, school_id) 
        VALUES (teacher_user_id, school1_id)
        ON CONFLICT (teacher_id, school_id) DO NOTHING;
    END IF;
END $$;

-- ==================== 第六部分：清理旧的策略和触发器 ====================

-- 删除可能存在的旧RLS策略
DROP POLICY IF EXISTS "Allow authenticated users to view schools" ON schools;
DROP POLICY IF EXISTS "Allow authenticated users to insert schools" ON schools;
DROP POLICY IF EXISTS "Allow authorized users to update schools" ON schools;
DROP POLICY IF EXISTS "Allow admin to delete schools" ON schools;
DROP POLICY IF EXISTS "Users can view own school assignments" ON teacher_school_assignments;
DROP POLICY IF EXISTS "Allow system to insert school assignments" ON teacher_school_assignments;
DROP POLICY IF EXISTS "Teachers can view students from their schools" ON students;
DROP POLICY IF EXISTS "Teachers can insert students to their schools" ON students;

-- 删除可能存在的旧触发器
DROP TRIGGER IF EXISTS trigger_auto_assign_school_permission ON schools;

-- 删除可能存在的旧函数
DROP FUNCTION IF EXISTS auto_assign_school_permission();
DROP FUNCTION IF EXISTS get_current_user_id();

-- 完成提示
SELECT '简化数据库修复脚本执行完成！' as message,
       '学校添加功能应该已经修复。' as status,
       '权限控制现在在应用层处理。' as note;
