/**
 * 增强的管理员界面 - 教师权限管理功能
 * 支持教师-学校-班级的层级权限管理
 */

// 权限管理相关的全局变量
let teacherPermissions = {};
let schoolConfigs = {};
let currentEditingTeacher = null;

/**
 * 更新的添加教师模态框事件处理
 */
document.addEventListener('DOMContentLoaded', function() {
    // 教师学校选择变化处理
    const teacherSchoolsSelect = document.getElementById('teacherSchools');
    if (teacherSchoolsSelect) {
        teacherSchoolsSelect.addEventListener('change', handleTeacherSchoolSelection);
    }

    const editTeacherSchoolsSelect = document.getElementById('editTeacherSchools');
    if (editTeacherSchoolsSelect) {
        editTeacherSchoolsSelect.addEventListener('change', handleEditTeacherSchoolSelection);
    }
    
    // 加载学校数据到选择框
    loadSchoolsToSelect();
});

/**
 * 加载学校数据到选择框
 */
async function loadSchoolsToSelect() {
    try {
        const response = await fetch('/api/admin/schools', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (response.ok) {
            const result = await response.json();
            const schools = result.data || [];

            // 更新添加教师模态框中的学校选择
            const addSchoolSelect = document.getElementById('teacherSchools');
            if (addSchoolSelect) {
                addSchoolSelect.innerHTML = schools.map(school => 
                    `<option value="${school.id}">${school.name}</option>`
                ).join('');
            }

            // 更新编辑教师模态框中的学校选择
            const editSchoolSelect = document.getElementById('editTeacherSchools');
            if (editSchoolSelect) {
                editSchoolSelect.innerHTML = schools.map(school => 
                    `<option value="${school.id}">${school.name}</option>`
                ).join('');
            }

            // 保存学校数据
            allSchools = schools;
        }
    } catch (error) {
        console.error('加载学校数据失败:', error);
    }
}

/**
 * 处理教师学校选择变化（添加教师）
 */
async function handleTeacherSchoolSelection() {
    const selectedSchools = Array.from(document.getElementById('teacherSchools').selectedOptions)
        .map(option => parseInt(option.value));
    
    await generateTeacherAssignmentUI(selectedSchools, 'teacherAssignmentContainer');
}

/**
 * 处理教师学校选择变化（编辑教师）
 */
async function handleEditTeacherSchoolSelection() {
    const selectedSchools = Array.from(document.getElementById('editTeacherSchools').selectedOptions)
        .map(option => parseInt(option.value));
    
    await generateTeacherAssignmentUI(selectedSchools, 'editTeacherAssignmentContainer');
}

/**
 * 生成教师任教信息配置UI
 */
async function generateTeacherAssignmentUI(selectedSchools, containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    if (selectedSchools.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-5">
                <div class="mb-3">
                    <i class="fas fa-arrow-up fa-3x text-primary opacity-50"></i>
                </div>
                <h6 class="mb-2">配置任教信息</h6>
                <p class="mb-0 small">请先在上方选择任教学校，系统将为每个学校生成独立的年级班级配置区域</p>
            </div>
        `;
        return;
    }

    // 为每个学校生成配置区域
    let html = '';
    for (const schoolId of selectedSchools) {
        const school = allSchools.find(s => s.id === schoolId);
        if (!school) continue;

        // 获取学校的年级配置
        const schoolConfig = await getSchoolConfig(schoolId);
        
        html += `
            <div class="school-assignment-section mb-4" data-school-id="${schoolId}">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-school me-2"></i>${school.name}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row" id="grades-container-${schoolId}">
                            ${generateGradeClassSelection(schoolId, schoolConfig)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    container.innerHTML = html || `
        <div class="text-center text-muted py-3">
            <p>无法加载学校配置</p>
        </div>
    `;
}

/**
 * 获取学校的年级班级配置（基于教师权限）
 */
async function getSchoolConfig(schoolId) {
    // 如果是编辑教师模式，获取该教师的实际权限
    if (currentEditingTeacher) {
        return await getTeacherSchoolPermissions(currentEditingTeacher.id, schoolId);
    }

    // 如果是添加教师模式，返回学校的完整配置供选择
    try {
        const response = await fetch(`/api/admin/schools/${schoolId}/config`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (response.ok) {
            const result = await response.json();
            const config = result.data || [];
            schoolConfigs[schoolId] = config;
            return config;
        }
    } catch (error) {
        console.error('获取学校配置失败:', error);
    }

    // 如果API调用失败，返回空配置
    schoolConfigs[schoolId] = [];
    return [];
}

/**
 * 获取教师在指定学校的实际权限配置
 */
async function getTeacherSchoolPermissions(teacherId, schoolId) {
    try {
        const response = await fetch(`/api/admin/teachers/${teacherId}/permissions`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (response.ok) {
            const result = await response.json();
            const permissions = result.data?.classes || [];

            // 将权限数据转换为年级配置格式
            const gradeMap = new Map();

            permissions.forEach(perm => {
                if (parseInt(perm.school_id) === parseInt(schoolId)) {
                    const grade = perm.grade;
                    if (!gradeMap.has(grade)) {
                        gradeMap.set(grade, new Set());
                    }
                    gradeMap.get(grade).add(perm.class);
                }
            });

            // 转换为配置格式
            const config = Array.from(gradeMap.entries()).map(([grade, classes]) => ({
                grade: grade,
                class_count: Math.max(...Array.from(classes)) // 使用最大班级号作为班级数量
            })).sort((a, b) => a.grade - b.grade);

            console.log(`教师 ${teacherId} 在学校 ${schoolId} 的权限配置:`, config);
            return config;
        }
    } catch (error) {
        console.error('获取教师权限失败:', error);
    }

    return [];
}

/**
 * 生成年级班级选择HTML
 */
function generateGradeClassSelection(schoolId, schoolConfig) {
    if (!schoolConfig || schoolConfig.length === 0) {
        return `
            <div class="col-12 text-center text-muted py-3">
                <i class="fas fa-upload fa-2x mb-2 text-info"></i>
                <p class="mb-1">该学校暂无年级配置</p>
                <small class="text-muted">请通过Excel导入学生数据来自动创建年级班级配置</small>
            </div>
        `;
    }

    return schoolConfig.map(config => {
        const gradeClasses = Array.from({ length: config.class_count }, (_, i) => i + 1);
        
        return `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card border-secondary">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">${config.grade}年级</h6>
                    </div>
                    <div class="card-body p-2">
                        <div class="form-check-group">
                            ${gradeClasses.map(classNum => `
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input teacher-class-permission" 
                                           type="checkbox" 
                                           id="class-${schoolId}-${config.grade}-${classNum}"
                                           data-school-id="${schoolId}"
                                           data-grade="${config.grade}"
                                           data-class="${classNum}">
                                    <label class="form-check-label" for="class-${schoolId}-${config.grade}-${classNum}">
                                        ${classNum}班
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllClasses(${schoolId}, ${config.grade})">
                                全选
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="clearAllClasses(${schoolId}, ${config.grade})">
                                清空
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

/**
 * 全选指定年级的所有班级
 */
function selectAllClasses(schoolId, grade) {
    const checkboxes = document.querySelectorAll(
        `input[data-school-id="${schoolId}"][data-grade="${grade}"]`
    );
    checkboxes.forEach(cb => cb.checked = true);
}

/**
 * 清空指定年级的所有班级选择
 */
function clearAllClasses(schoolId, grade) {
    const checkboxes = document.querySelectorAll(
        `input[data-school-id="${schoolId}"][data-grade="${grade}"]`
    );
    checkboxes.forEach(cb => cb.checked = false);
}

/**
 * 获取教师权限配置
 */
function getTeacherPermissionConfig() {
    const schools = [];
    const classes = [];

    // 获取选中的学校
    const schoolSelects = document.querySelectorAll('select[id$="Schools"]');
    schoolSelects.forEach(select => {
        if (select.style.display !== 'none') {
            Array.from(select.selectedOptions).forEach(option => {
                const schoolId = parseInt(option.value);
                if (!schools.includes(schoolId)) {
                    schools.push(schoolId);
                }
            });
        }
    });

    // 获取选中的班级
    const classCheckboxes = document.querySelectorAll('.teacher-class-permission:checked');
    classCheckboxes.forEach(cb => {
        classes.push({
            school_id: parseInt(cb.dataset.schoolId),
            grade: parseInt(cb.dataset.grade),
            class: parseInt(cb.dataset.class)
        });
    });

    return { schools, classes };
}

/**
 * 确认添加教师（增强版本）
 */
async function confirmAddTeacher() {
    const username = document.getElementById('teacherUsername').value.trim();
    const password = document.getElementById('teacherPassword').value;
    const displayName = document.getElementById('teacherDisplayName').value.trim();

    if (!username || !password || !displayName) {
        Utils.showMessage('请填写所有必需字段', 'error');
        return;
    }

    // 获取权限配置
    const permissions = getTeacherPermissionConfig();

    const spinner = document.getElementById('addTeacherSpinner');
    if (spinner) {
        spinner.classList.remove('d-none');
    }

    try {
        const response = await fetch('/api/admin/teachers', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                username,
                password,
                display_name: displayName,
                schools: permissions.schools,
                classes: permissions.classes
            })
        });

        const result = await response.json();

        if (response.ok) {
            Utils.showMessage('教师添加成功', 'success');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addTeacherModal'));
            if (modal) modal.hide();
            // 刷新教师列表
            if (currentSection === 'teachers') {
                showAdminSection('teachers');
            }
        } else {
            Utils.showMessage(result.message || '添加教师失败', 'error');
        }
    } catch (error) {
        console.error('添加教师失败:', error);
        Utils.showMessage('添加教师失败，请重试', 'error');
    } finally {
        if (spinner) {
            spinner.classList.add('d-none');
        }
    }
}

/**
 * 编辑教师权限
 */
async function editTeacherPermissions(teacherId) {
    // 设置当前编辑的教师信息
    currentEditingTeacher = { id: teacherId };
    
    try {
        // 获取教师当前权限
        const response = await fetch(`/api/admin/teachers/${teacherId}/permissions`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (response.ok) {
            const result = await response.json();
            const permissions = result.data;
            
            // 设置学校选择
            const schoolSelect = document.getElementById('editTeacherSchools');
            if (schoolSelect) {
                // 清除现有选择
                Array.from(schoolSelect.options).forEach(option => option.selected = false);
                // 设置当前权限
                permissions.schools.forEach(school => {
                    const option = schoolSelect.querySelector(`option[value="${school.school_id}"]`);
                    if (option) option.selected = true;
                });
                
                // 触发学校选择变化
                await handleEditTeacherSchoolSelection();
                
                // 设置班级选择
                setTimeout(() => {
                    permissions.classes.forEach(cls => {
                        const checkbox = document.getElementById(`class-${cls.school_id}-${cls.grade}-${cls.class}`);
                        if (checkbox) checkbox.checked = true;
                    });
                }, 100);
            }
            
            // 显示编辑模态框
            const modal = new bootstrap.Modal(document.getElementById('editTeacherModal'));
            modal.show();
        }
    } catch (error) {
        console.error('获取教师权限失败:', error);
        Utils.showMessage('获取教师权限失败', 'error');
    }
}

/**
 * 确认编辑教师（增强版本）
 */
async function confirmEditTeacher() {
    const displayName = document.getElementById('editTeacherDisplayName').value.trim();
    const newPassword = document.getElementById('editTeacherNewPassword').value;

    if (!displayName) {
        Utils.showMessage('显示名称不能为空', 'error');
        return;
    }

    // 获取权限配置
    const permissions = getTeacherPermissionConfig();

    const spinner = document.getElementById('editTeacherSpinner');
    if (spinner) {
        spinner.classList.remove('d-none');
    }

    try {
        // 更新基本信息
        const updateData = { display_name: displayName };
        if (newPassword.trim()) {
            updateData.new_password = newPassword;
        }

        const response = await fetch(`/api/admin/teachers/${currentEditingTeacher.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify(updateData)
        });

        if (!response.ok) {
            const result = await response.json();
            throw new Error(result.message || '更新教师基本信息失败');
        }

        // 更新权限
        const permissionResponse = await fetch(`/api/admin/teachers/${currentEditingTeacher.id}/permissions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify(permissions)
        });

        if (!permissionResponse.ok) {
            const result = await permissionResponse.json();
            throw new Error(result.message || '更新教师权限失败');
        }

        Utils.showMessage('教师信息更新成功', 'success');
        // 清除当前编辑教师
        currentEditingTeacher = null;
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('editTeacherModal'));
        if (modal) modal.hide();
        // 刷新教师列表
        if (currentSection === 'teachers') {
            showAdminSection('teachers');
        }

    } catch (error) {
        console.error('更新教师失败:', error);
        Utils.showMessage(error.message || '更新教师失败，请重试', 'error');
    } finally {
        if (spinner) {
            spinner.classList.add('d-none');
        }
    }
}

/**
 * 重置教师密码
 * {{ AURA-X: Modify - 更新为支持密码类型选择的完整实现. Approval: 寸止 }}
 */
async function resetTeacherPassword(teacherId, teacherName) {
    document.getElementById('resetPasswordTeacherId').value = teacherId;
    document.getElementById('resetPasswordTeacherName').textContent = teacherName;
    document.getElementById('newPasswordDisplay').classList.add('d-none');
    document.getElementById('passwordInputSection').style.display = 'block';
    document.getElementById('resetPasswordBtn').style.display = 'inline-block';

    // 重置表单
    document.getElementById('customPassword').checked = true;
    document.getElementById('randomPassword').checked = false;
    document.getElementById('newTeacherPassword').value = '';
    document.getElementById('confirmTeacherPassword').value = '';
    document.getElementById('customPasswordInputs').style.display = 'block';
    document.getElementById('randomPasswordNote').classList.add('d-none');

    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();

    // 绑定密码类型选择事件
    document.querySelectorAll('input[name="passwordType"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const customInputs = document.getElementById('customPasswordInputs');
            const randomNote = document.getElementById('randomPasswordNote');

            if (this.value === 'custom') {
                customInputs.style.display = 'block';
                randomNote.classList.add('d-none');
            } else {
                customInputs.style.display = 'none';
                randomNote.classList.remove('d-none');
                // 清空自定义密码输入
                document.getElementById('newTeacherPassword').value = '';
                document.getElementById('confirmTeacherPassword').value = '';
            }
        });
    });
}

// {{ AURA-X: Delete - 删除错误的confirmResetPassword函数，避免与admin-management.js中正确实现冲突. Approval: 寸止 }}
// 注意：confirmResetPassword函数已移至admin-management.js中，使用正确的密码类型选择逻辑

// {{ AURA-X: Delete - 删除重复的copyPassword函数，使用admin-management.js中的版本. Approval: 寸止 }}

// 导出到全局作用域
window.confirmAddTeacher = confirmAddTeacher;
window.confirmEditTeacher = confirmEditTeacher;
window.editTeacherPermissions = editTeacherPermissions;
window.resetTeacherPassword = resetTeacherPassword;
// window.confirmResetPassword = confirmResetPassword; // 已移至admin-management.js
// window.copyPassword = copyPassword; // 已移至admin-management.js
window.selectAllClasses = selectAllClasses;
window.clearAllClasses = clearAllClasses;