/*
 Navicat Premium Dump SQL

 Source Server         : 1
 Source Server Type    : MySQL
 Source Server Version : 80041 (8.0.41)
 Source Host           : localhost:3306
 Source Schema         : syxxstudent

 Target Server Type    : MySQL
 Target Server Version : 80041 (8.0.41)
 File Encoding         : 65001

 Date: 06/04/2025 21:49:04
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for articles
-- ----------------------------
DROP TABLE IF EXISTS `articles`;
CREATE TABLE `articles`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `language` enum('zh','en') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `grade_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `difficulty` tinyint NULL DEFAULT 3,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of articles
-- ----------------------------
INSERT INTO `articles` VALUES (1, '中国传统文化', '中国传统文化是世界文化宝库中的瑰宝，包括儒家思想、道家哲学、中医、书法、绘画等多个方面。', 'zh', '五年级', 3, '2025-04-06 12:46:04', '2025-04-06 12:46:04', 'active');
INSERT INTO `articles` VALUES (2, '科技创新', '科技创新是推动人类社会发展的重要力量。从蒸汽机的发明到互联网的普及，科技的进步改变了人们的生活方式和思维模式。', 'zh', '六年级', 3, '2025-04-06 12:46:04', '2025-04-06 12:46:04', 'active');
INSERT INTO `articles` VALUES (3, '环境保护', '环境保护是当今世界面临的重大挑战之一。随着工业化和城市化进程的加快，环境污染和生态破坏问题日益严重。', 'zh', '五年级', 3, '2025-04-06 12:46:04', '2025-04-06 12:46:04', 'active');
INSERT INTO `articles` VALUES (4, 'Technology and Society', 'Technology has fundamentally changed how we live, work, and interact with each other.', 'en', '六年级', 3, '2025-04-06 12:46:04', '2025-04-06 12:46:04', 'active');
INSERT INTO `articles` VALUES (5, 'Environmental Protection', 'Environmental protection is one of the most significant challenges facing the world today.', 'en', '五年级', 3, '2025-04-06 12:46:04', '2025-04-06 12:46:04', 'active');
INSERT INTO `articles` VALUES (6, 'The Importance of Education', 'Education is the foundation of national development and the ladder of personal growth.', 'en', '六年级', 3, '2025-04-06 12:46:04', '2025-04-06 12:46:04', 'active');
INSERT INTO `articles` VALUES (7, 'teste', 'hello', 'zh', NULL, 3, '2025-04-06 20:04:06', '2025-04-06 20:04:06', 'active');
INSERT INTO `articles` VALUES (8, '比尔·盖茨：我们生来不是为工作 AI将包揽大部分事情', '快科技4月5日消息，据报道，微软联合创始人比尔?盖茨分享了关于AI如何影响工作的有趣见解。\n\n比尔·盖茨表示，我们生来不是为了做工作。工作是短缺时代的产物，所有这些都是基于创造人类智慧而提供广泛的服务。 \n\n在盖茨构想的未来图景中，AI革命将用不足十年的时间完成对传统行业的深度改造。尤其在医疗和教育领域，人工智能系统将承担80%以上的常规诊断、药品研发和知识传授工作。\n\n通过精准的算法优化和24小时不间断运作，AI不仅大幅提升服务效率，更可能彻底改变人类获取医疗资源和教育服务的底层逻辑。\n\n比尔·盖茨最近还表示，还列举了三种不会被AI取代的职业：首先是程序员，尽管GitHub Copilot等AI编程工具已能完成30%的基础代码生成，但复杂系统的架构设计仍需要人类工程师的全局把控。面对千万行级的软件工程，程序员的创造性解决方案和边界突破能力，是AI难以企及的技术制高点。\n\n其次是能源专家，能源领域过于庞大且复杂，尽管AI可以协助分析并提高效率，但在决策和危机管理上，人类专业知识仍然不可替代。\n\n最后是生物学家，即生命探索者，虽然AI可以分析大量数据并协助诊断疾病，但生物学家在医学研究和科学发现领域需要依赖创造力、直觉和批判性思维，这些是AI难以复制的特质。', 'zh', NULL, 4, '2025-04-06 20:47:45', '2025-04-06 20:47:45', 'active');

-- ----------------------------
-- Table structure for attendance
-- ----------------------------
DROP TABLE IF EXISTS `attendance`;
CREATE TABLE `attendance`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `student_identifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '学生标识符，与students表关联',
  `grade` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '年级',
  `class` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '班级',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '学生姓名',
  `attendance_date` date NOT NULL COMMENT '签到日期',
  `seat_number` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '座位号',
  `status` enum('present','absent','late','leave') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'present' COMMENT '签到状态：present-出席，absent-缺席，late-迟到，leave-请假',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '备注信息',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_student`(`student_identifier` ASC) USING BTREE,
  INDEX `idx_date`(`attendance_date` ASC) USING BTREE,
  INDEX `idx_grade_class`(`grade` ASC, `class` ASC) USING BTREE,
  INDEX `idx_attendance_date_status`(`attendance_date` ASC, `status` ASC) USING BTREE,
  INDEX `idx_attendance_student_date`(`student_identifier` ASC, `attendance_date` ASC) USING BTREE,
  CONSTRAINT `fk_attendance_student` FOREIGN KEY (`student_identifier`) REFERENCES `students` (`student_identifier`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '学生签到记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of attendance
-- ----------------------------

-- ----------------------------
-- Table structure for logs
-- ----------------------------
DROP TABLE IF EXISTS `logs`;
CREATE TABLE `logs`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_logs_action`(`action` ASC) USING BTREE,
  INDEX `idx_logs_created`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 111 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of logs
-- ----------------------------
INSERT INTO `logs` VALUES (1, 'login', '用户 admin 登录成功', '2025-04-03 20:45:38');
INSERT INTO `logs` VALUES (2, 'session_start', '学生 4_1_王浩宇 开始会话 1', '2025-04-03 23:23:35');
INSERT INTO `logs` VALUES (3, 'session_end', '学生 4_1_王浩宇 结束会话 1', '2025-04-03 23:23:36');
INSERT INTO `logs` VALUES (4, 'session_start', '学生 5_7_林俊杰 开始会话 2', '2025-04-03 23:30:00');
INSERT INTO `logs` VALUES (5, 'session_end', '学生 5_7_林俊杰 结束会话 2', '2025-04-03 23:30:01');
INSERT INTO `logs` VALUES (6, 'session_start', '学生 4_1_王浩宇 开始会话 3', '2025-04-03 23:34:13');
INSERT INTO `logs` VALUES (7, 'session_end', '学生 4_1_王浩宇 结束会话 3', '2025-04-03 23:49:50');
INSERT INTO `logs` VALUES (8, 'session_start', '学生 4_1_王浩宇 开始会话 4', '2025-04-03 23:49:58');
INSERT INTO `logs` VALUES (9, 'session_end', '学生 4_1_王浩宇 结束会话 4', '2025-04-03 23:50:04');
INSERT INTO `logs` VALUES (10, 'session_start', '学生 4_2_李梓萱 开始会话 5', '2025-04-03 23:50:17');
INSERT INTO `logs` VALUES (11, 'session_end', '学生 4_2_李梓萱 结束会话 5', '2025-04-03 23:54:39');
INSERT INTO `logs` VALUES (12, 'session_start', '学生 4_2_李梓萱 开始会话 6', '2025-04-03 23:54:48');
INSERT INTO `logs` VALUES (13, 'session_end', '学生 4_2_李梓萱 结束会话 6', '2025-04-03 23:58:03');
INSERT INTO `logs` VALUES (14, 'session_start', '学生 4_1_王浩宇 开始会话 7', '2025-04-03 23:58:12');
INSERT INTO `logs` VALUES (15, 'session_end', '学生 4_1_王浩宇 结束会话 7', '2025-04-04 00:16:10');
INSERT INTO `logs` VALUES (16, 'session_start', '学生 4_2_李梓萱 开始会话 8', '2025-04-04 00:16:28');
INSERT INTO `logs` VALUES (17, 'session_end', '学生 4_2_李梓萱 结束会话 8', '2025-04-04 00:18:25');
INSERT INTO `logs` VALUES (18, 'session_start', '学生 4_3_周泽瑞 开始会话 9', '2025-04-04 00:18:58');
INSERT INTO `logs` VALUES (19, 'session_end', '学生 4_3_周泽瑞 结束会话 9', '2025-04-04 00:46:14');
INSERT INTO `logs` VALUES (20, 'session_start', '学生 5_4_罗浩轩 开始会话 10', '2025-04-04 00:46:32');
INSERT INTO `logs` VALUES (21, 'session_end', '学生 5_4_罗浩轩 结束会话 10', '2025-04-04 01:10:59');
INSERT INTO `logs` VALUES (22, 'session_start', '学生 4_1_王浩宇 开始会话 11', '2025-04-04 01:11:11');
INSERT INTO `logs` VALUES (23, 'session_end', '学生 4_1_王浩宇 结束会话 11', '2025-04-04 01:11:41');
INSERT INTO `logs` VALUES (24, 'session_start', '学生 4_1_王浩宇 开始会话 12', '2025-04-04 01:11:49');
INSERT INTO `logs` VALUES (25, 'session_end', '学生 4_1_王浩宇 结束会话 12', '2025-04-04 01:12:58');
INSERT INTO `logs` VALUES (26, 'session_start', '学生 4_1_王浩宇 开始会话 13', '2025-04-04 01:13:07');
INSERT INTO `logs` VALUES (27, 'session_end', '学生 4_1_王浩宇 结束会话 13', '2025-04-04 01:15:44');
INSERT INTO `logs` VALUES (28, 'session_start', '学生 4_1_王浩宇 开始会话 14', '2025-04-04 01:15:50');
INSERT INTO `logs` VALUES (29, 'session_end', '学生 4_1_王浩宇 结束会话 14', '2025-04-04 01:16:32');
INSERT INTO `logs` VALUES (30, 'session_start', '学生 4_1_王浩宇 开始会话 15', '2025-04-04 01:16:38');
INSERT INTO `logs` VALUES (31, 'session_end', '学生 4_1_王浩宇 结束会话 15', '2025-04-04 01:32:04');
INSERT INTO `logs` VALUES (32, 'session_start', '学生 4_1_王浩宇 开始会话 16', '2025-04-04 01:35:57');
INSERT INTO `logs` VALUES (33, 'session_end', '学生 4_1_王浩宇 结束会话 16', '2025-04-04 01:36:12');
INSERT INTO `logs` VALUES (34, 'session_start', '学生 4_1_王浩宇 开始会话 17', '2025-04-04 01:36:16');
INSERT INTO `logs` VALUES (35, 'session_end', '学生 4_1_王浩宇 结束会话 17', '2025-04-04 21:33:53');
INSERT INTO `logs` VALUES (36, 'session_start', '学生 4_1_王浩宇 开始会话 18', '2025-04-04 21:34:09');
INSERT INTO `logs` VALUES (37, 'session_end', '学生 4_1_王浩宇 结束会话 18', '2025-04-04 22:16:34');
INSERT INTO `logs` VALUES (38, 'session_start', '学生 4_1_王浩宇 开始会话 19', '2025-04-04 22:16:44');
INSERT INTO `logs` VALUES (39, 'session_end', '学生 4_1_王浩宇 结束会话 19', '2025-04-04 23:14:38');
INSERT INTO `logs` VALUES (40, 'session_start', '学生 4_1_王浩宇 开始会话 20', '2025-04-04 23:14:44');
INSERT INTO `logs` VALUES (41, 'session_end', '学生 4_1_王浩宇 结束会话 20', '2025-04-04 23:15:07');
INSERT INTO `logs` VALUES (42, 'session_start', '学生 4_1_王浩宇 开始会话 21', '2025-04-04 23:15:14');
INSERT INTO `logs` VALUES (43, 'session_end', '学生 4_1_王浩宇 结束会话 21', '2025-04-04 23:15:40');
INSERT INTO `logs` VALUES (44, 'session_start', '学生 4_1_王浩宇 开始会话 22', '2025-04-04 23:15:46');
INSERT INTO `logs` VALUES (45, 'session_end', '学生 4_1_王浩宇 结束会话 22', '2025-04-04 23:16:05');
INSERT INTO `logs` VALUES (46, 'session_start', '学生 4_1_王浩宇 开始会话 23', '2025-04-04 23:16:10');
INSERT INTO `logs` VALUES (47, 'session_end', '学生 4_1_王浩宇 结束会话 23', '2025-04-04 23:17:25');
INSERT INTO `logs` VALUES (48, 'session_start', '学生 4_1_王浩宇 开始会话 24', '2025-04-04 23:17:29');
INSERT INTO `logs` VALUES (49, 'session_end', '学生 4_1_王浩宇 结束会话 24', '2025-04-04 23:17:59');
INSERT INTO `logs` VALUES (50, 'session_start', '学生 4_1_王浩宇 开始会话 25', '2025-04-04 23:18:03');
INSERT INTO `logs` VALUES (51, 'session_end', '学生 4_1_王浩宇 结束会话 25', '2025-04-04 23:25:44');
INSERT INTO `logs` VALUES (52, 'session_start', '学生 4_1_王浩宇 开始会话 26', '2025-04-04 23:25:51');
INSERT INTO `logs` VALUES (53, 'session_end', '学生 4_1_王浩宇 结束会话 26', '2025-04-05 13:39:02');
INSERT INTO `logs` VALUES (54, 'session_start', '学生 4_1_王浩宇 开始会话 27', '2025-04-05 13:39:07');
INSERT INTO `logs` VALUES (55, 'session_end', '学生 4_1_王浩宇 结束会话 27', '2025-04-05 13:44:58');
INSERT INTO `logs` VALUES (56, 'session_start', '学生 4_1_王浩宇 开始会话 28', '2025-04-05 13:45:02');
INSERT INTO `logs` VALUES (57, 'session_end', '学生 4_1_王浩宇 结束会话 28', '2025-04-06 01:06:28');
INSERT INTO `logs` VALUES (58, 'session_start', '学生 4_1_王浩宇 开始会话 29', '2025-04-06 01:06:28');
INSERT INTO `logs` VALUES (59, 'session_start', '学生 4_3_周泽瑞 开始会话 30', '2025-04-06 01:10:16');
INSERT INTO `logs` VALUES (60, 'session_end', '学生 4_3_周泽瑞 结束会话 30', '2025-04-06 01:13:27');
INSERT INTO `logs` VALUES (61, 'session_start', '学生 4_3_周泽瑞 开始会话 31', '2025-04-06 01:13:27');
INSERT INTO `logs` VALUES (62, 'session_end', '学生 4_3_周泽瑞 结束会话 31', '2025-04-06 01:16:49');
INSERT INTO `logs` VALUES (63, 'session_start', '学生 4_3_周泽瑞 开始会话 32', '2025-04-06 01:16:49');
INSERT INTO `logs` VALUES (64, 'session_end', '学生 4_1_王浩宇 结束会话 29', '2025-04-06 01:26:37');
INSERT INTO `logs` VALUES (65, 'session_start', '学生 4_1_王浩宇 开始会话 33', '2025-04-06 01:26:37');
INSERT INTO `logs` VALUES (66, 'session_end', '学生 4_1_王浩宇 结束会话 33', '2025-04-06 01:33:19');
INSERT INTO `logs` VALUES (67, 'session_start', '学生 4_1_王浩宇 开始会话 34', '2025-04-06 01:33:19');
INSERT INTO `logs` VALUES (68, 'session_end', '学生 4_1_王浩宇 结束会话 34', '2025-04-06 01:37:01');
INSERT INTO `logs` VALUES (69, 'session_start', '学生 4_1_王浩宇 开始会话 35', '2025-04-06 01:37:01');
INSERT INTO `logs` VALUES (70, 'session_end', '学生 4_1_王浩宇 结束会话 35', '2025-04-06 01:39:55');
INSERT INTO `logs` VALUES (71, 'session_start', '学生 4_1_王浩宇 开始会话 36', '2025-04-06 01:39:55');
INSERT INTO `logs` VALUES (72, 'session_end', '学生 4_1_王浩宇 结束会话 36', '2025-04-06 01:51:37');
INSERT INTO `logs` VALUES (73, 'session_start', '学生 4_1_王浩宇 开始会话 37', '2025-04-06 01:51:37');
INSERT INTO `logs` VALUES (74, 'session_end', '学生 4_1_王浩宇 结束会话 37', '2025-04-06 09:33:56');
INSERT INTO `logs` VALUES (75, 'session_start', '学生 4_1_王浩宇 开始会话 38', '2025-04-06 09:33:56');
INSERT INTO `logs` VALUES (76, 'session_end', '学生 4_1_王浩宇 结束会话 38', '2025-04-06 12:49:43');
INSERT INTO `logs` VALUES (77, 'session_start', '学生 4_1_王浩宇 开始会话 39', '2025-04-06 12:49:43');
INSERT INTO `logs` VALUES (78, 'session_end', '学生 4_1_王浩宇 结束会话 39', '2025-04-06 13:03:26');
INSERT INTO `logs` VALUES (79, 'session_start', '学生 4_1_王浩宇 开始会话 40', '2025-04-06 13:03:26');
INSERT INTO `logs` VALUES (80, 'session_end', '学生 4_1_王浩宇 结束会话 40', '2025-04-06 14:00:52');
INSERT INTO `logs` VALUES (81, 'session_start', '学生 4_1_王浩宇 开始会话 41', '2025-04-06 14:00:52');
INSERT INTO `logs` VALUES (82, 'session_end', '学生 4_1_王浩宇 结束会话 41', '2025-04-06 18:55:15');
INSERT INTO `logs` VALUES (83, 'session_start', '学生 4_1_王浩宇 开始会话 42', '2025-04-06 18:55:15');
INSERT INTO `logs` VALUES (84, 'login', '用户 admin 登录成功', '2025-04-06 20:03:07');
INSERT INTO `logs` VALUES (85, 'session_end', '学生 4_1_王浩宇 结束会话 42', '2025-04-06 20:04:52');
INSERT INTO `logs` VALUES (86, 'session_start', '学生 4_1_王浩宇 开始会话 43', '2025-04-06 20:04:52');
INSERT INTO `logs` VALUES (87, 'login', '用户 admin 登录成功', '2025-04-06 20:46:58');
INSERT INTO `logs` VALUES (88, 'session_end', '学生 4_1_王浩宇 结束会话 43', '2025-04-06 20:47:58');
INSERT INTO `logs` VALUES (89, 'session_start', '学生 4_1_王浩宇 开始会话 44', '2025-04-06 20:47:58');
INSERT INTO `logs` VALUES (90, 'login', '用户 admin 登录成功', '2025-04-06 20:56:18');
INSERT INTO `logs` VALUES (91, 'session_end', '学生 4_1_王浩宇 结束会话 44', '2025-04-06 21:03:00');
INSERT INTO `logs` VALUES (92, 'session_start', '学生 4_1_王浩宇 开始会话 45', '2025-04-06 21:03:00');
INSERT INTO `logs` VALUES (93, 'session_end', '学生 4_1_王浩宇 结束会话 45', '2025-04-06 21:03:49');
INSERT INTO `logs` VALUES (94, 'session_start', '学生 4_1_王浩宇 开始会话 46', '2025-04-06 21:03:49');
INSERT INTO `logs` VALUES (95, 'session_start', '学生 4_2_李梓萱 开始会话 47', '2025-04-06 21:23:13');
INSERT INTO `logs` VALUES (96, 'session_start', '学生 4_5_吴思琪 开始会话 48', '2025-04-06 21:23:50');
INSERT INTO `logs` VALUES (97, 'session_end', '学生 4_5_吴思琪 结束会话 48', '2025-04-06 21:24:07');
INSERT INTO `logs` VALUES (98, 'session_start', '学生 4_5_吴思琪 开始会话 49', '2025-04-06 21:24:07');
INSERT INTO `logs` VALUES (99, 'session_end', '学生 4_5_吴思琪 结束会话 49', '2025-04-06 21:27:47');
INSERT INTO `logs` VALUES (100, 'session_start', '学生 4_5_吴思琪 开始会话 50', '2025-04-06 21:27:48');
INSERT INTO `logs` VALUES (101, 'session_end', '学生 4_5_吴思琪 结束会话 50', '2025-04-06 21:31:33');
INSERT INTO `logs` VALUES (102, 'session_start', '学生 4_5_吴思琪 开始会话 51', '2025-04-06 21:31:33');
INSERT INTO `logs` VALUES (103, 'session_end', '学生 4_5_吴思琪 结束会话 51', '2025-04-06 21:34:17');
INSERT INTO `logs` VALUES (104, 'session_start', '学生 4_5_吴思琪 开始会话 52', '2025-04-06 21:34:17');
INSERT INTO `logs` VALUES (105, 'session_end', '学生 4_5_吴思琪 结束会话 52', '2025-04-06 21:34:42');
INSERT INTO `logs` VALUES (106, 'session_start', '学生 4_5_吴思琪 开始会话 53', '2025-04-06 21:34:42');
INSERT INTO `logs` VALUES (107, 'session_end', '学生 4_2_李梓萱 结束会话 47', '2025-04-06 21:41:44');
INSERT INTO `logs` VALUES (108, 'session_start', '学生 4_2_李梓萱 开始会话 54', '2025-04-06 21:41:44');
INSERT INTO `logs` VALUES (109, 'session_end', '学生 4_2_李梓萱 结束会话 54', '2025-04-06 21:47:35');
INSERT INTO `logs` VALUES (110, 'session_start', '学生 4_2_李梓萱 开始会话 55', '2025-04-06 21:47:35');

-- ----------------------------
-- Table structure for medals
-- ----------------------------
DROP TABLE IF EXISTS `medals`;
CREATE TABLE `medals`  (
  `student_identifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '学生唯一标识符',
  `count` int NOT NULL DEFAULT 0 COMMENT '奖章数量',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`student_identifier`) USING BTREE,
  CONSTRAINT `fk_medals_student` FOREIGN KEY (`student_identifier`) REFERENCES `students` (`student_identifier`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '学生奖章记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of medals
-- ----------------------------
INSERT INTO `medals` VALUES ('4_1_王浩宇', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('4_2_李梓萱', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('4_3_周泽瑞', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('4_3_张星辰', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('4_4_刘雨桐', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('4_4_赵心悦', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('4_5_吴思琪', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('4_5_陈一诺', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('4_6_郑宇航', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('4_6_黄子轩', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('5_2_谢天宇', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('5_3_方文静', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('5_3_董欣怡', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('5_4_崔嘉昊', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('5_4_罗浩轩', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('5_5_彭诗涵', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('5_6_徐雅婷', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('5_6_熊浩然', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('5_7_林俊杰', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('6_2_潘昊然', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('6_2_陆明哲', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('6_2_韩佳琪', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('6_4_丁雅雯', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('6_5_石俊杰', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('6_7_蒋雨晴', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('6_8_唐欣然', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('6_8_沈宇航', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('6_8_秦雨菲', 30, '2025-04-03 22:36:45');
INSERT INTO `medals` VALUES ('6_8_许子豪', 30, '2025-04-03 22:36:45');

-- ----------------------------
-- Table structure for student_sessions
-- ----------------------------
DROP TABLE IF EXISTS `student_sessions`;
CREATE TABLE `student_sessions`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `student_identifier` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `start_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `end_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_session_student`(`student_identifier` ASC) USING BTREE,
  INDEX `idx_session_times`(`start_time` ASC, `end_time` ASC) USING BTREE,
  CONSTRAINT `student_sessions_ibfk_1` FOREIGN KEY (`student_identifier`) REFERENCES `students` (`student_identifier`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 56 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of student_sessions
-- ----------------------------
INSERT INTO `student_sessions` VALUES (1, '4_1_王浩宇', '2025-04-03 23:23:35', '2025-04-03 23:23:36');
INSERT INTO `student_sessions` VALUES (2, '5_7_林俊杰', '2025-04-03 23:30:00', '2025-04-03 23:30:01');
INSERT INTO `student_sessions` VALUES (3, '4_1_王浩宇', '2025-04-03 23:34:13', '2025-04-03 23:49:50');
INSERT INTO `student_sessions` VALUES (4, '4_1_王浩宇', '2025-04-03 23:49:58', '2025-04-03 23:50:04');
INSERT INTO `student_sessions` VALUES (5, '4_2_李梓萱', '2025-04-03 23:50:17', '2025-04-03 23:54:39');
INSERT INTO `student_sessions` VALUES (6, '4_2_李梓萱', '2025-04-03 23:54:48', '2025-04-03 23:58:03');
INSERT INTO `student_sessions` VALUES (7, '4_1_王浩宇', '2025-04-03 23:58:12', '2025-04-04 00:16:10');
INSERT INTO `student_sessions` VALUES (8, '4_2_李梓萱', '2025-04-04 00:16:28', '2025-04-04 00:18:25');
INSERT INTO `student_sessions` VALUES (9, '4_3_周泽瑞', '2025-04-04 00:18:58', '2025-04-04 00:46:14');
INSERT INTO `student_sessions` VALUES (10, '5_4_罗浩轩', '2025-04-04 00:46:31', '2025-04-04 01:10:59');
INSERT INTO `student_sessions` VALUES (11, '4_1_王浩宇', '2025-04-04 01:11:11', '2025-04-04 01:11:41');
INSERT INTO `student_sessions` VALUES (12, '4_1_王浩宇', '2025-04-04 01:11:49', '2025-04-04 01:12:58');
INSERT INTO `student_sessions` VALUES (13, '4_1_王浩宇', '2025-04-04 01:13:07', '2025-04-04 01:15:44');
INSERT INTO `student_sessions` VALUES (14, '4_1_王浩宇', '2025-04-04 01:15:50', '2025-04-04 01:16:32');
INSERT INTO `student_sessions` VALUES (15, '4_1_王浩宇', '2025-04-04 01:16:38', '2025-04-04 01:32:04');
INSERT INTO `student_sessions` VALUES (16, '4_1_王浩宇', '2025-04-04 01:35:57', '2025-04-04 01:36:12');
INSERT INTO `student_sessions` VALUES (17, '4_1_王浩宇', '2025-04-04 01:36:16', '2025-04-04 21:33:53');
INSERT INTO `student_sessions` VALUES (18, '4_1_王浩宇', '2025-04-04 21:34:09', '2025-04-04 22:16:34');
INSERT INTO `student_sessions` VALUES (19, '4_1_王浩宇', '2025-04-04 22:16:44', '2025-04-04 23:14:38');
INSERT INTO `student_sessions` VALUES (20, '4_1_王浩宇', '2025-04-04 23:14:44', '2025-04-04 23:15:07');
INSERT INTO `student_sessions` VALUES (21, '4_1_王浩宇', '2025-04-04 23:15:14', '2025-04-04 23:15:40');
INSERT INTO `student_sessions` VALUES (22, '4_1_王浩宇', '2025-04-04 23:15:46', '2025-04-04 23:16:04');
INSERT INTO `student_sessions` VALUES (23, '4_1_王浩宇', '2025-04-04 23:16:10', '2025-04-04 23:17:25');
INSERT INTO `student_sessions` VALUES (24, '4_1_王浩宇', '2025-04-04 23:17:29', '2025-04-04 23:17:59');
INSERT INTO `student_sessions` VALUES (25, '4_1_王浩宇', '2025-04-04 23:18:03', '2025-04-04 23:25:44');
INSERT INTO `student_sessions` VALUES (26, '4_1_王浩宇', '2025-04-04 23:25:51', '2025-04-05 13:39:02');
INSERT INTO `student_sessions` VALUES (27, '4_1_王浩宇', '2025-04-05 13:39:07', '2025-04-05 13:44:58');
INSERT INTO `student_sessions` VALUES (28, '4_1_王浩宇', '2025-04-05 13:45:02', '2025-04-06 01:06:28');
INSERT INTO `student_sessions` VALUES (29, '4_1_王浩宇', '2025-04-06 01:06:28', '2025-04-06 01:26:37');
INSERT INTO `student_sessions` VALUES (30, '4_3_周泽瑞', '2025-04-06 01:10:16', '2025-04-06 01:13:27');
INSERT INTO `student_sessions` VALUES (31, '4_3_周泽瑞', '2025-04-06 01:13:27', '2025-04-06 01:16:49');
INSERT INTO `student_sessions` VALUES (32, '4_3_周泽瑞', '2025-04-06 01:16:49', NULL);
INSERT INTO `student_sessions` VALUES (33, '4_1_王浩宇', '2025-04-06 01:26:37', '2025-04-06 01:33:19');
INSERT INTO `student_sessions` VALUES (34, '4_1_王浩宇', '2025-04-06 01:33:19', '2025-04-06 01:37:01');
INSERT INTO `student_sessions` VALUES (35, '4_1_王浩宇', '2025-04-06 01:37:01', '2025-04-06 01:39:55');
INSERT INTO `student_sessions` VALUES (36, '4_1_王浩宇', '2025-04-06 01:39:55', '2025-04-06 01:51:37');
INSERT INTO `student_sessions` VALUES (37, '4_1_王浩宇', '2025-04-06 01:51:37', '2025-04-06 09:33:56');
INSERT INTO `student_sessions` VALUES (38, '4_1_王浩宇', '2025-04-06 09:33:56', '2025-04-06 12:49:43');
INSERT INTO `student_sessions` VALUES (39, '4_1_王浩宇', '2025-04-06 12:49:43', '2025-04-06 13:03:26');
INSERT INTO `student_sessions` VALUES (40, '4_1_王浩宇', '2025-04-06 13:03:26', '2025-04-06 14:00:52');
INSERT INTO `student_sessions` VALUES (41, '4_1_王浩宇', '2025-04-06 14:00:52', '2025-04-06 18:55:15');
INSERT INTO `student_sessions` VALUES (42, '4_1_王浩宇', '2025-04-06 18:55:15', '2025-04-06 20:04:52');
INSERT INTO `student_sessions` VALUES (43, '4_1_王浩宇', '2025-04-06 20:04:52', '2025-04-06 20:47:58');
INSERT INTO `student_sessions` VALUES (44, '4_1_王浩宇', '2025-04-06 20:47:58', '2025-04-06 21:03:00');
INSERT INTO `student_sessions` VALUES (45, '4_1_王浩宇', '2025-04-06 21:03:00', '2025-04-06 21:03:49');
INSERT INTO `student_sessions` VALUES (46, '4_1_王浩宇', '2025-04-06 21:03:49', NULL);
INSERT INTO `student_sessions` VALUES (47, '4_2_李梓萱', '2025-04-06 21:23:13', '2025-04-06 21:41:44');
INSERT INTO `student_sessions` VALUES (48, '4_5_吴思琪', '2025-04-06 21:23:50', '2025-04-06 21:24:07');
INSERT INTO `student_sessions` VALUES (49, '4_5_吴思琪', '2025-04-06 21:24:07', '2025-04-06 21:27:47');
INSERT INTO `student_sessions` VALUES (50, '4_5_吴思琪', '2025-04-06 21:27:48', '2025-04-06 21:31:33');
INSERT INTO `student_sessions` VALUES (51, '4_5_吴思琪', '2025-04-06 21:31:33', '2025-04-06 21:34:17');
INSERT INTO `student_sessions` VALUES (52, '4_5_吴思琪', '2025-04-06 21:34:17', '2025-04-06 21:34:42');
INSERT INTO `student_sessions` VALUES (53, '4_5_吴思琪', '2025-04-06 21:34:42', NULL);
INSERT INTO `student_sessions` VALUES (54, '4_2_李梓萱', '2025-04-06 21:41:44', '2025-04-06 21:47:35');
INSERT INTO `student_sessions` VALUES (55, '4_2_李梓萱', '2025-04-06 21:47:35', NULL);

-- ----------------------------
-- Table structure for students
-- ----------------------------
DROP TABLE IF EXISTS `students`;
CREATE TABLE `students`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `student_identifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '学生唯一标识符',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '学生姓名',
  `grade` int NOT NULL COMMENT '年级',
  `class` int NOT NULL COMMENT '班级',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `student_identifier`(`student_identifier` ASC) USING BTREE,
  INDEX `idx_grade_class`(`grade` ASC, `class` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 129 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '学生基本信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of students
-- ----------------------------
INSERT INTO `students` VALUES (100, '4_1_王浩宇', '王浩宇', 4, 1, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (101, '4_2_李梓萱', '李梓萱', 4, 2, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (102, '4_3_张星辰', '张星辰', 4, 3, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (103, '4_4_刘雨桐', '刘雨桐', 4, 4, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (104, '4_5_陈一诺', '陈一诺', 4, 5, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (105, '4_6_黄子轩', '黄子轩', 4, 6, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (106, '4_4_赵心悦', '赵心悦', 4, 4, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (107, '4_3_周泽瑞', '周泽瑞', 4, 3, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (108, '4_5_吴思琪', '吴思琪', 4, 5, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (109, '4_6_郑宇航', '郑宇航', 4, 6, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (110, '5_7_林俊杰', '林俊杰', 5, 7, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (111, '5_6_徐雅婷', '徐雅婷', 5, 6, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (112, '5_4_罗浩轩', '罗浩轩', 5, 4, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (113, '5_3_董欣怡', '董欣怡', 5, 3, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (114, '5_2_谢天宇', '谢天宇', 5, 2, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (115, '5_3_方文静', '方文静', 5, 3, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (116, '5_4_崔嘉昊', '崔嘉昊', 5, 4, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (117, '5_5_彭诗涵', '彭诗涵', 5, 5, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (118, '5_6_熊浩然', '熊浩然', 5, 6, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (119, '6_7_蒋雨晴', '蒋雨晴', 6, 7, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (120, '6_8_许子豪', '许子豪', 6, 8, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (121, '6_8_唐欣然', '唐欣然', 6, 8, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (122, '6_2_陆明哲', '陆明哲', 6, 2, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (123, '6_2_韩佳琪', '韩佳琪', 6, 2, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (124, '6_8_沈宇航', '沈宇航', 6, 8, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (125, '6_8_秦雨菲', '秦雨菲', 6, 8, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (126, '6_5_石俊杰', '石俊杰', 6, 5, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (127, '6_4_丁雅雯', '丁雅雯', 6, 4, '2025-04-03 22:36:45');
INSERT INTO `students` VALUES (128, '6_2_潘昊然', '潘昊然', 6, 2, '2025-04-03 22:36:45');

-- ----------------------------
-- Table structure for typing_best
-- ----------------------------
DROP TABLE IF EXISTS `typing_best`;
CREATE TABLE `typing_best`  (
  `student_identifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '学生唯一标识符',
  `language` enum('zh','en') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '练习语言',
  `best_speed` int NOT NULL COMMENT '该语言下的最高速度',
  `record_id` int NULL DEFAULT NULL COMMENT '对应typing_records表的记录ID',
  `record_date` timestamp NULL DEFAULT NULL COMMENT '取得最高成绩的时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`student_identifier`, `language`) USING BTREE,
  INDEX `idx_best_speed`(`best_speed` ASC) USING BTREE,
  INDEX `fk_typing_best_record`(`record_id` ASC) USING BTREE,
  CONSTRAINT `fk_typing_best_record` FOREIGN KEY (`record_id`) REFERENCES `typing_records` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_typing_best_student` FOREIGN KEY (`student_identifier`) REFERENCES `students` (`student_identifier`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '学生最高打字成绩表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of typing_best
-- ----------------------------
INSERT INTO `typing_best` VALUES ('4_1_王浩宇', 'zh', 90, 9, '2025-04-03 23:46:28', '2025-04-03 23:50:49');
INSERT INTO `typing_best` VALUES ('4_2_李梓萱', 'zh', 83, 13, '2025-04-04 00:17:41', '2025-04-04 00:17:41');
INSERT INTO `typing_best` VALUES ('4_3_周泽瑞', 'zh', 94, 15, '2025-04-04 00:32:35', '2025-04-04 00:32:34');
INSERT INTO `typing_best` VALUES ('5_4_罗浩轩', 'zh', 121, 17, '2025-04-04 00:57:22', '2025-04-04 00:57:21');

-- ----------------------------
-- Table structure for typing_records
-- ----------------------------
DROP TABLE IF EXISTS `typing_records`;
CREATE TABLE `typing_records`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `student_identifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '学生唯一标识符',
  `speed` int NOT NULL COMMENT '打字速度(字/分钟)',
  `accuracy` decimal(5, 2) NOT NULL COMMENT '正确率',
  `language` enum('zh','en') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '练习语言：zh为中文，en为英文',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_student`(`student_identifier` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_student_language`(`student_identifier` ASC, `language` ASC) USING BTREE,
  CONSTRAINT `fk_typing_records_student` FOREIGN KEY (`student_identifier`) REFERENCES `students` (`student_identifier`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '学生打字练习记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of typing_records
-- ----------------------------
INSERT INTO `typing_records` VALUES (9, '4_1_王浩宇', 90, 98.00, 'zh', '2025-04-03 23:46:28');
INSERT INTO `typing_records` VALUES (10, '4_2_李梓萱', 68, 100.00, 'zh', '2025-04-03 23:54:03');
INSERT INTO `typing_records` VALUES (11, '4_1_王浩宇', 63, 100.00, 'zh', '2025-04-04 00:14:54');
INSERT INTO `typing_records` VALUES (12, '4_1_王浩宇', 14, 4.00, 'zh', '2025-04-04 00:15:35');
INSERT INTO `typing_records` VALUES (13, '4_2_李梓萱', 83, 87.00, 'zh', '2025-04-04 00:17:41');
INSERT INTO `typing_records` VALUES (14, '4_3_周泽瑞', 48, 26.00, 'zh', '2025-04-04 00:19:39');
INSERT INTO `typing_records` VALUES (15, '4_3_周泽瑞', 94, 98.00, 'zh', '2025-04-04 00:32:35');
INSERT INTO `typing_records` VALUES (16, '5_4_罗浩轩', 93, 94.00, 'zh', '2025-04-04 00:47:45');
INSERT INTO `typing_records` VALUES (17, '5_4_罗浩轩', 121, 100.00, 'zh', '2025-04-04 00:57:22');
INSERT INTO `typing_records` VALUES (18, '4_1_王浩宇', 24, 100.00, 'zh', '2025-04-04 22:01:27');
INSERT INTO `typing_records` VALUES (19, '4_1_王浩宇', 17, 100.00, 'zh', '2025-04-04 23:50:25');
INSERT INTO `typing_records` VALUES (20, '4_1_王浩宇', 6, 3.00, 'zh', '2025-04-04 23:57:50');
INSERT INTO `typing_records` VALUES (21, '4_1_王浩宇', 5, 2.00, 'zh', '2025-04-05 00:08:28');
INSERT INTO `typing_records` VALUES (22, '4_1_王浩宇', 15, 100.00, 'zh', '2025-04-06 00:55:00');
INSERT INTO `typing_records` VALUES (23, '4_1_王浩宇', 9, 13.00, 'zh', '2025-04-06 01:08:21');
INSERT INTO `typing_records` VALUES (24, '4_3_周泽瑞', 14, 100.00, 'zh', '2025-04-06 01:10:47');
INSERT INTO `typing_records` VALUES (25, '4_1_王浩宇', 14, 100.00, 'zh', '2025-04-06 01:35:03');
INSERT INTO `typing_records` VALUES (26, '4_1_王浩宇', 21, 80.00, 'zh', '2025-04-06 14:01:34');
INSERT INTO `typing_records` VALUES (27, '4_1_王浩宇', 17, 100.00, 'zh', '2025-04-06 18:56:18');

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登录用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `display_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '显示名称',
  `role` enum('admin','teacher') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'teacher' COMMENT '用户角色',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '账号创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  INDEX `idx_username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户账号表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, 'admin', 'admin123', '管理员', 'admin', '2025-04-02 14:09:56');
INSERT INTO `users` VALUES (3, 'teacher', 'teacher123', NULL, 'teacher', '2025-04-03 20:45:06');

-- ----------------------------
-- Procedure structure for add_student
-- ----------------------------
DROP PROCEDURE IF EXISTS `add_student`;
delimiter ;;
CREATE PROCEDURE `add_student`(IN p_identifier VARCHAR(255),
    IN p_name VARCHAR(100),
    IN p_grade INT,
    IN p_class INT)
BEGIN
    INSERT INTO students (student_identifier, name, grade, class) 
    VALUES (p_identifier, p_name, p_grade, p_class);
    
    -- 初始化奖章数为0
    INSERT INTO medals (student_identifier, count) VALUES (p_identifier, 0);
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for add_typing_record
-- ----------------------------
DROP PROCEDURE IF EXISTS `add_typing_record`;
delimiter ;;
CREATE PROCEDURE `add_typing_record`(IN p_student_identifier VARCHAR(255),
    IN p_speed INT,
    IN p_accuracy DECIMAL(5,2),
    IN p_language ENUM('zh', 'en'))
BEGIN
    DECLARE new_record_id INT;
    
    -- 插入新打字记录
    INSERT INTO typing_records (student_identifier, speed, accuracy, language)
    VALUES (p_student_identifier, p_speed, p_accuracy, p_language);
    
    SET new_record_id = LAST_INSERT_ID();
    
    -- 检查是否需要更新最高成绩
    -- (触发器已处理，此处无需手动更新)
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for update_student_medals
-- ----------------------------
DROP PROCEDURE IF EXISTS `update_student_medals`;
delimiter ;;
CREATE PROCEDURE `update_student_medals`(IN p_student_identifier VARCHAR(255),
    IN p_new_count INT)
BEGIN
    UPDATE medals SET count = p_new_count WHERE student_identifier = p_student_identifier;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table typing_records
-- ----------------------------
DROP TRIGGER IF EXISTS `update_best_typing_speed`;
delimiter ;;
CREATE TRIGGER `update_best_typing_speed` AFTER INSERT ON `typing_records` FOR EACH ROW BEGIN
    -- 检查是否存在该学生该语言的最高记录
    DECLARE current_best INT;
    SELECT best_speed INTO current_best FROM typing_best 
    WHERE student_identifier = NEW.student_identifier AND language = NEW.language LIMIT 1;
    
    -- 如果不存在记录或新记录更好，则更新
    IF current_best IS NULL OR NEW.speed > current_best THEN
        -- 插入或更新最高成绩记录
        INSERT INTO typing_best (student_identifier, language, best_speed, record_id, record_date, updated_at)
        VALUES (NEW.student_identifier, NEW.language, NEW.speed, NEW.id, NEW.created_at, NOW())
        ON DUPLICATE KEY UPDATE 
            best_speed = NEW.speed,
            record_id = NEW.id,
            record_date = NEW.created_at,
            updated_at = NOW();
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table typing_records
-- ----------------------------
DROP TRIGGER IF EXISTS `recalculate_best_after_delete`;
delimiter ;;
CREATE TRIGGER `recalculate_best_after_delete` AFTER DELETE ON `typing_records` FOR EACH ROW BEGIN
    -- 检查deleted记录是否是最高记录
    DECLARE best_record_id INT;
    DECLARE new_best_speed INT;
    DECLARE new_best_id INT;
    DECLARE new_best_date TIMESTAMP;
    
    SELECT record_id INTO best_record_id FROM typing_best 
    WHERE student_identifier = OLD.student_identifier AND language = OLD.language LIMIT 1;
    
    -- 如果删除的是最高纪录，需要重新计算
    IF best_record_id = OLD.id THEN
        -- 查找新的最高记录
        SELECT id, speed, created_at INTO new_best_id, new_best_speed, new_best_date 
        FROM typing_records
        WHERE student_identifier = OLD.student_identifier AND language = OLD.language
        ORDER BY speed DESC LIMIT 1;
        
        -- 如果还存在记录，更新最高成绩
        IF new_best_id IS NOT NULL THEN
            UPDATE typing_best
            SET best_speed = new_best_speed,
                record_id = new_best_id,
                record_date = new_best_date,
                updated_at = NOW()
            WHERE student_identifier = OLD.student_identifier AND language = OLD.language;
        ELSE
            -- 如果不存在任何记录，删除最高成绩
            DELETE FROM typing_best
            WHERE student_identifier = OLD.student_identifier AND language = OLD.language;
        END IF;
    END IF;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
