/**
 * 学生打字练习系统脚本
 */

// API配置
const API_CONFIG = {
    BASE_URL: '/api',
    ENDPOINTS: {
        SESSION_START: '/sessions/start',
        SESSION_END: '/sessions/end',
        TYPING_RECORDS: '/typing-records'
    }
};

// 年级转换映射
const GRADE_MAP = {
    '一年级': 1,
    '二年级': 2,
    '三年级': 3,
    '四年级': 4,
    '五年级': 5,
    '六年级': 6
};

document.addEventListener('DOMContentLoaded', function() {
    // 注释掉强制清除会话信息的代码，允许使用已存在的会话
    // localStorage.removeItem('currentStudent');
    // localStorage.removeItem('currentSession');
    
    // DOM元素引用
    const signinContainer = document.getElementById('signin-container');
    const studentContainer = document.getElementById('student-container');
    const typingPractice = document.getElementById('typing-practice');
    const historyContainer = document.getElementById('history-container');
    
    // 表单元素
    const schoolSelect = document.getElementById('school');
    const gradeSelect = document.getElementById('grade');
    const classSelect = document.getElementById('class');
    const nameInput = document.getElementById('name');
    const seatInput = document.getElementById('seat');
    const signinBtn = document.getElementById('signin-btn');
    
    // 打字练习元素
    const studentInfoEl = document.getElementById('student-info');
    const langZhBtn = document.getElementById('lang-zh');
    const langEnBtn = document.getElementById('lang-en');
    const timeRemainingEl = document.getElementById('time-remaining');
    const currentSpeedEl = document.getElementById('current-speed');
    const accuracyEl = document.getElementById('accuracy');
    const typingLinesContainer = document.getElementById('typing-lines-container');
    const startPracticeBtn = document.getElementById('start-practice');
    const finishPracticeBtn = document.getElementById('finish-practice');
    
    // 历史记录元素
    const backToPracticeBtn = document.getElementById('back-to-practice');
    const dateFilterEl = document.getElementById('date-filter');
    const classFilterEl = document.getElementById('class-filter');
    const nameFilterEl = document.getElementById('name-filter');
    const applyFilterBtn = document.getElementById('apply-filter');
    const exportCsvBtn = document.getElementById('export-csv');
    const historyDataEl = document.getElementById('history-data');
    
    // 全局变量
    let currentStudent = null;
    let currentSession = null;
    let practiceTimer = null;
    let practiceStartTime = null;
    let practiceEndTime = null;
    let isTypingActive = false;
    let typingInterval = null;
    let typingText = '';
    let originalText = '';
    let textLines = []; // 存储分割后的文本行
    let activeLineIndex = 0; // 当前活动行索引
    let inputElements = []; // 存储所有输入框元素
    let isComposing = false; // 标记是否正在使用输入法输入
    
    // 打字统计相关变量
    let correctChars = 0; // 正确字符计数
    let incorrectChars = 0; // 错误字符计数
    
    let selectedArticle = null; // 当前选择的文章对象
    let studentSchool = ''; // 学生学校
    let studentGrade = ''; // 学生年级
    let studentClass = ''; // 学生班级
    let studentName = ''; // 学生姓名
    let studentSeat = ''; // 学生座位号
    let studentIdentifier = ''; // 学生唯一标识符（年级+班级+姓名+座位号）
    
    // 中文练习文本集合
    const chineseTexts = [
        "春天来了，小草从地下探出头来，嫩嫩的，绿绿的。小树抽出了新的枝条，长出了嫩绿的叶子。花儿们也都开放了，红的、黄的、紫的，五颜六色，美丽极了。蝴蝶在花丛中飞舞，蜜蜂在花丛中忙碌地采蜜。小鸟在枝头欢快地歌唱，好像在说：'春天真美丽！'",
        "秋天到了，果园里的水果成熟了。红红的苹果像小朋友的脸蛋，金黄的梨子像一个个小葫芦，紫莹莹的葡萄像一串串珍珠。果农伯伯忙着采摘水果，小朋友也来帮忙。他们唱着欢快的歌，脸上洋溢着丰收的喜悦。",
        "小明家有一条小狗，名叫豆豆。豆豆有一身棕色的毛，两只尖尖的耳朵，一条卷卷的尾巴。它最喜欢和小明一起玩耍。每天放学后，小明一回到家，豆豆就会汪汪地叫，摇着尾巴跑过来。它还会做各种有趣的动作，逗得小明哈哈大笑。",
        "我的学校坐落在山脚下，四周环绕着绿树和鲜花。校园里有宽敞明亮的教室，有藏书丰富的图书馆，还有设备先进的实验室。每天清晨，当第一缕阳光照进校园，我们就开始了一天的学习生活。老师教给我们知识，同学之间互相帮助，我们在这里健康快乐地成长。",
        "小兔子住在森林里的一个小洞里。它有一双长长的耳朵，一双红红的眼睛，还有一身白白的绒毛。每天早上，它都要出去找食物。它最喜欢吃的是胡萝卜和嫩嫩的青草。小兔子非常警觉，一有风吹草动，它就会竖起耳朵，警惕地环顾四周。",
        "在一个阳光明媚的早晨，小鸟们在枝头唱歌，花儿们在微风中跳舞。一只小蚂蚁正忙碌地搬运食物，为冬天做准备。一只小蜜蜂飞来飞去，采集花粉。一只小蝴蝶在花丛中翩翩起舞。它们都是大自然中勤劳的小精灵，为美丽的地球增添了无限的活力。",
        "小红是一个活泼可爱的小女孩，她有一双明亮的大眼睛，长长的头发扎成两个小辫子。她最喜欢穿红色的衣服，因为她的名字叫小红。小红非常聪明，在学校里学习成绩很好，老师和同学们都很喜欢她。她还很乐于助人，经常帮助有困难的同学。",
        "我国是一个历史悠久的文明古国，有着五千多年的文明历史。我们的祖先创造了灿烂的文化，如四大发明、丝绸之路、长城、故宫等。这些宝贵的文化遗产是我们民族的骄傲，我们要继承和发扬中华民族的优秀传统文化，为实现中华民族的伟大复兴而努力学习。",
        "我的家乡在美丽的南方，那里山清水秀，风景如画。春天，油菜花开满山坡，金黄一片，蔚为壮观。夏天，树木葱郁，为人们遮挡炎炎烈日。秋天，果实累累，瓜果飘香。冬天，虽然没有北方的白雪皑皑，但也别有一番风情。我爱我的家乡，更爱这片养育我的土地。",
        "科技的发展改变了我们的生活方式。现在，我们可以通过手机随时随地与亲朋好友联系；可以通过互联网获取丰富的知识；可以通过高铁、飞机快速到达目的地。科技使我们的生活更加便捷、丰富多彩。但我们也要理性使用科技产品，不要沉迷网络，要保持健康的生活方式。"
    ];
    
    // 为每篇文章添加标题
    const chineseTextTitles = [
        "春天的景色",
        "丰收的秋天",
        "我家的小狗豆豆",
        "我的学校",
        "森林里的小兔子",
        "大自然的小精灵",
        "可爱的小红",
        "中华文明",
        "美丽的家乡",
        "科技改变生活"
    ];
    
    // 英文练习文章标题
    const englishTextTitle = "随机英语练习";
    
    // 英文短句集合（用于随机生成英文段落）
    const englishPhrases = [
        "The sun rises in the east.",
        "Time flies like an arrow.",
        "Knowledge is power.",
        "Practice makes perfect.",
        "Every cloud has a silver lining.",
        "All that glitters is not gold.",
        "Actions speak louder than words.",
        "Look before you leap.",
        "A journey of a thousand miles begins with a single step.",
        "Don't judge a book by its cover.",
        "Where there's a will, there's a way.",
        "The early bird catches the worm.",
        "Better late than never.",
        "Two heads are better than one.",
        "A picture is worth a thousand words.",
        "When in Rome, do as the Romans do.",
        "The grass is always greener on the other side.",
        "You can't teach an old dog new tricks.",
        "Don't put all your eggs in one basket.",
        "The pen is mightier than the sword."
    ];
    
    // 英文单词集合（用于随机生成英文段落）
    const commonWords = [
        "the", "be", "to", "of", "and", "a", "in", "that", "have", "I",
        "it", "for", "not", "on", "with", "he", "as", "you", "do", "at",
        "this", "but", "his", "by", "from", "they", "we", "say", "her", "she",
        "or", "an", "will", "my", "one", "all", "would", "there", "their", "what",
        "so", "up", "out", "if", "about", "who", "get", "which", "go", "me",
        "when", "make", "can", "like", "time", "no", "just", "him", "know", "take",
        "people", "into", "year", "your", "good", "some", "could", "them", "see", "other",
        "than", "then", "now", "look", "only", "come", "its", "over", "think", "also",
        "back", "after", "use", "two", "how", "our", "work", "first", "well", "way",
        "even", "new", "want", "because", "any", "these", "give", "day", "most", "us"
    ];
    
    // 打字问题相关变量
    let typingQuestions = [
        { question: "如果要开启或关闭大小写锁定，应该按哪个按键？", answer: "CapsLock", type: "key" },
        { question: "如果要输入空格，应该按哪个按键？", answer: " ", type: "key" },
        { question: "如果要换行或确认，应该按哪个按键？", answer: "Enter", type: "key" },
        { question: "如果要在表单中切换到下一项，应该按哪个按键？", answer: "Tab", type: "key" },
        { question: "如果要向上移动光标，应该按哪个按键？", answer: "ArrowUp", type: "key" }
    ];
    let currentQuestionIndex = 0;
    let questionAttempts = 0;
    const MAX_ATTEMPTS = 3;
    let allQuestionsAnswered = false;
    
    // 全局变量
    let currentLanguage = 'zh'; // 默认语言为中文

    // 文章数据结构
    let chineseArticles = [];
    let englishArticles = [];
    
    /**
     * 从API获取学校数据
     * @returns {Promise<Array>} 学校列表
     */
    async function fetchSchools() {
        try {
            console.log('开始获取学校数据...');
            const response = await fetch('/api/schools');
            console.log('API响应状态:', response.status);

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            const data = await response.json();
            console.log('获取到的学校数据:', data);

            // API直接返回学校数组
            return Array.isArray(data) ? data : [];
        } catch (error) {
            console.error('加载学校数据失败:', error);
            showMessage('加载学校数据失败，请刷新页面重试', 'error');
            return [];
        }
    }

    /**
     * 加载学校选项到下拉框
     */
    async function loadSchools() {
        try {
            console.log('开始加载学校选项...');
            const schools = await fetchSchools();
            console.log('fetchSchools返回的数据:', schools);

            if (!schoolSelect) {
                console.error('未找到学校选择元素');
                return;
            }

            // 清空现有选项（保留默认选项）
            schoolSelect.innerHTML = '<option value="" disabled selected>请选择学校</option>';

            // 添加学校选项
            if (Array.isArray(schools) && schools.length > 0) {
                schools.forEach(school => {
                    const option = document.createElement('option');
                    option.value = school.id;  // 使用学校ID而不是名称
                    option.textContent = school.name;
                    schoolSelect.appendChild(option);
                    console.log('添加学校选项:', school.name, 'ID:', school.id);
                });
                console.log(`成功加载 ${schools.length} 所学校`);
            } else {
                console.warn('没有获取到学校数据');
                showMessage('没有获取到学校数据，请检查网络连接', 'warning');
            }
        } catch (error) {
            console.error('加载学校选项失败:', error);
            showMessage('加载学校选项失败: ' + error.message, 'error');
        }
    }

    /**
     * 从API获取文章数据
     * @param {string} language - 文章语言：zh-中文, en-英文
     * @returns {Promise<Array>} 文章列表
     */
    async function fetchArticles(language) {
        try {
            const response = await fetch(`/api/articles/filter?language=${language}`);
            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || '获取文章失败');
            }
            
            return data.articles || [];
        } catch (error) {
            console.error(`加载${language === 'zh' ? '中文' : '英文'}文章失败:`, error);
            showMessage(`加载${language === 'zh' ? '中文' : '英文'}文章失败，将使用默认文章`, 'error');
            return [];
        }
    }
    
    /**
     * 加载所有文章数据
     * @param {boolean} showFeedback - 是否显示加载反馈
     * @returns {Promise<void>}
     */
    async function loadAllArticles(showFeedback = false) {
        try {
            if (showFeedback) {
                // 显示加载中的视觉反馈
                const refreshBtn = document.getElementById('refresh-articles');
                if (refreshBtn) {
                    // 禁用按钮并添加旋转动画
                    refreshBtn.disabled = true;
                    refreshBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" class="rotating-icon"><path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/><path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/></svg>';
                    
                    // 添加临时旋转样式
                    const style = document.createElement('style');
                    style.id = 'rotating-style';
                    style.textContent = `
                        .rotating-icon {
                            animation: rotate 1s linear infinite;
                        }
                        @keyframes rotate {
                            from { transform: rotate(0deg); }
                            to { transform: rotate(360deg); }
                        }
                    `;
                    document.head.appendChild(style);
                }
                
                // 禁用文章选择下拉框
                const articleSelect = document.getElementById('article-select');
                if (articleSelect) {
                    articleSelect.disabled = true;
                }
            }
            
            // 并行获取中文和英文文章
            const [zhArticles, enArticles] = await Promise.all([
                fetchArticles('zh'),
                fetchArticles('en')
            ]);
            
            // 更新全局变量
            chineseArticles = zhArticles.map(article => ({
                id: article.id.toString(),
                title: article.title,
                content: article.content,
                grade_level: article.grade_level,
                difficulty: article.difficulty
            }));
            
            englishArticles = enArticles.map(article => ({
                id: article.id.toString(),
                title: article.title,
                content: article.content,
                grade_level: article.grade_level,
                difficulty: article.difficulty
            }));
            
            console.log(`成功加载文章: ${chineseArticles.length}篇中文, ${englishArticles.length}篇英文`);
            
            // 创建文章选择列表
            createArticleSelectionList();
            
            if (showFeedback) {
                // 恢复按钮状态
                const refreshBtn = document.getElementById('refresh-articles');
                if (refreshBtn) {
                    refreshBtn.disabled = false;
                    refreshBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/><path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/></svg>';
                }
                
                // 移除临时样式
                const style = document.getElementById('rotating-style');
                if (style) {
                    document.head.removeChild(style);
                }
                
                // 恢复文章选择下拉框
                const articleSelect = document.getElementById('article-select');
                if (articleSelect) {
                    articleSelect.disabled = false;
                }
                
                // 显示成功信息
                showMessage(`文章列表已刷新: ${chineseArticles.length}篇中文, ${englishArticles.length}篇英文`, 'success');
            }
        } catch (error) {
            console.error('加载文章数据失败:', error);
            
            if (showFeedback) {
                // 恢复按钮状态
                const refreshBtn = document.getElementById('refresh-articles');
                if (refreshBtn) {
                    refreshBtn.disabled = false;
                    refreshBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/><path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/></svg>';
                }
                
                // 移除临时样式
                const style = document.getElementById('rotating-style');
                if (style) {
                    document.head.removeChild(style);
                }
                
                // 恢复文章选择下拉框
                const articleSelect = document.getElementById('article-select');
                if (articleSelect) {
                    articleSelect.disabled = false;
                }
                
                showMessage('加载文章数据失败，将使用内置文章', 'error');
            }
            
            // 使用默认文章作为后备方案
            setDefaultArticles();
        }
    }
    
    /**
     * 设置默认的文章数据（作为API加载失败的后备）
     */
    function setDefaultArticles() {
        chineseArticles = [
            { id: 'default-zh1', title: '中国传统文化', content: '中国传统文化是世界文化宝库中的瑰宝，包括儒家思想、道家哲学、中医、书法、绘画等多个方面。这些文化元素深深影响了中国人的价值观、行为方式和审美情趣。中国传统文化强调和谐、中庸、尊师重教等理念，对社会稳定和人际关系有着重要的影响。尊老爱幼、礼让谦和是中华民族的传统美德，在现代社会依然有着重要的现实意义。弘扬传统文化，既是对历史的尊重，也是对未来的责任。' },
            { id: 'default-zh2', title: '科技创新', content: '科技创新是推动人类社会发展的重要力量。从蒸汽机的发明到互联网的普及，科技的进步改变了人们的生活方式和思维模式。人工智能、大数据、云计算等新兴技术正在重塑各行各业，创造出新的发展机遇。创新需要开放的环境和自由的思想，需要容忍失败和鼓励尝试。在全球化的背景下，科技创新已经成为国家竞争力的重要体现，加强科技创新能力建设，对于提升国家综合实力具有战略意义。' }
        ];
        
        englishArticles = [
            { id: 'default-en1', title: 'Technology and Society', content: 'Technology has fundamentally changed how we live, work, and interact with each other. From smartphones to social media, technological innovations have created both opportunities and challenges for society. While technology has made communication faster and more convenient, it has also raised concerns about privacy, security, and the quality of human connections. As we continue to develop new technologies, it is important to consider their social impact and ensure that they serve human needs and values. The relationship between technology and society is complex and multifaceted, requiring careful consideration of both benefits and risks.' },
            { id: 'default-en2', title: 'Environmental Protection', content: 'Environmental protection is one of the most significant challenges facing the world today. As industrial and urban development accelerates, issues of environmental pollution and ecological damage are becoming increasingly serious. Air pollution, water pollution, and soil contamination not only threaten human health but also affect the balance of ecosystems. Protecting the environment requires the joint efforts of governments, businesses, and individuals. Governments should establish strict environmental regulations, businesses should adopt clean production technologies, and individuals should develop environmentally friendly habits. Only by living in harmony with nature can we achieve the goal of sustainable development.' }
        ];
        
        // 创建文章选择列表
        createArticleSelectionList();
    }
    
    /**
     * 更新计时器显示
     * @param {number} seconds - 剩余的秒数
     */
    function updateTimer(seconds) {
        if (!timeRemainingEl) return;
        
        // 计算分钟和秒
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        
        // 格式化为 MM:SS
        const formattedTime = `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        
        // 更新显示
        timeRemainingEl.textContent = formattedTime;
        
        // 添加视觉提示，当时间少于1分钟时显示红色
        if (seconds < 60) {
            timeRemainingEl.style.color = '#e74c3c'; // 红色
            // 如果少于30秒，添加闪烁效果
            if (seconds < 30) {
                timeRemainingEl.classList.add('blinking');
            } else {
                timeRemainingEl.classList.remove('blinking');
            }
        } else {
            timeRemainingEl.style.color = '#4a90e2'; // 恢复默认蓝色
            timeRemainingEl.classList.remove('blinking');
        }
    }
    
    /**
     * 初始化应用
     */
    async function init() {
        console.log('初始化学生打字练习系统...');

        // 检查当前页面是否是打字练习页面
        const isTypingPage = window.location.pathname.includes('/stutype');

        if (!isTypingPage) {
            // 只在非打字练习页面清除会话信息，强制重新签到
            localStorage.removeItem('currentStudent');
            localStorage.removeItem('currentSession');
            currentStudent = null;
            currentSession = null;
            resetStudentVariables();
        } else {
            // 在打字练习页面，尝试恢复学生信息
            console.log('打字练习页面，尝试恢复学生信息...');
            const storedStudent = localStorage.getItem('currentStudent');
            if (storedStudent) {
                try {
                    currentStudent = JSON.parse(storedStudent);
                    console.log('恢复的学生信息:', currentStudent);

                    // 设置全局变量
                    if (currentStudent) {
                        studentSchool = currentStudent.school || '';
                        studentGrade = currentStudent.grade || '';
                        studentClass = currentStudent.class || '';
                        studentName = currentStudent.name || '';
                        studentSeat = currentStudent.seat || '';

                        // 更新页面上的学生信息显示
                        updateStudentInfoDisplay();
                    }
                } catch (error) {
                    console.error('解析学生信息失败:', error);
                    localStorage.removeItem('currentStudent');
                }
            }
        }
        
        // 先隐藏所有页面，防止初始化过程中页面闪烁
        hideAllPages();

        // 只在签到页面加载学校数据和初始化表单
        if (!isTypingPage) {
            // 加载学校数据
            await loadSchools();

            // 初始化班级下拉列表
            updateClassOptions();
        }

        // 添加事件监听器
        attachEventListeners();

        // 只在打字练习页面加载文章数据
        if (isTypingPage) {
            await loadAllArticles();
        }

        // 只在签到页面检查签到状态
        if (!isTypingPage) {
            // 检查签到状态
            await checkSigninStatus();
        } else {
            // 在打字练习页面，直接显示打字练习界面
            console.log('打字练习页面，跳过签到状态检查');
            if (currentStudent) {
                console.log('学生信息已恢复，显示打字练习界面');
                showTypingPracticePage();
            } else {
                console.log('没有学生信息，可能需要重新签到');
                // 可以选择跳转回签到页面或显示错误信息
            }
        }
        
        console.log('初始化完成');
    }
    
    /**
     * 添加各种事件监听器
     */
    function attachEventListeners() {
        console.log('开始添加事件监听器...');
        
        // 签到按钮事件（仅在签到页面存在）
        if (signinBtn) {
            signinBtn.addEventListener('click', handleSignin);
        }

        // 签到成功后的按钮事件
        const goToTypingBtn = document.getElementById('go-to-typing');
        if (goToTypingBtn) {
            goToTypingBtn.addEventListener('click', function() {
                console.log('点击开始打字练习按钮，跳转到打字页面');
                window.location.href = '/student/stutype';
            });
        }
        
        // 重新开始按钮事件
        const restartPracticeBtn = document.getElementById('restart-practice');
        if (restartPracticeBtn) {
            restartPracticeBtn.addEventListener('click', restartPractice);
        }
        
        // 座位号输入事件 - 自动转为大写
        if (seatInput) {
            seatInput.addEventListener('input', function(e) {
                this.value = this.value.toUpperCase();
            });
        }
        
        // 学校选择变更时更新年级选项（仅在签到页面存在）
        if (schoolSelect) {
            schoolSelect.addEventListener('change', function() {
                console.log('学校选择变更:', this.value);
                updateGradeOptions();
            });
        }

        // 年级下拉列表变更时更新班级选项（仅在签到页面存在）
        if (gradeSelect) {
            gradeSelect.addEventListener('change', function() {
                console.log('年级选择变更:', this.value);
                updateClassOptions();
            });
        }
        
        // 刷新文章列表按钮事件
        const refreshArticlesBtn = document.getElementById('refresh-articles');
        if (refreshArticlesBtn) {
            refreshArticlesBtn.addEventListener('click', function() {
                // 调用loadAllArticles函数并显示视觉反馈
                loadAllArticles(true);
            });
        }
        
        // 返回签到按钮已删除
        
        // 历史记录查看按钮
        const viewHistoryBtn = document.getElementById('view-history');
        if (viewHistoryBtn) {
            console.log('找到查看历史记录按钮，添加事件监听器');
            viewHistoryBtn.addEventListener('click', function() {
                console.log('查看历史记录按钮被点击');
                showHistoryPage();
            });
        }
        
        // 返回练习按钮
        if (backToPracticeBtn) {
            backToPracticeBtn.addEventListener('click', function() {
                if (historyContainer) historyContainer.style.display = 'none';
                if (typingPractice) typingPractice.style.display = 'block';
                // 隐藏文章选择区（如果存在）
                const articleSelection = document.getElementById('article-selection');
                if (articleSelection) articleSelection.style.display = 'none';
            });
        }
        
        // 语言切换按钮事件
        if (langZhBtn) {
        langZhBtn.addEventListener('click', function() {
            if (!isTypingActive) {
                setLanguage('zh');
            }
        });
        }
        
        if (langEnBtn) {
        langEnBtn.addEventListener('click', function() {
            if (!isTypingActive) {
                setLanguage('en');
            }
        });
        }
        
        // 开始练习按钮事件（仅在打字练习页面存在）
        if (startPracticeBtn) {
            startPracticeBtn.addEventListener('click', startPractice);
        }
        
        // 结束练习按钮事件
        if (finishPracticeBtn) {
        finishPracticeBtn.addEventListener('click', function() {
            if (isTypingActive) {
                    // 检查至少已完成一行文本
                    let hasCompletedLine = false;
                    for (let i = 0; i < textLines.length; i++) {
                        const textEl = document.getElementById(`typing-text-${i}`);
                        if (textEl && textEl.classList.contains('line-completed')) {
                            hasCompletedLine = true;
                            break;
                        }
                    }
                    
                    // 如果一行都没完成，询问是否确认结束
                    if (!hasCompletedLine) {
                        showDialog('您还未完成任何一行文本，确定要结束练习吗？', function() {
                        finishPractice();
                    });
                } else {
                        // 至少完成了一行，直接结束
                    finishPractice();
                }
            }
        });
        }
        
        // 重置按钮事件
        const resetPracticeBtn = document.getElementById('reset-practice');
        if (resetPracticeBtn) {
            resetPracticeBtn.addEventListener('click', function() {
                // 如果正在进行练习，询问是否确认重置
                if (isTypingActive) {
                    showDialog('练习正在进行中，确定要重置吗？这将清除当前进度。', function() {
                        // 如果正在练习，先停止
                        if (isTypingActive) {
                            stopPractice();
                        }
                        // 重置练习界面
                        resetPracticeInterface();
                    });
                } else {
                    // 直接重置
                    resetPracticeInterface();
                }
            });
        }
        
        // 练习结果继续按钮
        if (document.getElementById('result-continue')) {
        document.getElementById('result-continue').addEventListener('click', function() {
            document.getElementById('result-overlay').style.display = 'none';
                // 重置练习界面
            resetPracticeInterface();
        });
        }
    }

    // 修改showResultOverlay函数，以便使用新的结果计算
    function showResultOverlay(speed, accuracy) {
        // 更新结果显示
        document.getElementById('result-speed').textContent = speed;
        document.getElementById('result-accuracy').textContent = `${accuracy}%`;
        
        // 显示结果弹窗
        document.getElementById('result-overlay').style.display = 'flex';
    }
    
    /**
     * 停止打字练习（用于中断练习）
     */
    function stopPractice() {
        // 如果当前不是活动状态，不执行任何操作
        if (!isTypingActive) return;
        
        console.log('停止打字练习');
        
        // 停止计时器
        if (practiceTimer) {
            clearInterval(practiceTimer);
            practiceTimer = null;
        }
        
        if (typingInterval) {
            clearInterval(typingInterval);
            typingInterval = null;
        }
        
        // 禁用所有输入框
        if (Array.isArray(inputElements)) {
            inputElements.forEach(inputEl => {
                if (inputEl) inputEl.disabled = true;
            });
        }
        
        // 停止监听键盘事件
        document.removeEventListener('keydown', preventCopyPasteShortcuts);
        
        // 更新状态
        isTypingActive = false;
        
        // 结束练习状态
        // 显示额外按钮
        const extraButtons = document.querySelector('.extra-buttons');
        if (extraButtons) {
            extraButtons.style.display = 'flex';
        }
        
        // 启用语言选择按钮
        if (langZhBtn) langZhBtn.disabled = false;
        if (langEnBtn) langEnBtn.disabled = false;
        
        // 重置开始按钮状态
        if (startPracticeBtn) {
            startPracticeBtn.disabled = false;
            startPracticeBtn.style.backgroundColor = '';
            startPracticeBtn.style.cursor = 'pointer';
            startPracticeBtn.style.display = 'block';
        }
        
        // 检查结束按钮是否存在
        if (finishPracticeBtn) {
            finishPracticeBtn.style.display = 'none';
        }
        
        // 启用文章选择下拉框
        const articleSelect = document.getElementById('article-select');
        if (articleSelect) {
            articleSelect.disabled = false;
        }
        
        // 启用文章选择区域的交互
        const selectGroup = document.querySelector('.select-group');
        if (selectGroup) {
            selectGroup.style.opacity = '1';
            selectGroup.style.pointerEvents = 'auto';
        }
    }
    
    /**
     * 重置练习界面
     */
    function resetPracticeInterface() {
        // 清空打字区域
        typingLinesContainer.innerHTML = '';
        
        // 重置计时和统计
        document.getElementById('time-remaining').textContent = '5:00';
        document.getElementById('current-speed').textContent = '0';
        document.getElementById('accuracy').textContent = '100%';
        
        // 重置文章选择
        if (selectedArticle) {
            // 保留当前选择的文章
            // 生成新文本并显示
        generateTypingText();
            displayText(textLines);
            
            // 提示用户可以继续练习
            showMessage(`已重置练习，您可以继续练习"${selectedArticle.title}"`, 'info');
            
            // 确保打字区域可见
            typingLinesContainer.style.display = 'block';
        } else {
            // 如果没有选择文章，显示提示
            typingLinesContainer.innerHTML = '<div class="article-selection-tip">请先选择一篇您想要练习的文章</div>';
            // 隐藏打字区域
            typingLinesContainer.style.display = 'none';
        }
        
        // 重置界面状态 - 开始按钮和结束按钮
        startPracticeBtn.disabled = false;
        startPracticeBtn.style.backgroundColor = '';
        startPracticeBtn.style.cursor = 'pointer';
        startPracticeBtn.style.display = 'block';
        
        // 恢复语言选择器和文章选择器
        const languageSelector = document.getElementById('language-selector');
        if (languageSelector) {
            languageSelector.style.display = 'flex';
        }
        
        // 恢复文章选择区域的交互
        const selectGroup = document.querySelector('.select-group');
        if (selectGroup) {
            selectGroup.style.opacity = '1';
            selectGroup.style.pointerEvents = 'auto';
        }
        
        // 显示文章选择
        const articleSelect = document.getElementById('article-select');
        if (articleSelect) {
            articleSelect.disabled = false;
        }
        
        // 重新激活额外按钮
        const extraButtons = document.querySelector('.extra-buttons');
        if (extraButtons) {
            extraButtons.style.display = 'flex';
        }
        
        // 重新启用语言选择按钮
        if (langZhBtn) langZhBtn.disabled = false;
        if (langEnBtn) langEnBtn.disabled = false;
    }
    
    /**
     * 阻止复制粘贴事件
     * @param {Event} e - 事件对象
     */
    function preventCopyPaste(e) {
        e.preventDefault();
        if (isTypingActive) {
            showMessage('禁止复制粘贴！', 'error');
        }
    }
    
    /**
     * 检查签到状态
     */
    async function checkSigninStatus() {
        console.log('检查签到状态');
        
        try {
            // 尝试从localStorage获取学生信息
            const storedStudent = localStorage.getItem('currentStudent');
            
            if (storedStudent) {
                try {
                    // 尝试解析存储的学生信息
                    currentStudent = JSON.parse(storedStudent);
                    console.log('发现保存的学生信息:', currentStudent);
                    
                    // 验证现有会话是否有效
                    const sessionValid = await validateExistingSession(currentStudent);
                    
                    if (sessionValid) {
                        console.log('现有会话有效，显示学生页面');
                        await showStudentPage();
                        return;
                    } else {
                        console.log('现有会话无效，需要重新签到');
                    }
                } catch (parseError) {
                    console.error('解析保存的学生信息出错:', parseError);
                    // 清除无效的学生信息
                localStorage.removeItem('currentStudent');
                    localStorage.removeItem('currentSession');
                    currentStudent = null;
                }
            }
            
            // 没有有效的会话，显示签到页面
            await showSigninPage();
        } catch (error) {
            console.error('检查签到状态错误:', error);
            await showSigninPage();
        }
    }
    
    /**
     * 将年级文本转换为数字
     * @param {string|number} gradeText - 年级文本（如"四年级"）或数字（如4）
     * @returns {number} 年级数字（如4）
     */
    function convertGradeToNumber(gradeText) {
        // 如果已经是数字，直接返回
        if (!isNaN(gradeText)) {
            const num = parseInt(gradeText);
            // 确保年级在1-6之间
            if (num >= 1 && num <= 6) {
                return num;
            }
        }
        
        // 如果是字符串，尝试处理
        if (typeof gradeText === 'string') {
            // 去除空格
            const cleanText = gradeText.trim();
            
            // 使用映射转换中文年级
            if (GRADE_MAP[cleanText]) {
                return GRADE_MAP[cleanText];
            }
            
            // 如果是"4年级"这样的格式
            const numericMatch = cleanText.match(/^(\d+)年级$/);
            if (numericMatch) {
                const num = parseInt(numericMatch[1]);
                if (num >= 1 && num <= 6) {
                    return num;
                }
            }
            
            // 如果只是纯数字字符串
            const pureNumber = cleanText.match(/^(\d+)$/);
            if (pureNumber) {
                const num = parseInt(pureNumber[1]);
                if (num >= 1 && num <= 6) {
                    return num;
                }
            }
        }
        
        // 如果无法转换，记录错误并返回null
        console.error('无法转换年级:', gradeText);
        return null;
    }
    
    /**
     * 验证学生并启动会话
     * @param {Object} student - 学生信息对象
     * @returns {Promise<boolean>} 验证结果
     */
    async function verifyStudent(student) {
        try {
            // 显示加载状态
            document.getElementById('loading').style.display = 'flex';
            
            // 转换年级为数字
            const gradeNumber = convertGradeToNumber(student.grade);
            if (gradeNumber === null) {
                showMessage('年级格式无效，请检查输入', 'error');
                document.getElementById('loading').style.display = 'none';
                return false;
            }
            
            // 构建学生标识符 - 使用数字年级
            const studentId = `${gradeNumber}_${student.class}_${student.name}`;
            
            // 数据保护：只输出非敏感的调试信息
            console.log('开始验证学生信息:', {
                年级类型: typeof student.grade,
                转换后年级: gradeNumber,
                班级类型: typeof student.class,
                姓名长度: student.name?.length || 0,
                座位号: student.seat,
                标识符长度: studentId?.length || 0,
                请求URL: `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.SESSION_START}`
            });
            
            // 调用会话启动API
            let response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.SESSION_START}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    student_identifier: studentId,
                    grade: gradeNumber,  // 使用转换后的数字年级
                    class: student.class,
                    name: student.name,
                    seat: student.seat,  // 添加座位号
                    strict_validation: true
                })
            });
            
            let data = await response.json();
            console.log('服务器响应:', data);
            
            // 隐藏加载状态
            document.getElementById('loading').style.display = 'none';
            
            // 检查是否存在未结束的会话
            if (!response.ok && data.error && data.error.includes('学生已有未结束的会话')) {
                console.log('检测到未结束的会话，尝试结束旧会话');
                showMessage('检测到未结束的会话，正在自动处理...', 'info');
                
                // 显示加载状态
                document.getElementById('loading').style.display = 'flex';
                
                // 尝试结束活动会话
                const sessionEnded = await endActiveSession(studentId);
                
                if (sessionEnded) {
                    showMessage('成功结束之前的会话，正在继续登录...', 'success');
                    
                    // 重新调用会话启动API
                    response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.SESSION_START}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ 
                            student_identifier: studentId,
                            grade: gradeNumber,  // 使用转换后的数字年级
                            class: student.class,
                            name: student.name,
                            seat: student.seat,  // 添加座位号
                            strict_validation: true
                        })
                    });
                    
                    data = await response.json();
                    console.log('重新请求响应:', data);
            
            // 隐藏加载状态
            document.getElementById('loading').style.display = 'none';
            
            if (response.ok) {
                student.sessionId = data.data.id;
                        student.sessionStartTime = data.data.start_time || new Date().toISOString();
                        // 保存转换后的年级
                        student.grade = gradeNumber;
                        localStorage.setItem('currentStudent', JSON.stringify(student));
                        showMessage('签到成功！', 'success');
                return true;
                    }
            } else {
                    document.getElementById('loading').style.display = 'none';
                    showMessage('无法自动结束之前的会话，请联系管理员处理', 'error');
                    return false;
                }
            }
            
            if (response.ok) {
                student.sessionId = data.data.id;
                student.sessionStartTime = data.data.start_time || new Date().toISOString();
                // 保存转换后的年级
                student.grade = gradeNumber;
                localStorage.setItem('currentStudent', JSON.stringify(student));
                showMessage('签到成功！', 'success');
                return true;
            } else {
                console.error('验证失败:', data);
                let errorMessage = parseErrorMessage(data.error || '验证学生失败', data);
                showMessage(errorMessage, 'error');
                return false;
            }
        } catch (error) {
            console.error('验证学生错误:', error);
            document.getElementById('loading').style.display = 'none';
            showMessage('网络错误，请检查服务器连接后重试', 'error');
            return false;
        }
    }
    
    /**
     * 处理签到表单提交
     */
    async function handleSignin() {
        // 验证表单
        if (!validateSigninForm()) {
            return;
        }

        // 获取输入值
        const schoolId = schoolSelect.value;
        const schoolName = schoolSelect.options[schoolSelect.selectedIndex].text;
        studentSchool = schoolName; // 保存学校名称而不是ID
        studentGrade = gradeSelect.value;
        studentClass = classSelect.value;
        studentName = nameInput.value.trim();
        studentSeat = seatInput.value.trim().toUpperCase();

        // 显示加载状态
        document.getElementById('loading').style.display = 'block';

        try {
            // 发送签到请求
            console.log('发送签到请求，数据:', {
                school_id: parseInt(schoolId),
                grade: parseInt(studentGrade),
                class: parseInt(studentClass),
                name: studentName,
                seat_number: studentSeat
            });

            const response = await fetch('/api/student-signin', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    school_id: parseInt(schoolId),
                    grade: parseInt(studentGrade),
                    class: parseInt(studentClass),
                    name: studentName,
                    seat_number: studentSeat
                })
            });

            const data = await response.json();
            document.getElementById('loading').style.display = 'none';

            if (response.ok && data.success) {
                // 签到成功
                showMessage('签到成功！', 'success');

                // 转换年级为数字
                const gradeNumber = convertGradeToNumber(studentGrade);

                // 创建学生标识符 - 使用数字格式年级
                studentIdentifier = `${gradeNumber}_${studentClass}_${studentName}`;

                console.log('构建的学生标识符:', studentIdentifier, '(原始年级:', studentGrade, '转换后:', gradeNumber, ')');

                // 保存学生信息到本地存储，包含数据库中的真实标识符
                const studentInfo = {
                    school: studentSchool,
                    grade: studentGrade,
                    class: studentClass,
                    name: studentName,
                    seat: studentSeat,
                    signin_time: new Date().toISOString(),
                    student_data: data.data.student_info,
                    // 从API响应中提取真实的学生标识符
                    student_identifier: data.data.student_info?.student_identifier,
                    id: data.data.student_info?.id
                };

                console.log('保存的学生信息:', studentInfo);
                console.log('学生标识符:', studentInfo.student_identifier);

                localStorage.setItem('currentStudent', JSON.stringify(studentInfo));

                // 显示签到成功界面
                showSigninSuccessPage();
            } else {
                console.error('签到失败:', data);
                showMessage(data.error || '签到失败，请重试', 'error');
            }
        } catch (error) {
            console.error('签到错误:', error);
            document.getElementById('loading').style.display = 'none';
            showMessage('网络错误，请检查服务器连接后重试', 'error');
        }
    }
    
    /**
     * 创建学生会话
     */
    function createStudentSession(student) {
        currentSession = {
            student: student,
            startTime: new Date().toISOString(),
            records: []
        };
        
        // 存储会话信息
        localStorage.setItem('currentSession', JSON.stringify(currentSession));
    }
    
    /**
     * 验证签到表单
     * @returns {boolean} 表单是否有效
     */
    function validateSigninForm() {
        // 检查学校
        if (!schoolSelect.value) {
            showMessage('请选择学校', 'error');
            schoolSelect.focus();
            return false;
        }

        // 检查年级
        if (!gradeSelect.value) {
            showMessage('请选择年级', 'error');
            gradeSelect.focus();
            return false;
        }
        
        // 检查班级
        if (!classSelect.value) {
            showMessage('请选择班级', 'error');
            classSelect.focus();
            return false;
        }
        
        // 检查姓名
        const nameValue = nameInput.value.trim();
        if (!nameValue) {
            showMessage('请输入姓名', 'error');
            nameInput.focus();
            return false;
        }
        
        // 检查姓名是否至少包含2个汉字
        const chineseCharCount = nameValue.match(/[\u4e00-\u9fa5]/g)?.length || 0;
        if (chineseCharCount < 2) {
            showMessage('姓名必须至少包含2个汉字', 'error');
            nameInput.focus();
            return false;
        }
        
        // 检查座位号 (要求字母+数字格式)
        const seatValue = seatInput.value.trim().toUpperCase();
        if (!seatValue) {
            showMessage('请输入座位号', 'error');
            seatInput.focus();
            return false;
        }

        // 验证座位号格式：字母+数字，如A1、B12、AB3等
        if (!/^[A-Z]+[0-9]+$/.test(seatValue)) {
            showMessage('座位号格式无效，请输入字母+数字的格式，如A1、B12', 'error');
            seatInput.focus();
            return false;
        }
        
        return true;
    }
    
    /**
     * 显示签到页面
     */
    async function showSigninPage() {
        // 清除会话数据
        localStorage.removeItem('currentStudent');
        localStorage.removeItem('currentSession');
        currentStudent = null;
        currentSession = null;
        
        // 重置所有状态
        resetStudentVariables();
        
        // 重置表单
        if (schoolSelect) schoolSelect.value = '';
        if (nameInput) nameInput.value = '';
        if (seatInput) seatInput.value = '';
        
        // 隐藏所有页面元素
        hideAllPages();

        // 隐藏签到成功界面
        const signinSuccessContainer = document.getElementById('signin-success');
        if (signinSuccessContainer) {
            signinSuccessContainer.style.display = 'none';
        }

        // 显示签到界面
        if (signinContainer) {
            signinContainer.style.display = 'flex';
        } else {
            console.log('签到容器不存在，可能在打字练习页面');
        }
        
        // 移除问题容器
        const questionContainer = document.getElementById('question-container');
        if (questionContainer) {
            questionContainer.remove();
        }
        
        console.log('签到页面已显示');
    }
    
    /**
     * 显示打字练习页面
     */
    function showTypingPracticePage() {
        // 隐藏所有页面元素
        hideAllPages();

        // 移除键盘事件监听器
        document.removeEventListener('keydown', handleQuestionKeydown);

        // 显示学生主界面
        const studentContainer = document.getElementById('student-container');
        if (studentContainer) {
            studentContainer.style.display = 'block';
        }

        // 显示打字练习界面
        if (typingPractice) {
            typingPractice.style.display = 'block';
        } else {
            console.log('打字练习元素不存在');
        }
        
        // 初始显示中文作为默认语言
        setLanguage('zh');
        
        // 显示提示信息（无论什么语言都用中文提示）
        if (typingLinesContainer) {
            typingLinesContainer.innerHTML = '<div class="article-selection-tip">请先选择一篇您想要练习的文章</div>';
            typingLinesContainer.style.display = 'block';
        }
        
        // 显示开始练习按钮
        const startPracticeBtn = document.getElementById('start-practice');
        if (startPracticeBtn) {
            startPracticeBtn.style.display = 'block';
        }

        const finishPracticeBtn = document.getElementById('finish-practice');
        if (finishPracticeBtn) {
            finishPracticeBtn.style.display = 'none';
        }
        
        // 显示文章选择区
        const articleSelection = document.getElementById('article-selection');
        if (articleSelection) {
            articleSelection.style.display = 'block';
        }
        
        console.log('打字练习界面已显示，当前语言:', currentLanguage);
    }
    
    /**
     * 将文本分割成多行
     * @param {string} text - 原始文本
     * @returns {Array} 分割后的文本行数组
     */
    function splitTextIntoLines(text) {
        // 如果文本为空，返回一个空行
        if (!text || text.length === 0) {
            return [""];
        }
        
        // 获取容器宽度
        const containerWidth = typingLinesContainer.clientWidth || 800;
        
        // 创建一个临时元素来测量文本宽度
        const measureEl = document.createElement('div');
        measureEl.style.font = '18px "Microsoft YaHei", Arial, sans-serif'; // 与CSS中的字体样式保持一致
        measureEl.style.whiteSpace = 'nowrap';
        measureEl.style.position = 'absolute';
        measureEl.style.visibility = 'hidden';
        document.body.appendChild(measureEl);
        
        // 每个字符的平均宽度估计（根据语言调整）
        const avgCharWidth = currentLanguage === 'zh' ? 18 : 10; // 中文字符宽度约为英文的1.8倍
        
        // 基于容器宽度和平均字符宽度，估算一行能容纳的字符数
        // 为留出一些边距，实际容量乘以0.9
        const estimatedCharsPerLine = Math.floor((containerWidth - 60) / avgCharWidth * 0.9);
        
        console.log(`容器宽度: ${containerWidth}px, 估算每行字符数: ${estimatedCharsPerLine}`);
        
        const lines = [];
        let currentLine = "";
        let currentLineWidth = 0;
        
        // 定义中文标点符号的正则表达式
        const punctuationRegex = /[，。；：""''（）、！？《》【】]/;
        
        // 逐个字符测量并分行
        for (let i = 0; i < text.length; i++) {
            const char = text[i];
            
            // 测量当前字符的宽度
            measureEl.textContent = char;
            const charWidth = measureEl.offsetWidth;
            
            // 检查当前字符是否为标点符号
            const isPunctuation = punctuationRegex.test(char);
            
            // 如果添加这个字符会导致超出容器宽度，则创建新行
            if (currentLine.length > 0 && currentLineWidth + charWidth > containerWidth - 60) {
                // 查看下一个字符是否为标点符号
                const nextCharIsPunctuation = i < text.length - 1 && punctuationRegex.test(text[i + 1]);
                
                if (isPunctuation) {
                    // 如果当前字符是标点符号，将其添加到当前行末尾，即使它会稍微超出宽度
                    currentLine += char;
                    lines.push(currentLine);
                    currentLine = "";
                    currentLineWidth = 0;
                } else if (nextCharIsPunctuation && i < text.length - 1) {
                    // 如果下一个字符是标点符号，则同时添加当前字符和下一个标点
                    currentLine += char + text[i + 1];
                    lines.push(currentLine);
                    currentLine = "";
                    currentLineWidth = 0;
                    i++; // 跳过下一个字符，因为已经处理过了
                } else {
                    // 正常情况，开始新的一行
                    lines.push(currentLine);
                    currentLine = char;
                    currentLineWidth = charWidth;
                }
            } else {
                // 正常添加字符
                currentLine += char;
                currentLineWidth += charWidth;
            }
        }
        
        // 添加最后一行
        if (currentLine.length > 0) {
            lines.push(currentLine);
        }
        
        // 移除临时元素
        document.body.removeChild(measureEl);
        
        // 最后检查确保没有行以标点符号开头
        for (let i = 1; i < lines.length; i++) {
            if (lines[i].length > 0 && punctuationRegex.test(lines[i][0])) {
                // 如果行首是标点符号，将它移到上一行末尾
                const punctuation = lines[i][0];
                lines[i-1] += punctuation;
                lines[i] = lines[i].substring(1);
                
                // 如果移除标点后当前行为空，则删除此行并继续检查
                if (lines[i].length === 0) {
                    lines.splice(i, 1);
                    i--; // 回退索引以检查下一行
                }
            }
        }
        
        console.log(`文本被分割为${lines.length}行，基于容器宽度${containerWidth}px`);
        return lines;
    }
    
    /**
     * 生成打字练习文本
     */
    function generateTypingText() {
        let textContent = "";
        
        if (currentLanguage === 'zh') {
            if (selectedArticle) {
                // 使用选择的特定文章
                textContent = selectedArticle.content;
            } else if (chineseArticles.length > 0) {
                // 随机选择一篇中文文章
                const randomIndex = Math.floor(Math.random() * chineseArticles.length);
                selectedArticle = chineseArticles[randomIndex];
                textContent = selectedArticle.content;
            } else {
                // 如果没有可用的文章，使用默认文本
                textContent = "目前没有可用的中文文章。请选择其他语言或联系管理员添加文章。";
                selectedArticle = {
                    id: 'no-article',
                    title: '无可用文章',
                    content: textContent
                };
            }
        } else {
            if (selectedArticle) {
                // 使用选择的特定英文文章
                textContent = selectedArticle.content;
            } else if (englishArticles.length > 0) {
                // 随机选择一篇英文文章
                    const randomIndex = Math.floor(Math.random() * englishArticles.length);
                    selectedArticle = englishArticles[randomIndex];
                    textContent = selectedArticle.content;
                } else {
                // 如果没有可用的文章，生成随机英文段落
                    textContent = generateEnglishParagraph();
                    selectedArticle = { 
                        id: 'random-en', 
                        title: '随机英文练习', 
                        content: textContent 
                    };
            }
        }
        
        // 记录原始文本用于显示
        originalText = textContent;
        
        // 分割文本成多行
        textLines = splitTextIntoLines(textContent);
        
        console.log(`原始文本长度: ${textContent.length} 字符，分割为 ${textLines.length} 行`);
        
        // 重置打字进度
        resetTypingProgress();
    }
    
    /**
     * 重置打字练习进度
     */
    function resetTypingProgress() {
        currentCharIndex = 0;
        currentLineIndex = 0;
        correctChars = 0;
        incorrectChars = 0;
        typingStartTime = null;
        typingEndTime = null;
        isTypingActive = false;
        inputElements = [];
        
        // 重置UI显示
        const accuracyEl = document.getElementById('accuracy');
        const currentSpeedEl = document.getElementById('current-speed');
        const timeRemainingEl = document.getElementById('time-remaining');
        
        if (accuracyEl) accuracyEl.textContent = '0%';
        if (currentSpeedEl) currentSpeedEl.textContent = '0';
        if (timeRemainingEl) timeRemainingEl.textContent = '5:00';
    }
    
    /**
     * 生成随机英文段落
     * @returns {string} 生成的英文段落
     */
    function generateEnglishParagraph() {
        let paragraph = '';
        
        // 随机选择一些短语
        const numPhrases = Math.floor(Math.random() * 5) + 8; // 8-12个短语
        const selectedPhrases = [];
        
        for (let i = 0; i < numPhrases; i++) {
            const randomIndex = Math.floor(Math.random() * englishPhrases.length);
            selectedPhrases.push(englishPhrases[randomIndex]);
        }
        
        // 在短语之间添加一些随机句子
        for (let i = 0; i < selectedPhrases.length; i++) {
            paragraph += selectedPhrases[i] + ' ';
            
            // 在每个短语后有50%的概率添加一个随机句子
            if (Math.random() > 0.5 && i < selectedPhrases.length - 1) {
                paragraph += generateRandomSentence() + ' ';
            }
        }
        
        // 确保段落长度至少为300个字符
        while (paragraph.length < 300) {
            paragraph += generateRandomSentence() + ' ';
        }
        
        return paragraph.trim();
    }
    
    /**
     * 生成随机英文句子
     * @returns {string} 生成的英文句子
     */
    function generateRandomSentence() {
        const sentenceLength = Math.floor(Math.random() * 8) + 5; // 5-12个单词
        let sentence = '';
        
        for (let i = 0; i < sentenceLength; i++) {
            const randomIndex = Math.floor(Math.random() * commonWords.length);
            let word = commonWords[randomIndex];
            
            // 首字母大写
            if (i === 0) {
                word = word.charAt(0).toUpperCase() + word.slice(1);
            }
            
            sentence += word;
            
            if (i < sentenceLength - 1) {
                sentence += ' ';
            }
        }
        
        return sentence + '.';
    }
    
    /**
     * 在界面上显示多行文本
     * @param {Array} lines - 文本行数组
     */
    function displayText(lines) {
        // 清空容器
        typingLinesContainer.innerHTML = '';
        
        // 如果没有文本，显示提示
        if (!lines || lines.length === 0) {
            typingLinesContainer.innerHTML = '<div class="empty-text-notice">无可用文本</div>';
            return;
        }
        
        // 如果当前选择的文章有标题，显示标题
        if (selectedArticle && selectedArticle.title) {
            const titleEl = document.createElement('div');
            titleEl.className = 'typing-title';
            titleEl.textContent = selectedArticle.title;
            typingLinesContainer.appendChild(titleEl);
        }
        
        // 创建行容器
        inputElements = []; // 重置输入框元素集合
        activeLineIndex = 0; // 重置当前行索引
        
        // 添加每一行文本及对应的输入框
        lines.forEach((line, index) => {
            // 创建行对容器
            const linePair = document.createElement('div');
            linePair.className = 'typing-line-pair';
            linePair.setAttribute('data-line', index);
            
            // 创建示范文字行
            const textLine = document.createElement('div');
            textLine.className = 'typing-text-line';
            textLine.id = `typing-text-${index}`;
            if (index === 0) textLine.classList.add('current-line');
            
            // 将文本分割成单个字符并添加span
            for (let i = 0; i < line.length; i++) {
                const charSpan = document.createElement('span');
                charSpan.textContent = line[i];
                charSpan.setAttribute('data-index', i);
                textLine.appendChild(charSpan);
            }
            
            // 确保文本行宽度占满容器
            textLine.style.width = '100%';
            
            // 创建输入框
            const inputLine = document.createElement('input');
            inputLine.type = 'text';
            inputLine.className = 'typing-input-line';
            inputLine.id = `typing-input-${index}`;
            inputLine.autocomplete = 'off';
            inputLine.autocorrect = 'off';
            inputLine.autocapitalize = 'off';
            inputLine.spellcheck = false;
            inputLine.disabled = true; // 初始状态禁用
            
            // 设置最大输入长度与参考文本长度相同
            inputLine.maxLength = line.length;
            
            // 确保输入框宽度与文本行一致
            inputLine.style.width = '100%';
            
            // 只有第一行输入框在开始练习时启用
            if (index === 0) {
                inputLine.disabled = false;
            }
            
            // 添加输入事件
            inputLine.addEventListener('input', function() {
                if (!isComposing) { // 只有在非输入法输入状态下才检查
                    checkTypingLine(index);
                }
            });

            // 添加输入法事件监听
            inputLine.addEventListener('compositionstart', function() {
                isComposing = true;
            });

            inputLine.addEventListener('compositionend', function() {
                isComposing = false;
                // 输入法输入完成后，立即检查
                checkTypingLine(index);
                
                // 输入法输入完成后检查是否需要行切换
                if (this.value.length === line.length) {
                    console.log(`输入法输入完成后行 ${index} 已满，准备切换到下一行`);
                    setTimeout(() => {
                        handleLineCompletion(index);
                    }, 100);
                }
            });
            
            // 添加键盘事件监听，处理backspace返回上一行
            inputLine.addEventListener('keydown', function(e) {
                // 检测是否按下了backspace键且输入框为空
                if (e.key === 'Backspace' && this.value === '' && index > 0) {
                    handleBackspaceToPreviousLine(index);
                    e.preventDefault(); // 阻止默认行为
                }
            });
            
            // 存储输入框元素引用
            inputElements.push(inputLine);
            
            // 添加元素到容器
            linePair.appendChild(textLine);
            linePair.appendChild(inputLine);
            typingLinesContainer.appendChild(linePair);
            
            // 初始显示当前行的第一个字符为"当前"字符
            if (index === 0) {
                const firstChar = textLine.querySelector('span');
                if (firstChar) {
                    firstChar.className = 'current';
                }
            }
        });
    }
    
    /**
     * 检查特定行的打字输入
     * @param {number} lineIndex - 行索引
     */
    function checkTypingLine(lineIndex) {
        if (!isTypingActive || isComposing) return; // 如果正在使用输入法输入，不检查
        
        const inputEl = document.getElementById(`typing-input-${lineIndex}`);
        const textEl = document.getElementById(`typing-text-${lineIndex}`);
        
        if (!inputEl || !textEl) return;
        
        const inputText = inputEl.value;
        const textSpans = textEl.querySelectorAll('span');
        
        // 确保输入文本不超过参考文本长度
        if (inputText.length > textSpans.length) {
            inputEl.value = inputText.substring(0, textSpans.length);
        }
        
        // 临时统计当前行的正确和错误字符数
        let newCorrectChars = 0;
        let newIncorrectChars = 0;
        
        // 重置当前行所有字符的样式
        textSpans.forEach(span => {
            span.className = '';
        });
        
        // 获取之前输入的长度
        const prevInputLength = inputEl._prevInputLength || 0;
        
        // 如果输入长度减少（用户删除了字符），需要调整全局计数器
        if (inputText.length < prevInputLength) {
            // 因为用户可能删除了正确或错误的字符，我们需要重新计算
            // 为了简单起见，我们重置当前行的计数，然后重新计算全部
            
            // 先获取当前行的正确和错误字符数
            const prevCorrect = inputEl._correctChars || 0;
            const prevIncorrect = inputEl._incorrectChars || 0;
            
            // 从全局计数中减去
            correctChars -= prevCorrect;
            incorrectChars -= prevIncorrect;
            
            // 重置当前行的计数
            inputEl._correctChars = 0;
            inputEl._incorrectChars = 0;
        }
        
        // 逐字比对并标记正误
        for (let i = 0; i < inputText.length && i < textSpans.length; i++) {
            const inputChar = inputText[i];
            const targetChar = textSpans[i].textContent;
            
            if (inputChar === targetChar) {
                textSpans[i].className = 'correct';
                newCorrectChars++;
            } else {
                textSpans[i].className = 'incorrect';
                newIncorrectChars++;
            }
        }
        
        // 更新全局计数器，仅考虑新增的字符
        // 计算当前行之前已经统计的字符数
        const prevCorrect = inputEl._correctChars || 0;
        const prevIncorrect = inputEl._incorrectChars || 0;
        
        // 更新全局计数
        correctChars += (newCorrectChars - prevCorrect);
        incorrectChars += (newIncorrectChars - prevIncorrect);
        
        // 更新当前行的计数
        inputEl._correctChars = newCorrectChars;
        inputEl._incorrectChars = newIncorrectChars;
        
        // 记录当前输入长度，用于下次比较
        inputEl._prevInputLength = inputText.length;
        
        // 标记下一个要输入的字符
        if (inputText.length < textSpans.length) {
            textSpans[inputText.length].className = 'current';
        }
        
        // 计算总体准确率
        const totalChars = correctChars + incorrectChars;
        const accuracy = totalChars > 0 ? Math.round((correctChars / totalChars) * 100) : 0;
        
        // 更新准确率显示（添加安全检查）
        const accuracyEl = document.getElementById('accuracy');
        if (accuracyEl) accuracyEl.textContent = `${accuracy}%`;
        
        // 更新总体统计（包括速度计算）
        updateOverallStats();
        
        // 输出调试信息
        console.log(`当前输入: ${inputText}`);
        console.log(`统计: 正确=${correctChars}, 错误=${incorrectChars}, 准确率=${accuracy}%`);
        
        // 检查是否需要自动换行
        // 只有在非输入法输入状态下，且输入文本长度达到参考文本长度时，才自动切换到下一行
        if (inputText.length === textSpans.length && !isComposing) {
            // 添加短暂延迟，让用户看到最后一个字符的正确/错误状态
            setTimeout(() => {
                console.log(`行 ${lineIndex} 已满，自动切换到下一行`);
                handleLineCompletion(lineIndex);
            }, 100);
        }
    }
    
    /**
     * 处理一行输入完成后的逻辑
     * @param {number} lineIndex - 完成的行索引
     */
    function handleLineCompletion(lineIndex) {
        // 获取当前行的输入框和文本元素
        const inputEl = document.getElementById(`typing-input-${lineIndex}`);
        const textEl = document.getElementById(`typing-text-${lineIndex}`);
        
        if (!inputEl || !textEl) return;
        
        // 不再检查输入是否为空，因为自动换行只会在行满时触发
        
        // 标记当前行为已完成
        textEl.classList.add('line-completed');
        textEl.classList.remove('current-line');
        inputEl.disabled = true; // 禁用输入框，但可以通过backspace返回
        
        // 显示自动换行通知效果
        showAutoLineChangeNotification(lineIndex);
        
        // 播放换行音效
        playLineCompletionSound();
        
        console.log(`行 ${lineIndex} 已完成，自动切换到下一行`);
        
        // 如果有下一行，激活它
        if (lineIndex < textLines.length - 1) {
            activeLineIndex = lineIndex + 1;
            const nextInputEl = document.getElementById(`typing-input-${activeLineIndex}`);
            const nextTextEl = document.getElementById(`typing-text-${activeLineIndex}`);
            
            if (nextInputEl && nextTextEl) {
                console.log(`激活下一行: ${activeLineIndex}`);
                
                // 获取行对容器并添加动画类
                const nextLinePair = nextTextEl.closest('.typing-line-pair');
                if (nextLinePair) {
                    nextLinePair.classList.add('new-active');
                    // 动画结束后移除类
                    setTimeout(() => {
                        nextLinePair.classList.remove('new-active');
                    }, 300);
                }
                
                // 确保下一行立即可用
                nextInputEl.disabled = false;
                nextInputEl.value = ''; // 确保输入框为空
                setTimeout(() => {
                    nextInputEl.focus(); // 稍微延迟聚焦，确保DOM已更新
                }, 50);
                
                nextTextEl.classList.add('current-line');
                
                // 平滑滚动到下一行
                nextTextEl.scrollIntoView({ behavior: 'smooth', block: 'center' });
                
                // 标记下一行的第一个字符为当前字符
                const firstChar = nextTextEl.querySelector('span');
                if (firstChar) {
                    firstChar.className = 'current';
                }
                
                // 添加高亮效果
                nextTextEl.classList.add('line-highlight');
                setTimeout(() => {
                    nextTextEl.classList.remove('line-highlight');
                }, 500);
            } else {
                console.error(`未找到下一行元素: ${activeLineIndex}`);
            }
        } else {
            // 所有行都已完成，结束练习
            console.log('所有行都已完成，结束练习');
            finishPractice();
        }
    }
    
    /**
     * 显示自动换行通知效果
     * @param {number} lineIndex - 当前行索引
     */
    function showAutoLineChangeNotification(lineIndex) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = 'auto-line-change-notification';
        notification.textContent = '自动换行';
        
        // 获取当前行容器
        const textEl = document.getElementById(`typing-text-${lineIndex}`);
        if (!textEl) return;
        
        // 将通知添加到容器
        textEl.appendChild(notification);
        
        // 动画结束后移除通知
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 1000);
    }
    
    /**
     * 播放行完成音效
     */
    function playLineCompletionSound() {
        // 创建音频上下文（如果浏览器支持）
        try {
            const AudioContext = window.AudioContext || window.webkitAudioContext;
            if (!AudioContext) return;
            
            const context = new AudioContext();
            const oscillator = context.createOscillator();
            const gain = context.createGain();
            
            oscillator.connect(gain);
            gain.connect(context.destination);
            
            oscillator.type = 'sine';
            oscillator.frequency.value = 880; // A5音符
            gain.gain.value = 0.1; // 音量控制
            
            oscillator.start(0);
            gain.gain.exponentialRampToValueAtTime(0.001, context.currentTime + 0.3);
            oscillator.stop(context.currentTime + 0.3);
        } catch (e) {
            console.error('无法播放音效', e);
        }
    }
    
    /**
     * 更新整体统计数据
     */
    function updateOverallStats() {
        if (!isTypingActive) return;
        
        // 计算总体准确率
        const totalChars = correctChars + incorrectChars;
        const overallAccuracy = totalChars > 0 ? Math.round((correctChars / totalChars) * 100) : 0;
        
        // 只在值发生变化时更新DOM，以提高性能
        if (accuracyEl.textContent !== `${overallAccuracy}%`) {
            accuracyEl.textContent = `${overallAccuracy}%`;
        }
        
        // 只有当实际有字符输入时才计算速度
        if (totalChars > 0) {
        // 计算已经过去的时间（分钟）
        const elapsedMinutes = (new Date() - practiceStartTime) / 1000 / 60;
        
        // 计算打字速度（字/分钟），仅考虑正确的字符
        let typingSpeed = 0;
        if (elapsedMinutes > 0) {
                typingSpeed = Math.round(correctChars / elapsedMinutes);
        }
        
            // 只在值发生变化时更新DOM，以提高性能
            if (currentSpeedEl.textContent !== String(typingSpeed)) {
        currentSpeedEl.textContent = typingSpeed;
            }
            

        }
    }
    
    /**
     * 开始打字练习
     */
    function startPractice() {
        // 如果已经在进行中，不执行任何操作
        if (isTypingActive) return;
        
        // 确保已选择文章
        if (!selectedArticle) {
            const articleSelect = document.getElementById('article-select');
            const selectGroup = document.querySelector('.select-group');
            
            // 显示更醒目的提示
            showMessage('请先选择一篇文章再开始练习', 'warning');
            
            // 高亮文章选择器
            if (articleSelect && selectGroup) {
                // 添加闪烁效果
                selectGroup.classList.add('highlight-selection');
                
                // 滚动到文章选择器
                articleSelect.scrollIntoView({ behavior: 'smooth', block: 'center' });
                
                // 聚焦到下拉框
                setTimeout(() => {
                    articleSelect.focus();
                    
                    // 3秒后移除高亮效果
                    setTimeout(() => {
                        selectGroup.classList.remove('highlight-selection');
                    }, 3000);
                }, 300);
            }
            return;
        }
        
        console.log('开始打字练习');
        
        // 确保文本行已显示
        if (typingLinesContainer.childNodes.length <= 1) {
            // 只有一个元素（可能是标题）或为空，重新生成
            generateTypingText();
            displayText(textLines);
        }
        
        // 重置正确率和打字速度为0
        correctChars = 0;
        incorrectChars = 0;
        
        // 确保所有输入框都清空和重置其内部计数
        inputElements.forEach(inputEl => {
            if (!inputEl) return;
            inputEl.value = '';
            inputEl._prevInputLength = 0;
            inputEl._correctChars = 0;
            inputEl._incorrectChars = 0;
        });
        
        // 重置DOM显示（添加安全检查）
        const accuracyEl = document.getElementById('accuracy');
        const currentSpeedEl = document.getElementById('current-speed');
        if (accuracyEl) accuracyEl.textContent = '0%';
        if (currentSpeedEl) currentSpeedEl.textContent = '0';
        
        // 直接设置开始按钮为禁用状态
        startPracticeBtn.disabled = true;
        startPracticeBtn.style.backgroundColor = '#ccc';
        startPracticeBtn.style.cursor = 'not-allowed';
        
        // 禁用文章选择和语言按钮
        const articleSelect = document.getElementById('article-select');
        if (articleSelect) {
            articleSelect.disabled = true;
        }
        if (langZhBtn) langZhBtn.disabled = true;
        if (langEnBtn) langEnBtn.disabled = true;
        
        // 禁用文章选择区域的交互
        const selectGroup = document.querySelector('.select-group');
        if (selectGroup) {
            selectGroup.style.opacity = '0.6';
            selectGroup.style.pointerEvents = 'none';
        }
        
        // 设置时间（5分钟 = 300秒）
        let totalSeconds = 5 * 60;
        
        // 立即更新一次计时器显示
        updateTimer(totalSeconds);
        
        // 启动计时器
        practiceTimer = setInterval(function() {
            totalSeconds--;
            
            // 更新计时器显示
            updateTimer(totalSeconds);
            
            // 时间到，自动结束练习
            if (totalSeconds <= 0) {
                clearInterval(practiceTimer);
                finishPractice();
            }
        }, 1000);
        
        // 启动打字速度计算定时器
        typingInterval = setInterval(updateOverallStats, 2000);
        
        // 记录开始时间
        typingStartTime = new Date();
        practiceStartTime = new Date();
        
        // 激活第一行输入框
        const firstInput = document.getElementById('typing-input-0');
        if (firstInput) {
            firstInput.disabled = false;
            firstInput.focus();
        }
        
        // 标记为活动状态
        isTypingActive = true;
        
        // 添加键盘快捷键防护
        document.addEventListener('keydown', preventCopyPasteShortcuts);
        
        // 显示提示信息
        showMessage('练习开始，请开始输入文本', 'info');
        
        console.log('打字练习已开始，计时器已启动');
    }

    /**
     * 完成打字练习
     */
    function finishPractice() {
        if (!isTypingActive) return;
        
        // 计算结束时间
        practiceEndTime = new Date();
        
        // 关闭练习相关UI
        stopPractice();
        
        // 计算最终速度和准确率
        const elapsedMinutes = (practiceEndTime - practiceStartTime) / 1000 / 60;
        const finalSpeed = elapsedMinutes > 0 ? Math.round(correctChars / elapsedMinutes) : 0;
        const totalChars = correctChars + incorrectChars;
        const finalAccuracy = totalChars > 0 ? Math.round((correctChars / totalChars) * 100) : 100;
        
        // 构建打字记录
        const record = {
            speed: finalSpeed,
            accuracy: finalAccuracy,
            date: new Date().toISOString(),
            synced: false
        };
        
        // 确保学生标识符使用数字格式的年级
        let grade = studentGrade;
        if (isNaN(parseInt(grade)) && typeof convertGradeToNumber === 'function') {
            grade = convertGradeToNumber(grade);
            console.log('在保存打字记录时转换年级:', studentGrade, '→', grade);
        }
        
        // 保存记录
        saveTypingRecord(finalSpeed, finalAccuracy, grade);
        
        // 显示成绩对话框
        showResultDialog(finalSpeed, finalAccuracy);
    }
    
    /**
     * 重置练习界面
     */
    function resetPracticeInterface() {
        // 清空打字区域
        typingLinesContainer.innerHTML = '';
        
        // 重置计时和统计
        document.getElementById('time-remaining').textContent = '5:00';
        document.getElementById('current-speed').textContent = '0';
        document.getElementById('accuracy').textContent = '100%';
        
        // 重置文章选择
        if (selectedArticle) {
            // 保留当前选择的文章
            // 生成新文本并显示
            generateTypingText();
            displayText(textLines);
            
            // 提示用户可以继续练习
            showMessage(`已重置练习，您可以继续练习"${selectedArticle.title}"`, 'info');
            
            // 确保打字区域可见
            typingLinesContainer.style.display = 'block';
        } else {
            // 如果没有选择文章，显示提示
            typingLinesContainer.innerHTML = '<div class="article-selection-tip">请先选择一篇您想要练习的文章</div>';
            // 隐藏打字区域
            typingLinesContainer.style.display = 'none';
        }
        
        // 重置界面状态 - 开始按钮和结束按钮
        startPracticeBtn.disabled = false;
        startPracticeBtn.style.backgroundColor = '';
        startPracticeBtn.style.cursor = 'pointer';
        startPracticeBtn.style.display = 'block';
        
        // 恢复语言选择器和文章选择器
        const languageSelector = document.getElementById('language-selector');
        if (languageSelector) {
            languageSelector.style.display = 'flex';
        }
        
        // 恢复文章选择区域的交互
        const selectGroup = document.querySelector('.select-group');
        if (selectGroup) {
            selectGroup.style.opacity = '1';
            selectGroup.style.pointerEvents = 'auto';
        }
        
        // 显示文章选择
        const articleSelect = document.getElementById('article-select');
        if (articleSelect) {
            articleSelect.disabled = false;
        }
        
        // 重新激活额外按钮
        const extraButtons = document.querySelector('.extra-buttons');
        if (extraButtons) {
            extraButtons.style.display = 'flex';
        }
        
        // 重新启用语言选择按钮
        if (langZhBtn) langZhBtn.disabled = false;
        if (langEnBtn) langEnBtn.disabled = false;
    }
    
    /**
     * 显示打字成绩结果对话框
     * @param {number} speed - 打字速度（字/分钟）
     * @param {number} accuracy - 准确率（百分比）
     */
    function showResultDialog(speed, accuracy) {
        // 显示结果弹窗
        const resultOverlay = document.getElementById('result-overlay');
        const speedResult = document.getElementById('speed-result');
        const accuracyResult = document.getElementById('accuracy-result');
        
        if (resultOverlay && speedResult && accuracyResult) {
            speedResult.textContent = speed + ' 字/分钟';
            accuracyResult.textContent = accuracy + '%';
            resultOverlay.style.display = 'flex';
            
            // 给结果弹窗添加关闭按钮事件
            const closeBtn = document.getElementById('close-result');
            if (closeBtn) {
                // 移除之前的事件监听器
                const newCloseBtn = closeBtn.cloneNode(true);
                closeBtn.parentNode.replaceChild(newCloseBtn, closeBtn);
                
                // 添加新的事件监听器
                newCloseBtn.addEventListener('click', function() {
                    resultOverlay.style.display = 'none';
                });
            }
        } else {
            // 如果结果弹窗不存在，使用alert显示结果
            alert(`打字练习完成！\n速度: ${speed} 字/分钟\n准确率: ${accuracy}%`);
        }
    }
    
    /**
     * 停止打字练习（用于中断练习）
     */
    function stopPractice() {
        // 如果当前不是活动状态，不执行任何操作
        if (!isTypingActive) return;
        
        console.log('停止打字练习');
        
        // 停止计时器
        if (practiceTimer) {
            clearInterval(practiceTimer);
            practiceTimer = null;
        }
        
        if (typingInterval) {
            clearInterval(typingInterval);
            typingInterval = null;
        }
        
        // 禁用所有输入框
        if (Array.isArray(inputElements)) {
            inputElements.forEach(inputEl => {
                if (inputEl) inputEl.disabled = true;
            });
        }
        
        // 停止监听键盘事件
        document.removeEventListener('keydown', preventCopyPasteShortcuts);
        
        // 更新状态
        isTypingActive = false;
        
        // 结束练习状态
        // 显示额外按钮
        const extraButtons = document.querySelector('.extra-buttons');
        if (extraButtons) {
            extraButtons.style.display = 'flex';
        }
        
        // 启用语言选择按钮
        if (langZhBtn) langZhBtn.disabled = false;
        if (langEnBtn) langEnBtn.disabled = false;
        
        // 重置开始按钮状态
        if (startPracticeBtn) {
            startPracticeBtn.disabled = false;
            startPracticeBtn.style.backgroundColor = '';
            startPracticeBtn.style.cursor = 'pointer';
            startPracticeBtn.style.display = 'block';
        }
        
        // 检查结束按钮是否存在
        if (finishPracticeBtn) {
            finishPracticeBtn.style.display = 'none';
        }
        
        // 启用文章选择下拉框
        const articleSelect = document.getElementById('article-select');
        if (articleSelect) {
            articleSelect.disabled = false;
        }
        
        // 启用文章选择区域的交互
        const selectGroup = document.querySelector('.select-group');
        if (selectGroup) {
            selectGroup.style.opacity = '1';
            selectGroup.style.pointerEvents = 'auto';
        }
    }
    
    /**
     * 阻止复制粘贴的键盘快捷键
     * @param {KeyboardEvent} e - 键盘事件对象
     */
    function preventCopyPasteShortcuts(e) {
        // 检测常见的复制粘贴快捷键
        if ((e.ctrlKey || e.metaKey) && (
            e.key === 'c' || 
            e.key === 'v' || 
            e.key === 'x' || 
            e.key === 'a' ||
            e.keyCode === 67 || // C键
            e.keyCode === 86 || // V键
            e.keyCode === 88 || // X键
            e.keyCode === 65    // A键
        )) {
            e.preventDefault();
            showMessage('禁止使用复制粘贴快捷键！', 'error');
            return false;
        }
    }
    
    /**
     * 保存练习记录
     * @param {number} speed - 打字速度
     * @param {number} accuracy - 准确率
     * @returns {Promise} 包含保存记录的Promise
     */
    async function savePracticeRecord(speed, accuracy) {
        console.log('开始保存练习记录...');
        
        if (!currentStudent) {
            const errorMsg = '无法保存记录：未找到学生信息';
            console.error(errorMsg);
            showMessage(errorMsg, 'error');
            return Promise.reject(errorMsg);
        }
        
        if (!currentSession) {
            const errorMsg = '无法保存记录：未找到会话信息';
            console.error(errorMsg);
            showMessage(errorMsg, 'error');
            
            // 如果有学生信息但无会话，尝试重新创建会话
            console.log('尝试重新创建会话...');
            createStudentSession(currentStudent);
            console.log('会话已重新创建:', currentSession ? '成功' : '失败');
            
            if (!currentSession) {
                return Promise.reject(errorMsg);
            }
        }
        
        // 转换年级格式为数字
        let gradeNumber;
        if (typeof Utils !== 'undefined' && Utils.gradeToNumber) {
            gradeNumber = Utils.gradeToNumber(currentStudent.grade);
        } else {
            // 如果Utils不可用，使用简单的格式转换
            const gradeMap = {
                '一年级': 1, '二年级': 2, '三年级': 3, 
                '四年级': 4, '五年级': 5, '六年级': 6
            };
            gradeNumber = gradeMap[currentStudent.grade] || 1;
        }
        
        // 数据保护：只输出结构信息，不输出敏感数据
        console.log('=== currentStudent 结构检查 ===');
        console.log('currentStudent 存在:', !!currentStudent);
        console.log('currentStudent 的属性数量:', Object.keys(currentStudent || {}).length);
        console.log('包含的字段:', {
            hasStudentIdentifier: !!currentStudent?.student_identifier,
            hasId: !!currentStudent?.id,
            hasName: !!currentStudent?.name,
            hasGrade: !!currentStudent?.grade,
            hasClass: !!currentStudent?.class
        });

        // 尝试多种可能的标识符字段
        const studentId = currentStudent?.student_identifier ||
                         currentStudent?.id ||
                         currentStudent?.studentId ||
                         currentStudent?.student_id ||
                         `${currentStudent?.grade}_${currentStudent?.class}_${currentStudent?.name}`;

        console.log('最终使用的学生标识符:', studentId);
        
        // 直接调用API保存到数据库
        const apiRecord = {
            student_identifier: studentId,
            speed: speed,
            accuracy: accuracy,
            date: new Date().toISOString()
        };

        console.log('尝试保存到数据库，API请求数据:', apiRecord);

        try {
            const response = await fetch('/api/typing-records', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(apiRecord)
            });

            console.log('API响应状态:', response.status, response.statusText);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('API请求失败，响应内容:', errorText);
                throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
            }

            const result = await response.json();
            console.log('数据库保存成功:', result);
            showMessage('成绩已成功保存到数据库', 'success');
            return result;
        } catch (error) {
            console.error('数据库保存失败:', error);
            showMessage('保存记录到数据库失败: ' + error.message, 'error');
            return Promise.reject(error);
        }
    }
    
    /**
     * 获取当前是第几周
     */
    function getWeekNumber(date) {
        const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
        const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
        return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
    }
    
    /**
     * 显示提示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     * @param {number} duration - 显示时长(毫秒)，默认3000ms
     */
    function showMessage(message, type = 'success', duration = 3000) {
        const messageBox = document.getElementById('message-box');
        const messageContent = messageBox.querySelector('.message-content');
        
        // 清除任何先前的定时器
        if (messageBox._timer) {
            clearTimeout(messageBox._timer);
            messageBox._timer = null;
        }
        
        // 设置消息内容
        messageContent.textContent = message;
        
        // 设置样式类
        messageBox.className = 'message-box';
        
        // 稍微延迟后再添加show类，确保过渡动画正常
        setTimeout(() => {
        messageBox.className = 'message-box show ' + (type || 'success');
        }, 10);
        
        // 添加图标提示
        let icon = '';
        switch (type) {
            case 'success': 
                icon = '✓'; 
                break;
            case 'error': 
                icon = '✕'; 
                break;
            case 'warning': 
                icon = '⚠'; 
                break;
            case 'info': 
                icon = 'ℹ'; 
                break;
        }
        
        if (icon) {
            messageContent.innerHTML = `<span style="margin-right:8px;font-weight:bold;">${icon}</span> ${message}`;
        }
        
        // 震动功能已禁用，避免在某些环境下被阻止
        // if (type === 'error' && navigator.vibrate) {
        //     try {
        //         navigator.vibrate(300);
        //     } catch (error) {
        //         console.log('震动功能不可用:', error.message);
        //     }
        // }
        
        // 指定时间后自动隐藏
        messageBox._timer = setTimeout(() => {
            messageBox.className = 'message-box';
            messageBox._timer = null;
        }, duration);
    }
    
    /**
     * 显示确认对话框
     * @param {string} message - 对话框消息
     * @param {Function} confirmCallback - 确认按钮回调函数
     * @param {string} title - 对话框标题
     */
    function showDialog(message, confirmCallback, title = '提示') {
        // 获取已有对话框容器或创建新的
        let dialogContainer = document.getElementById('dialog-container');
        
        if (!dialogContainer) {
        // 创建对话框容器
            dialogContainer = document.createElement('div');
        dialogContainer.className = 'dialog-container';
            dialogContainer.id = 'dialog-container';
            document.body.appendChild(dialogContainer);
        } else {
            // 如果已存在，先清空内容
            dialogContainer.innerHTML = '';
        }
        
        // 创建对话框
        const dialog = document.createElement('div');
        dialog.className = 'dialog';
        
        // 添加标题
        const dialogTitle = document.createElement('div');
        dialogTitle.className = 'dialog-title';
        dialogTitle.textContent = title;
        dialog.appendChild(dialogTitle);
        
        // 添加消息
        const dialogMessage = document.createElement('div');
        dialogMessage.className = 'dialog-message';
        dialogMessage.innerHTML = message;
        dialog.appendChild(dialogMessage);
        
        // 添加按钮容器
        const dialogButtons = document.createElement('div');
        dialogButtons.className = 'dialog-buttons';
        
        // 添加确认按钮
        const confirmButton = document.createElement('button');
        confirmButton.className = 'dialog-button confirm';
        confirmButton.textContent = '确定';
        confirmButton.addEventListener('click', () => {
            // 关闭对话框
            dialogContainer.style.display = 'none';
            
            // 调用回调函数
            if (typeof confirmCallback === 'function') {
                confirmCallback();
            }
        });
        
        // 添加取消按钮
        const cancelButton = document.createElement('button');
        cancelButton.className = 'dialog-button cancel';
        cancelButton.textContent = '取消';
        cancelButton.addEventListener('click', () => {
            // 只关闭对话框
            dialogContainer.style.display = 'none';
        });
        
        // 将按钮添加到按钮容器
        dialogButtons.appendChild(confirmButton);
        dialogButtons.appendChild(cancelButton);
        
        // 将按钮容器添加到对话框
        dialog.appendChild(dialogButtons);
        
        // 将对话框添加到容器
        dialogContainer.appendChild(dialog);
        
        // 显示对话框
        dialogContainer.style.display = 'flex';
        
        // 自动聚焦确认按钮
        setTimeout(() => confirmButton.focus(), 100);
        
        // 添加键盘事件处理
        const handleKeydown = (e) => {
            if (e.key === 'Enter') {
                // 按下回车，触发确认操作
                confirmButton.click();
                e.preventDefault();
            } else if (e.key === 'Escape') {
                // 按下Esc，触发取消操作
                cancelButton.click();
                e.preventDefault();
            }
        };
        
        // 添加键盘事件监听
        document.addEventListener('keydown', handleKeydown);
        
        // 在对话框关闭时移除键盘事件监听
        const removeListener = () => {
            document.removeEventListener('keydown', handleKeydown);
        };
        
        // 确保在对话框关闭时移除事件监听
        confirmButton.addEventListener('click', removeListener);
        cancelButton.addEventListener('click', removeListener);
    }
    
    /**
     * 显示历史记录页面
     */
    function showHistoryPage() {
        console.log('显示历史记录页面');

        // 检查是否有学生信息
        if (!currentStudent) {
            // 尝试从localStorage恢复学生信息
            const storedStudent = localStorage.getItem('currentStudent');
            if (storedStudent) {
                try {
                    currentStudent = JSON.parse(storedStudent);
                    console.log('从localStorage恢复学生信息:', currentStudent);
                } catch (error) {
                    console.error('解析学生信息失败:', error);
                }
            }

            // 如果仍然没有学生信息，从全局变量创建
            if (!currentStudent) {
                if (!studentName || !studentGrade || !studentClass) {
                    showMessage('无法获取学生信息，请重新签到', 'error');
                    return;
                }

                currentStudent = {
                    grade: studentGrade,
                    class: studentClass,
                    name: studentName,
                    seat: studentSeat,
                    school: studentSchool
                };
                console.log('从全局变量创建学生信息:', currentStudent);
            }
        }

        // 显示历史记录页面（添加安全检查）
        if (signinContainer) signinContainer.style.display = 'none';
        if (typingPractice) typingPractice.style.display = 'none';
        if (historyContainer) {
            historyContainer.style.display = 'block';
        } else {
            console.error('未找到历史记录容器元素');
            showMessage('历史记录页面不可用', 'error');
            return;
        }
        
        // 显示加载指示器
        showLoading(true);
        
        // 加载历史记录
        loadHistoryRecords()
            .then(() => {
                showLoading(false);
            })
            .catch(error => {
                console.error('加载历史记录失败:', error);
                showLoading(false);
                showMessage('加载历史记录失败: ' + error.message, 'error');
            });
    }
    
    /**
     * 加载历史记录
     */
    async function loadHistoryRecords() {
        if (!currentStudent) {
            showMessage('无法加载历史记录：未找到学生信息', 'error');
            return Promise.reject('未找到学生信息');
        }
        
        try {
            // 显示加载中
            showLoading(true);
            
            console.log('尝试从数据库加载历史记录...');
            
            // 确保年级是数字格式
            let gradeNumber = currentStudent.grade;
            
            // 如果年级不是数字，尝试转换
            if (isNaN(parseInt(gradeNumber))) {
                if (typeof convertGradeToNumber === 'function') {
                    gradeNumber = convertGradeToNumber(gradeNumber);
                } else if (typeof Utils !== 'undefined' && Utils.gradeToNumber) {
                    gradeNumber = Utils.gradeToNumber(gradeNumber);
                } else {
                    // 最后的备选方案
                    const gradeMap = {
                        '一年级': 1, '二年级': 2, '三年级': 3, 
                        '四年级': 4, '五年级': 5, '六年级': 6
                    };
                    gradeNumber = gradeMap[gradeNumber] || 1;
                }
                console.log('在加载历史记录时转换年级:', currentStudent.grade, '→', gradeNumber);
            }
            
            // 调试并获取学生标识符
            console.log('=== loadHistoryRecords currentStudent ===');
            console.log('currentStudent:', currentStudent);
            console.log('currentStudent 属性:', Object.keys(currentStudent || {}));

            const studentId = currentStudent?.student_identifier ||
                             currentStudent?.id ||
                             currentStudent?.studentId ||
                             currentStudent?.student_id ||
                             `${currentStudent?.grade}_${currentStudent?.class}_${currentStudent?.name}`;

            console.log('使用的学生标识符:', studentId);
            
            // 直接调用API获取记录
            console.log('调用API获取打字记录，学生标识符:', studentId);
            const response = await fetch(`/api/typing-records?student_identifier=${encodeURIComponent(studentId)}`);

            if (!response.ok) {
                if (response.status === 404) {
                    console.log('学生不存在或没有打字记录');
                    updateHistoryTable([]);
                    drawSpeedChart([]);
                    showLoading(false);
                    return [];
                } else {
                    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                }
            }

            const data = await response.json();
            console.log('API响应数据:', data);
            const records = data.records || data.data || [];
            
            if (!records || !Array.isArray(records)) {
                console.log('未找到历史记录或返回格式不正确');
                updateHistoryTable([]);
                drawSpeedChart([]);
                showLoading(false);
                return [];
            }
            
            console.log(`从数据库加载到${records.length}条记录`);
            
            // 将API返回的记录转换为前端格式
            const formattedRecords = records.map(record => ({
                grade: currentStudent.grade,
                class: currentStudent.class,
                name: currentStudent.name,
                seat: currentStudent.seat || '',
                speed: record.speed,
                accuracy: record.accuracy || 100,
                timestamp: record.created_at || record.date || new Date().toISOString(),
                synced: true // 数据库中的记录都被视为已同步
            }));
            
            // 更新历史记录表格
            updateHistoryTable(formattedRecords);
            
            // 绘制速度折线图
            drawSpeedChart(formattedRecords);
            
            // 隐藏加载中
            showLoading(false);
            
            return formattedRecords;
        } catch (error) {
            console.error('加载历史记录失败:', error);
            showMessage('加载历史记录失败: ' + error.message, 'error');
            updateHistoryTable([]);
            drawSpeedChart([]);
            showLoading(false);
            return [];
        }
    }
    
    /**
     * 更新历史记录表格
     * @param {Array} records - 打字记录数组
     */
    function updateHistoryTable(records) {
        const tableBody = document.getElementById('history-data');
        if (!tableBody) return;
        
        // 清空表格
        tableBody.innerHTML = '';
        
        if (!records || records.length === 0) {
            const noDataRow = document.createElement('tr');
            const noDataCell = document.createElement('td');
            noDataCell.colSpan = 8;
            noDataCell.textContent = '暂无记录';
            noDataCell.style.textAlign = 'center';
            noDataRow.appendChild(noDataCell);
            tableBody.appendChild(noDataRow);
            return;
        }
        
        console.log(`更新历史记录表格，共${records.length}条记录`);
        
        // 对记录按时间戳排序（降序）
        const sortedRecords = sortRecordsByTimestamp(records);
        
        // 计算显示时间和周数
        const now = new Date();
        
        // 创建表格行
        sortedRecords.forEach((record, index) => {
            const row = document.createElement('tr');
            
            // 计算记录日期
            const recordDate = new Date(record.timestamp);
            const dateFormatted = recordDate.toLocaleDateString();
            
            // 计算记录时间（HH:MM:SS）
            const timeFormatted = recordDate.toLocaleTimeString();
            
            // 计算练习周数（每7天算一周）
            const dayDiff = Math.floor((now - recordDate) / (1000 * 60 * 60 * 24));
            const weekNumber = Math.floor(dayDiff / 7) + 1;
            
            // 添加所有单元格
            appendCell(row, dateFormatted);
            appendCell(row, record.grade);
            appendCell(row, record.class);
            appendCell(row, record.name);
            appendCell(row, record.seat || '');
            appendCell(row, record.speed + ' 字/分钟');
            appendCell(row, timeFormatted);
            appendCell(row, '第 ' + weekNumber + ' 周');
            
            tableBody.appendChild(row);
        });
        
        // 辅助函数：添加单元格
        function appendCell(row, text) {
            const cell = document.createElement('td');
            cell.textContent = text;
            row.appendChild(cell);
        }
    }
    
    /**
     * 在历史页面初始化时设置事件监听器
     */
    function initHistoryPage() {
        // 返回练习按钮事件
        document.getElementById('back-to-practice').addEventListener('click', function() {
            historyContainer.style.display = 'none';
            typingPractice.style.display = 'block';
        });
        
        // 添加右键菜单导出功能
        const historyTable = document.getElementById('history-table');
        if (historyTable) {
            historyTable.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                exportToExcel();
                return false;
            });
        }
    }
    
    /**
     * 导出到Excel
     */
    function exportToExcel() {
        loadHistoryRecords().then(records => {
            if (!records || records.length === 0) {
                showMessage('没有可导出的记录', 'info');
                return;
            }
            
            processExcelExport(records);
        }).catch(error => {
            console.error('导出失败:', error);
            showMessage('导出失败: ' + error.message, 'error');
        });
    }
    
    /**
     * 处理Excel导出
     * @param {Array} records - 要导出的记录
     */
    function processExcelExport(records) {
        try {
            // 准备数据：表头
            const headers = ['日期', '时间', '年级', '班级', '姓名', '座位号', '语言', '速度(字/分钟)', '正确率(%)'];
            
            // 准备数据：内容行
            const data = records.map(record => {
                // 格式化日期和时间
                const date = new Date(record.timestamp);
                const dateStr = date.toLocaleDateString();
                const timeStr = date.toLocaleTimeString();
                
                return [
                    dateStr,
                    timeStr,
                record.grade,
                record.class,
                record.name,
                record.seat,
                    record.language === 'zh' ? '中文' : '英文',
                record.speed,
                record.accuracy
                ];
            });
            
            // 创建工作表
            const ws = XLSX.utils.aoa_to_sheet([headers, ...data]);
            
            // 设置列宽
            const colWidths = [
                { wch: 12 }, // 日期
                { wch: 10 }, // 时间
                { wch: 8 },  // 年级
                { wch: 10 }, // 班级
                { wch: 10 }, // 姓名
                { wch: 8 },  // 座位号
                { wch: 8 },  // 语言
                { wch: 15 }, // 速度
                { wch: 10 }  // 正确率
            ];
            ws['!cols'] = colWidths;
            
            // 创建工作簿
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "打字练习记录");
            
            // 导出Excel文件
            XLSX.writeFile(wb, `打字练习记录_${new Date().toLocaleDateString().replace(/\//g, '-')}.xlsx`);
            
            showMessage('记录已成功导出为Excel文件');
        } catch (error) {
            console.error('导出Excel时出错:', error);
            showMessage('导出Excel文件时出错', 'error');
        }
    }

    /**
     * 每个年级对应的班级数量
     */
    let classCountByGrade = {
        '一年级': 10,
        '二年级': 8,
        '三年级': 8,
        '四年级': 6,
        '五年级': 6,
        '六年级': 4
    };
    
    /**
     * 更新年级选项
     */
    async function updateGradeOptions() {
        const schoolSelect = document.getElementById('school');
        const gradeSelect = document.getElementById('grade');
        const classSelect = document.getElementById('class');

        if (!schoolSelect || !gradeSelect || !classSelect) return;

        const schoolId = schoolSelect.value;

        // 清空年级和班级选项
        gradeSelect.innerHTML = '<option value="">请选择年级</option>';
        classSelect.innerHTML = '<option value="">请选择班级</option>';

        if (!schoolId) {
            gradeSelect.disabled = true;
            classSelect.disabled = true;
            return;
        }

        try {
            // 显示加载状态
            gradeSelect.innerHTML = '<option value="">正在加载年级...</option>';
            gradeSelect.disabled = true;

            // 获取学校的年级配置
            const response = await fetch(`/api/schools/${schoolId}/grades`);

            if (response.ok) {
                const result = await response.json();
                const grades = (result && result.success !== false)
                    ? (Array.isArray(result.data) ? result.data : (Array.isArray(result) ? result : []))
                    : [];

                if (grades.length > 0) {
                    gradeSelect.innerHTML = '<option value="">请选择年级</option>' +
                        grades.map(grade => `<option value="${grade.grade}">${grade.grade}年级</option>`).join('');
                    gradeSelect.disabled = false;

                    // 更新全局的年级班级配置
                    classCountByGrade = {};
                    grades.forEach(grade => {
                        classCountByGrade[grade.grade] = grade.class_count;
                    });
                } else {
                    gradeSelect.innerHTML = '<option value="">该学校暂无年级配置</option>';
                    gradeSelect.disabled = true;
                }
            } else {
                gradeSelect.innerHTML = '<option value="">加载年级失败</option>';
                gradeSelect.disabled = true;
            }
        } catch (error) {
            console.error('加载学校年级失败:', error);
            gradeSelect.innerHTML = '<option value="">加载年级失败</option>';
            gradeSelect.disabled = true;
        }

        classSelect.disabled = true;
    }

    /**
     * 更新班级选项
     */
    function updateClassOptions() {
        // 检查必要的DOM元素是否存在
        if (!gradeSelect || !classSelect) {
            console.log('年级或班级选择元素不存在，跳过更新班级选项');
            return;
        }

        console.log('更新班级选项，当前年级:', gradeSelect.value);
        const selectedGrade = gradeSelect.value;

        // 清空现有选项
        classSelect.innerHTML = '';

        // 添加默认选项
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = '请选择班级';
        defaultOption.disabled = true;
        defaultOption.selected = true;
        classSelect.appendChild(defaultOption);

        if (selectedGrade) {
            // 获取选中年级的班级数量
            const classCount = classCountByGrade[selectedGrade] || 0;
            console.log('班级数量:', classCount, '年级:', selectedGrade);

            // 添加班级选项
            for (let i = 1; i <= classCount; i++) {
                const option = document.createElement('option');
                option.value = i.toString();
                option.textContent = `${i}班`;
                classSelect.appendChild(option);
            }

            // 启用班级选择
            classSelect.disabled = false;
        } else {
            // 禁用班级选择
            classSelect.disabled = true;
        }

        // 检查是否有选项被添加
        if (classSelect.options.length <= 1) {
            console.warn('警告: 没有班级选项被添加');
        } else {
            console.log('成功添加班级选项，总数:', classSelect.options.length - 1);
        }
    }

    /**
     * 按时间戳排序记录（降序）
     * @param {Array} records - 要排序的记录数组
     * @returns {Array} 排序后的记录数组
     */
    function sortRecordsByTimestamp(records) {
        if (!records || !Array.isArray(records)) {
            return [];
        }
        return [...records].sort((a, b) => {
            const dateA = new Date(a.timestamp);
            const dateB = new Date(b.timestamp);
            return dateB - dateA; // 降序排列，最新的记录在前
        });
    }

    /**
     * 显示或隐藏加载指示器
     * @param {boolean} show - 是否显示加载指示器
     */
    function showLoading(show) {
        const loadingEl = document.getElementById('loading');
        if (loadingEl) {
            loadingEl.style.display = show ? 'flex' : 'none';
        }
    }

    /**
     * 绘制打字速度折线图
     * @param {Array} records - 打字记录数组
     */
    function drawSpeedChart(records) {
        // 获取canvas元素
        const canvas = document.getElementById('typing-speed-chart');
        if (!canvas) {
            console.error('找不到图表Canvas元素');
            return;
        }
        
        // 确保Chart.js已加载
        if (typeof Chart === 'undefined') {
            console.error('Chart.js未加载');
            return;
        }
        
        // 清除可能存在的旧图表
        const chartInstance = Chart.getChart(canvas);
        if (chartInstance) {
            chartInstance.destroy();
        }
        
        if (!records || records.length === 0) {
            // 没有记录时显示提示
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.font = '18px Arial';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('暂无打字记录数据', canvas.width / 2, canvas.height / 2);
            return;
        }
        
        // 对记录按时间排序（升序，最早的在前）
        const sortedRecords = [...records].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
        
        // 提取日期和速度
        const labels = sortedRecords.map(record => {
            const date = new Date(record.timestamp);
            return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
        });
        
        const speeds = sortedRecords.map(record => record.speed);
        const accuracies = sortedRecords.map(record => record.accuracy);
        
        // 计算速度的平均值
        const avgSpeed = speeds.reduce((sum, speed) => sum + speed, 0) / speeds.length;
        
        // 创建图表
        const ctx = canvas.getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '打字速度 (字/分钟)',
                        data: speeds,
                        borderColor: '#4a90e2',
                        backgroundColor: 'rgba(74, 144, 226, 0.1)',
                        borderWidth: 3,
                        pointBackgroundColor: '#4a90e2',
                        pointRadius: 5,
                        pointHoverRadius: 7,
                        fill: true,
                        tension: 0.2, // 使线条更平滑
                        yAxisID: 'y'
                    },
                    {
                        label: '平均速度',
                        data: Array(labels.length).fill(avgSpeed),
                        borderColor: '#27ae60',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        pointRadius: 0,
                        fill: false,
                        yAxisID: 'y'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                aspectRatio: 2, // 设置宽高比为2:1
                responsiveAnimationDuration: 0, // 禁用调整大小时的动画
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            font: {
                                size: 14
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        titleFont: {
                            size: 14
                        },
                        bodyFont: {
                            size: 14
                        },
                        padding: 12,
                        callbacks: {
                            title: function(tooltipItems) {
                                const idx = tooltipItems[0].dataIndex;
                                const date = new Date(sortedRecords[idx].timestamp);
                                return date.toLocaleString();
                            },
                            label: function(context) {
                                if (context.dataset.label === '平均速度') {
                                    return `平均速度: ${avgSpeed.toFixed(1)} 字/分钟`;
                                }
                                return `速度: ${context.parsed.y} 字/分钟`;
                            },
                            afterLabel: function(context) {
                                if (context.dataset.label !== '平均速度') {
                                    const idx = context.dataIndex;
                                    return `正确率: ${sortedRecords[idx].accuracy}%`;
                                }
                                return '';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            color: '#666',
                            maxRotation: 45,
                            minRotation: 45,
                            font: {
                                size: 12
                            }
                        },
                        title: {
                            display: true,
                            text: '练习时间',
                            color: '#666',
                            font: {
                                size: 14,
                                weight: 'bold'
                            },
                            padding: {top: 10, bottom: 0}
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            color: '#666',
                            font: {
                                size: 12
                            },
                            callback: function(value) {
                                return value + ' 字/分';
                            }
                        },
                        title: {
                            display: true,
                            text: '打字速度 (字/分钟)',
                            color: '#666',
                            font: {
                                size: 14,
                                weight: 'bold'
                            },
                            padding: {top: 0, bottom: 10}
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                },
                elements: {
                    line: {
                        tension: 0.3 // 线条弯曲度
                    }
                },
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                // 限制图表高度
                layout: {
                    padding: {
                        top: 5,
                        right: 5,
                        bottom: 5,
                        left: 5
                    }
                }
            }
        });
    }

    /**
     * 重置计时器
     */
    function resetTimer() {
        // 重置状态
        practiceStartTime = new Date();
        practiceEndTime = null;
        activeLineIndex = 0;
        
        // 清空所有输入框
        inputElements.forEach((inputEl, index) => {
            if (!inputEl) return;
            inputEl.value = '';
            inputEl.disabled = index !== 0; // 只启用第一个输入框
            
            // 重置文本行样式
            const textEl = document.getElementById(`typing-text-${index}`);
            if (textEl) {
                textEl.classList.remove('line-completed');
                if (index === 0) {
                    textEl.classList.add('current-line');
                } else {
                    textEl.classList.remove('current-line');
                }
                
                // 重置所有span样式
                const spans = textEl.querySelectorAll('span');
                spans.forEach(span => {
                    span.className = '';
                });
                
                // 只将第一行的第一个字符标记为当前字符
                if (index === 0 && spans.length > 0) {
                    spans[0].className = 'current';
                }
            }
        });
        
        // 重置统计数据
        currentSpeedEl.textContent = '0';
        accuracyEl.textContent = '100%';
    }

    /**
     * 启动计时器
     */
    function startTimer() {
        // 启动计时器（5分钟倒计时）
        let remainingSeconds = 5 * 60;
        updateTimer(remainingSeconds);
        
        practiceTimer = setInterval(function() {
            remainingSeconds--;
            
            if (remainingSeconds <= 0) {
                // 时间到，自动结束练习
                clearInterval(practiceTimer);
                practiceTimer = null;
                finishPractice();
                return;
            }
            
            updateTimer(remainingSeconds);
        }, 1000);
        
        // 添加键盘快捷键防护
        document.addEventListener('keydown', preventCopyPasteShortcuts);
        
        typingInterval = setInterval(updateOverallStats, 1000);
    }

    /**
     * 更新界面状态
     * @param {boolean} isActive - 是否处于活动状态
     */
    function updateInterfaceState(isActive) {
        // 更新按钮状态
        if (isActive) {
            // 进入练习状态
            // 隐藏额外按钮
            const extraButtons = document.querySelector('.extra-buttons');
            if (extraButtons) {
                extraButtons.style.display = 'none';
            }
            
            // 禁用语言选择按钮
            if (langZhBtn) langZhBtn.disabled = true;
            if (langEnBtn) langEnBtn.disabled = true;
            
            // 设置开始按钮为禁用状态
            if (startPracticeBtn) {
                startPracticeBtn.disabled = true;
                startPracticeBtn.style.backgroundColor = '#ccc';
                startPracticeBtn.style.cursor = 'not-allowed';
            }
            
            // 检查结束按钮是否存在
            if (finishPracticeBtn) {
            finishPracticeBtn.style.display = 'block';
            }
            if (startPracticeBtn) {
            startPracticeBtn.style.display = 'none';
            }
            
            // 禁用文章选择下拉框
            const articleSelect = document.getElementById('article-select');
            if (articleSelect) {
                articleSelect.disabled = true;
            }
            
            // 禁用文章选择区域的交互
            const selectGroup = document.querySelector('.select-group');
            if (selectGroup) {
                selectGroup.style.opacity = '0.6';
                selectGroup.style.pointerEvents = 'none';
            }
        } else {
            // 结束练习状态
            // 显示额外按钮
            const extraButtons = document.querySelector('.extra-buttons');
            if (extraButtons) {
                extraButtons.style.display = 'flex';
            }
            
            // 启用语言选择按钮
            if (langZhBtn) langZhBtn.disabled = false;
            if (langEnBtn) langEnBtn.disabled = false;
            
            // 重置开始按钮状态
            if (startPracticeBtn) {
                startPracticeBtn.disabled = false;
                startPracticeBtn.style.backgroundColor = '';
                startPracticeBtn.style.cursor = 'pointer';
            startPracticeBtn.style.display = 'block';
            }
            
            // 检查结束按钮是否存在
            if (finishPracticeBtn) {
            finishPracticeBtn.style.display = 'none';
            }
            
            // 启用文章选择下拉框
            const articleSelect = document.getElementById('article-select');
            if (articleSelect) {
                articleSelect.disabled = false;
            }
            
            // 启用文章选择区域的交互
            const selectGroup = document.querySelector('.select-group');
            if (selectGroup) {
                selectGroup.style.opacity = '1';
                selectGroup.style.pointerEvents = 'auto';
            }
        }
    }

    // 初始化应用
    init().catch(err => {
        console.error('初始化应用失败:', err);
        showMessage('初始化应用失败，请刷新页面重试', 'error');
    });

    /**
     * 更新学生信息显示
     */
    async function updateStudentInfoDisplay() {
        const studentInfoEl = document.getElementById('student-info');
        if (studentInfoEl && currentStudent) {
            // 获取学校名称（如果是ID，需要转换为名称）
            let schoolName = currentStudent.school;
            if (typeof schoolName === 'number' || !isNaN(schoolName)) {
                // 如果是数字ID，从学校列表中查找名称
                try {
                    const schools = await fetchSchools();
                    const school = schools.find(s => s.id == schoolName);
                    schoolName = school ? school.name : `学校ID: ${schoolName}`;
                } catch (error) {
                    console.error('获取学校名称失败:', error);
                    schoolName = `学校ID: ${schoolName}`;
                }
            }

            const infoText = `${currentStudent.name} | ${schoolName} | ${currentStudent.grade}年级${currentStudent.class}班 | 座位号: ${currentStudent.seat}`;
            studentInfoEl.textContent = infoText;
            console.log('更新学生信息显示:', infoText);
        } else {
            console.log('学生信息元素不存在或学生信息为空');
        }
    }

    /**
     * 显示签到成功页面
     */
    function showSigninSuccessPage() {
        console.log('显示签到成功页面');

        // 隐藏签到界面
        signinContainer.style.display = 'none';

        // 显示签到成功界面
        const signinSuccessContainer = document.getElementById('signin-success');
        if (signinSuccessContainer) {
            signinSuccessContainer.style.display = 'flex';
        }
    }

    /**
     * 显示学生主页
     */
    async function showStudentPage() {
        console.log('签到成功，准备跳转到打字练习页面');

        // 创建学生对象并保存到localStorage
        const studentInfo = {
            school: schoolSchool,
            grade: studentGrade,
            class: studentClass,
            name: studentName,
            seat: studentSeat,
            sessionStartTime: new Date().toISOString()
        };

        // 保存学生信息到localStorage，供打字页面使用
        localStorage.setItem('currentStudent', JSON.stringify(studentInfo));

        // 延迟一下让用户看到成功消息，然后跳转
        setTimeout(() => {
            console.log('跳转到打字练习页面: /student/stutype');
            window.location.href = '/student/stutype';
        }, 1500);
    }

    /**
     * 显示打字问题
     */
    function showTypingQuestion() {
        // 如果已答完所有问题，则直接进入打字练习
        if (currentQuestionIndex >= typingQuestions.length || allQuestionsAnswered) {
            showTypingPracticePage();
            return;
        }
        
        // 隐藏所有页面
        hideAllPages();
        
        // 重置问题尝试次数
        questionAttempts = 0;
        
        // 获取当前问题
        const currentQuestion = typingQuestions[currentQuestionIndex];
        
        // 如果问题容器不存在，创建它
        let questionContainer = document.getElementById('question-container');
        if (!questionContainer) {
            questionContainer = document.createElement('div');
            questionContainer.id = 'question-container';
            questionContainer.className = 'question-container glass-effect';
            document.body.appendChild(questionContainer);
            
            // 创建标题
            const header = document.createElement('header');
            const title = document.createElement('h1');
            title.textContent = '打字技能检测';
            
            header.appendChild(title);
            questionContainer.appendChild(header);
        } else {
            // 清空容器内容，保留标题
            const header = questionContainer.querySelector('header');
            questionContainer.innerHTML = '';
            questionContainer.appendChild(header);
        }
        
        // 创建问题框
        const questionBox = document.createElement('div');
        questionBox.className = 'question-box';
        
        // 创建问题内容
        const questionText = document.createElement('div');
        questionText.className = 'question-text';
        questionText.textContent = currentQuestion.question;
        
        // 创建进度显示
        const questionProgress = document.createElement('div');
        questionProgress.className = 'question-progress';
        
        const progressText = document.createElement('div');
        progressText.className = 'progress-text';
        progressText.textContent = `问题 ${currentQuestionIndex + 1} / ${typingQuestions.length}`;
        
        const progressBar = document.createElement('div');
        progressBar.className = 'progress-bar';
        
        const progressFill = document.createElement('div');
        progressFill.className = 'progress-fill';
        progressFill.style.width = `${((currentQuestionIndex) / typingQuestions.length) * 100}%`;
        
        progressBar.appendChild(progressFill);
        questionProgress.appendChild(progressText);
        questionProgress.appendChild(progressBar);
        
        // 创建说明文本
        const instructionText = document.createElement('div');
        instructionText.className = 'instruction-text';
        instructionText.textContent = '请按下符合问题描述的键盘按键。必须答对所有问题才能继续。';
        
        // 组装问题容器
        questionBox.appendChild(questionText);
        questionBox.appendChild(questionProgress);
        questionBox.appendChild(instructionText);
        
        // 添加问题框到容器
        questionContainer.appendChild(questionBox);
        
        // 显示问题容器
        questionContainer.style.display = 'flex';
        
        // 自动设置焦点并添加键盘事件监听
        document.addEventListener('keydown', handleQuestionKeydown);
        
        console.log('问题页面已显示，当前问题索引:', currentQuestionIndex);
    }

    /**
    /**
     * 处理打字问题的按键事件
     * @param {KeyboardEvent} event - 键盘事件
     */
    function handleQuestionKeydown(event) {
        // 阻止默认行为
        event.preventDefault();
        
        // 忽略按键释放事件
        if (event.type === 'keyup') return;
        
        // 获取当前问题
        const currentQuestion = typingQuestions[currentQuestionIndex];
        
        // 获取学生输入的按键
        let pressedKey = event.key;
        
        // 特殊按键处理
        if (pressedKey === ' ') pressedKey = '空格键';
        
        // 检查答案是否正确
        const correctKey = currentQuestion.answer;
        let displayKey = correctKey;
        
        // 将正确答案转换为显示格式
        if (correctKey === ' ') displayKey = '空格键';
        else if (correctKey === 'Tab') displayKey = 'Tab键';
        else if (correctKey === 'Enter') displayKey = '回车键';
        else if (correctKey === 'CapsLock') displayKey = '大小写锁定键';
        else if (correctKey === 'ArrowUp') displayKey = '↑方向键';
        
        // 检查答案是否正确
        const isCorrect = pressedKey === correctKey || 
                          (pressedKey === '空格键' && correctKey === ' ');
        
        // 处理答案
        handleQuestionAnswer(isCorrect, displayKey, pressedKey);
    }

    /**
     * 处理学生对问题的回答
     * @param {boolean} isCorrect - 回答是否正确
     * @param {string} correctKey - 正确的按键
     * @param {string} pressedKey - 学生按下的按键
     */
    function handleQuestionAnswer(isCorrect, correctKey, pressedKey) {
        const questionContainer = document.getElementById('question-container');
        const questionText = questionContainer.querySelector('.question-text');
        
        // 更新尝试次数
        questionAttempts++;
        
        // 获取底部的尝试进度条
        let attemptsProgress = questionContainer.querySelector('.attempts-progress-bar');
        if (!attemptsProgress) {
            // 如果不存在，创建尝试进度条
            const attemptsContainer = document.createElement('div');
            attemptsContainer.className = 'attempts-container';
            
            const attemptsLabel = document.createElement('div');
            attemptsLabel.className = 'attempts-label';
            attemptsLabel.textContent = `尝试次数: ${questionAttempts}/${MAX_ATTEMPTS}`;
            
            attemptsProgress = document.createElement('div');
            attemptsProgress.className = 'attempts-progress-bar';
            
            const attemptsProgressFill = document.createElement('div');
            attemptsProgressFill.className = 'attempts-progress-fill';
            
            attemptsProgress.appendChild(attemptsProgressFill);
            attemptsContainer.appendChild(attemptsLabel);
            attemptsContainer.appendChild(attemptsProgress);
            
            // 添加到问题框底部
            const questionBox = questionContainer.querySelector('.question-box');
            questionBox.appendChild(attemptsContainer);
        }
        
        // 更新尝试进度条
        const attemptsProgressFill = attemptsProgress.querySelector('.attempts-progress-fill');
        const attemptsLabel = questionContainer.querySelector('.attempts-label');
        attemptsLabel.textContent = `尝试次数: ${questionAttempts}/${MAX_ATTEMPTS}`;
        attemptsProgressFill.style.width = `${(questionAttempts / MAX_ATTEMPTS) * 100}%`;
        
        // 根据尝试次数更新尝试进度条的样式
        if (questionAttempts === 1) {
            attemptsProgressFill.style.backgroundColor = '#ffc107'; // 黄色
        } else if (questionAttempts === 2) {
            attemptsProgressFill.style.backgroundColor = '#ff5722'; // 橙色
        } else if (questionAttempts >= 3) {
            attemptsProgressFill.style.backgroundColor = '#f44336'; // 红色
        }
        
        if (isCorrect) {
            // 回答正确 - 立即进入下一题，不播放声音和等待
            
            // 移除document的键盘事件监听器
            document.removeEventListener('keydown', handleQuestionKeydown);
            
            // 直接进入下一题
            currentQuestionIndex++;
            showTypingQuestion();
        } else {
            // 回答错误 - 不播放声音
            
            if (questionAttempts >= MAX_ATTEMPTS) {
                // 三次尝试后显示正确答案弹窗
                const dialogMessage = `<strong>正确答案:</strong> 应该按 "${correctKey}" 键。<br>您已尝试${MAX_ATTEMPTS}次，请务必记住正确答案。`;
                
                showDialog(dialogMessage, () => {
                    // 确认后清除当前问题的尝试次数，让学生重新尝试
                    questionAttempts = 0;
                    
                    // 重置尝试进度条
                    attemptsProgressFill.style.width = '0%';
                    attemptsProgressFill.style.backgroundColor = '#4a90e2';
                    attemptsLabel.textContent = `尝试次数: ${questionAttempts}/${MAX_ATTEMPTS}`;
                    
                    // 移除所有答案提示
                    const answerReveals = questionText.querySelectorAll('.error-notification');
                    answerReveals.forEach(reveal => reveal.remove());
                    
                }, '答案提示');
            } else {
                // 未达到最大尝试次数，显示醒目的错误提示
                
                // 先移除可能存在的之前的错误提示
                const existingErrors = document.querySelectorAll('.error-notification');
                existingErrors.forEach(error => error.remove());
                
                // 创建新的错误提示元素（固定位置，更加醒目）
                const errorNotification = document.createElement('div');
                errorNotification.className = 'error-notification';
                errorNotification.innerHTML = `<strong>不正确!</strong> 您按的是 "${pressedKey}" 键，请再试一次。`;
                
                // 将错误提示添加到问题容器顶部
                const questionBox = questionContainer.querySelector('.question-box');
                questionBox.insertBefore(errorNotification, questionBox.firstChild);
                
                // 3秒后自动移除错误提示
                setTimeout(() => {
                    errorNotification.classList.add('fade-out');
                    setTimeout(() => {
                        errorNotification.remove();
                    }, 500); // 淡出动画后再移除
                }, 3000);
            }
        }
    }

    // 重置学生变量的处理方法
    function resetStudentVariables() {
        studentGrade = '';
        studentClass = '';
        studentName = '';
        studentSeat = '';
        studentIdentifier = '';
        currentQuestionIndex = 0;
        questionAttempts = 0;
        allQuestionsAnswered = false;
    }

    /**
     * 播放音效
     * @param {string} type - 音效类型：'correct' 或 'error'
     */
    function playSound(type) {
        // 创建音频上下文
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        // 连接节点
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        // 设置音效参数
        if (type === 'correct') {
            // 正确答案的音效 - 愉快的上升音调
            oscillator.type = 'sine';
            oscillator.frequency.setValueAtTime(660, audioContext.currentTime);
            oscillator.frequency.linearRampToValueAtTime(880, audioContext.currentTime + 0.2);
            
            gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3);
            
            oscillator.start();
            oscillator.stop(audioContext.currentTime + 0.3);
        } else if (type === 'error') {
            // 错误答案的音效 - 不和谐的下降音调
            oscillator.type = 'square';
            oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
            oscillator.frequency.linearRampToValueAtTime(220, audioContext.currentTime + 0.2);
            
            gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3);
            
            oscillator.start();
            oscillator.stop(audioContext.currentTime + 0.3);
        }
    }

    /**
     * 根据难度值返回星级表示
     * @param {number} difficulty - 难度值(1-5)
     * @returns {string} 星级表示
     */
    function getDifficultyStars(difficulty) {
        // 确保难度在1-5之间
        const level = Math.min(Math.max(parseInt(difficulty) || 3, 1), 5);
        // 返回对应数量的星星
        return '★'.repeat(level);
    }

    /**
     * 创建文章选择列表
     */
    function createArticleSelectionList() {
        const articleSelect = document.getElementById('article-select');
        if (!articleSelect) return;
        
        // 清空现有选项，保留默认选项
        articleSelect.innerHTML = '<option value="" disabled selected>请选择练习文章</option>';
        
        // 创建中文文章组
        const zhGroup = document.createElement('optgroup');
        zhGroup.label = '中文文章';
        
        // 添加中文文章
        if (chineseArticles.length > 0) {
        chineseArticles.forEach(article => {
            const option = document.createElement('option');
            option.value = 'zh_' + article.id;
                // 使用星级表示难度而不显示年级
                const difficultyStars = getDifficultyStars(article.difficulty);
                option.textContent = `${article.title} ${difficultyStars}`;
            zhGroup.appendChild(option);
        });
        } else {
            // 如果没有中文文章，添加提示选项
            const option = document.createElement('option');
            option.value = "";
            option.textContent = "没有可用的中文文章";
            option.disabled = true;
            zhGroup.appendChild(option);
        }
        
        // 创建英文文章组
        const enGroup = document.createElement('optgroup');
        enGroup.label = '英文文章';
        
        // 添加英文文章
        if (englishArticles.length > 0) {
        englishArticles.forEach(article => {
            const option = document.createElement('option');
            option.value = 'en_' + article.id;
                // 使用星级表示难度而不显示年级
                const difficultyStars = getDifficultyStars(article.difficulty);
                option.textContent = `${article.title} ${difficultyStars}`;
            enGroup.appendChild(option);
        });
        } else {
            // 如果没有英文文章，添加提示选项
            const option = document.createElement('option');
            option.value = "";
            option.textContent = "没有可用的英文文章";
            option.disabled = true;
            enGroup.appendChild(option);
        }
        
        // 添加分组到选择框
        articleSelect.appendChild(zhGroup);
        articleSelect.appendChild(enGroup);
        
        // 监听选择变化
        articleSelect.addEventListener('change', function() {
            const selectedId = this.value;
            
            if (!selectedId || selectedId === '') {
                selectedArticle = null;
                return;
            }
            
            // 解析语言前缀和文章ID
            const [lang, articleId] = selectedId.split('_');
            
            // 根据语言选择文章数组
            const articleArray = lang === 'zh' ? chineseArticles : englishArticles;
            selectedArticle = articleArray.find(article => article.id.toString() === articleId);
            
            if (selectedArticle) {
                // 设置当前语言
                currentLanguage = lang;
                
                // 更新语言按钮状态
                if (langZhBtn) {
                    langZhBtn.className = lang === 'zh' ? 'lang-btn active' : 'lang-btn';
                }
                
                if (langEnBtn) {
                    langEnBtn.className = lang === 'en' ? 'lang-btn active' : 'lang-btn';
                }
                
                // 生成文本并显示
                textContent = selectedArticle.content;
                generateTypingText();
                displayText(textLines);
                
                // 显示开始练习按钮
                document.getElementById('start-practice').style.display = 'block';
                
                // 确保打字区域可见
                typingLinesContainer.style.display = 'block';
                
                // 平滑滚动到打字区域
                typingLinesContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
                
                // 显示提示信息
                showMessage(`文章"${selectedArticle.title}"已加载，点击"开始练习"按钮开始`, 'info');
                
                // 高亮开始练习按钮
                const startBtn = document.getElementById('start-practice');
                if (startBtn) {
                    startBtn.classList.add('highlight-btn');
                    setTimeout(() => {
                        startBtn.classList.remove('highlight-btn');
                    }, 3000);
                }
            }
        });
    }

    /**
     * 设置当前练习语言
     * @param {string} lang - 要设置的语言代码（'zh'或'en'）
     */
    function setLanguage(lang) {
        // 如果正在进行练习，则不能切换语言
        if (isTypingActive) return;
        
        if (lang === 'zh' || lang === 'en') {
            currentLanguage = lang;
            
            // 更新语言按钮状态
            if (langZhBtn) {
                langZhBtn.className = lang === 'zh' ? 'lang-btn active' : 'lang-btn';
            }
            
            if (langEnBtn) {
                langEnBtn.className = lang === 'en' ? 'lang-btn active' : 'lang-btn';
            }
            
            console.log(`语言切换到: ${lang === 'zh' ? '中文' : '英文'}`);
            
            // 重置练习文本区域
            if (typingLinesContainer) {
                typingLinesContainer.innerHTML = '';
            }
            
            // 重置选择的文章
            selectedArticle = null;
            
            // 更新文章选择区标题文字
            document.getElementById('article-select').value = '';
            
            // 显示提示信息
            typingLinesContainer.innerHTML = '<div class="article-selection-tip">请先选择一篇您想要练习的文章</div>';
            
            // 创建文章选择列表
            createArticleSelectionList();
            
            // 隐藏开始练习按钮
            document.getElementById('start-practice').style.display = 'none';
        }
    }

    /**
     * 启动学生会话
     * @returns {Promise<boolean>} 启动结果
     */
    async function startStudentSession() {
        try {
            // 创建学生对象
            currentStudent = {
                school: schoolSelect.value,
                grade: studentGrade,
                class: studentClass,
                name: studentName,
                seat: studentSeat
            };
            
            // 尝试结束可能存在的活动会话
            // 获取当前年级数字
            let gradeNumber;
            if (typeof Utils !== 'undefined' && Utils.gradeToNumber) {
                gradeNumber = Utils.gradeToNumber(studentGrade);
            } else {
                // 如果Utils不可用，使用简单转换
                const gradeMap = {
                    '一年级': 1, '二年级': 2, '三年级': 3, 
                    '四年级': 4, '五年级': 5, '六年级': 6
                };
                gradeNumber = gradeMap[studentGrade] || 1;
            }
            
            // 构建学生标识符用于结束会话
            const studentId = `${gradeNumber}_${studentClass}_${studentName}`;
            
            // 静默尝试结束现有会话，忽略可能的错误
            try {
                await endActiveSession(studentId);
                console.log('预先尝试结束可能存在的会话');
            } catch (e) {
                console.log('预先结束会话时发生错误，将继续验证流程', e);
            }
            
            // 验证学生信息
            const isValid = await verifyStudent(currentStudent);
            
            if (isValid) {
                // 验证成功，但不再保存到localStorage
                console.log('学生会话启动成功，会话ID:', currentStudent.sessionId);
                console.log('会话开始时间:', currentStudent.sessionStartTime);
                
                // 根据设置，不保存会话状态到localStorage
                // localStorage.setItem('currentStudent', JSON.stringify(currentStudent));
                
                // 成功启动会话
                return true;
            } else {
                // 验证失败
                console.error('学生信息验证失败');
                return false;
            }
        } catch (error) {
            console.error('启动学生会话错误:', error);
            showMessage('无法启动学生会话，请稍后重试', 'error');
            return false;
        }
    }

    /**
     * 结束活动的会话
     * @param {string} studentId - 学生标识符
     * @returns {Promise<boolean>} 是否成功结束会话
     */
    async function endActiveSession(studentId) {
        try {
            console.log('尝试结束活动会话:', studentId);
            
            const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.SESSION_END}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ student_identifier: studentId })
            });
            
            const data = await response.json();
            console.log('结束会话响应:', data);
            
            if (response.ok) {
                console.log('成功结束会话');
                return true;
            } else {
                console.error('结束会话失败:', data.error);
                return false;
            }
        } catch (error) {
            console.error('结束会话时发生错误:', error);
            return false;
        }
    }

    /**
     * 隐藏所有页面元素
     * 在显示新页面前调用，确保界面干净
     */
    function hideAllPages() {
        // 隐藏签到页面
        if (signinContainer) signinContainer.style.display = 'none';
        
        // 隐藏学生主容器
        if (studentContainer) studentContainer.style.display = 'none';
        
        // 隐藏打字练习页面
        if (typingPractice) typingPractice.style.display = 'none';
        
        // 隐藏历史记录页面
        if (historyContainer) historyContainer.style.display = 'none';
        
        // 隐藏问题容器
        const questionContainer = document.getElementById('question-container');
        if (questionContainer) {
            questionContainer.style.display = 'none';
        }
        
        console.log('已隐藏所有页面元素');
    }

    /**
     * 验证已存在的会话
     * @param {Object} student - 学生信息对象，包含会话ID
     * @returns {Promise<boolean>} 验证结果
     */
    async function validateExistingSession(student) {
        // 如果没有会话ID，则会话无效
        if (!student || !student.sessionId) {
            console.log('会话无效: 没有会话ID');
            return false;
        }
        
        try {
            // 这里可以实现更复杂的会话验证逻辑，如检查会话是否过期等
            console.log('会话有效: 使用已存在的会话 ID:', student.sessionId);
            return true;
        } catch (error) {
            console.error('验证会话错误:', error);
            return false;
        }
    }

    /**
     * 解析错误消息，提供更具体的错误提示
     * @param {string} error - 原始错误消息
     * @param {Object} [data] - 完整的错误响应数据
     * @returns {string} 格式化后的错误消息
     */
    function parseErrorMessage(error, data) {
        // 默认错误消息
        let message = '验证学生失败，请检查您的输入信息';
        
        // 输出完整的错误对象结构，便于调试
        console.log('原始错误消息:', error);
        console.log('完整错误数据结构:', JSON.stringify(data, null, 2));
        
        try {
            // 直接从错误消息中提取不匹配字段（最高优先级）
            if (error && typeof error === 'string') {
                const regex = /学生信息不匹配：(.*?)与系统记录不符/;
                const match = error.match(regex);
                
                if (match && match[1]) {
                    const extractedFields = match[1]; // 如"年级、班级"
                    message = `您输入的${extractedFields}与系统记录不符，请检查后重试`;
                    console.log('1. 直接从错误文本提取成功:', extractedFields);
                    return message;
                }
            }
            
            // 尝试从data对象中获取mismatchFields数组
            if (data && typeof data === 'object') {
                if (data.mismatchFields && Array.isArray(data.mismatchFields) && data.mismatchFields.length > 0) {
                    message = `您输入的${data.mismatchFields.join('、')}与系统记录不符，请检查后重试`;
                    console.log('2. 从data.mismatchFields中提取成功:', data.mismatchFields);
                    return message;
                }
            }
            
            // 从错误文本中分析关键词
            if (error && typeof error === 'string') {
                if (error.includes('学生不存在')) {
                    message = '该学生信息不存在，请检查年级、班级和姓名是否正确';
                    console.log('3. 学生不存在错误');
                } else if (error.includes('不匹配')) {
                    // 分析错误文本中的关键词提取可能不匹配的字段
                    const issues = [];
                    
                    if (error.includes('年级')) {
                        issues.push('年级');
                    }
                    
                    if (error.includes('班级')) {
                        issues.push('班级');
                    }
                    
                    if (error.includes('姓名')) {
                        issues.push('姓名');
                    }
                    
                    if (issues.length > 0) {
                        message = `您输入的${issues.join('、')}与系统记录不符，请检查后重试`;
                        console.log('4. 从错误关键词分析提取字段:', issues);
                    } else {
                        message = '学生信息不匹配，请检查您的输入是否正确';
                        console.log('5. 使用一般不匹配消息');
                    }
                } else if (error.includes('学生标识符不匹配')) {
                    message = '学生识别信息不匹配，请确保您输入的年级、班级和姓名完全正确';
                    console.log('6. 学生标识符不匹配错误');
                } else if (error.includes('严格验证模式')) {
                    message = '信息验证失败，请确保填写了完整的年级、班级和姓名';
                    console.log('7. 严格验证模式错误');
                } else if (error.includes('学生已有未结束的会话')) {
                    message = '检测到您有一个未结束的会话，系统将自动处理';
                    console.log('8. 有未结束会话错误');
                }
            }
        } catch (parseError) {
            console.error('解析错误消息时出错:', parseError);
            // 如果解析过程中出错，确保返回一个有意义的错误消息
            message = '验证学生失败，请检查您的输入信息是否正确';
        }
        
        // 确保返回的是字符串
        if (typeof message !== 'string') {
            message = '验证学生失败，请检查您的输入信息';
        }
        
        console.log('最终错误消息:', message);
        return message;
    }

    /**
     * 处理在当前行为空时按下backspace键的逻辑
     * @param {number} currentLineIndex - 当前行索引
     */
    function handleBackspaceToPreviousLine(currentLineIndex) {
        if (currentLineIndex <= 0) return; // 如果是第一行则不处理
        
        const prevLineIndex = currentLineIndex - 1;
        const prevInputEl = document.getElementById(`typing-input-${prevLineIndex}`);
        const prevTextEl = document.getElementById(`typing-text-${prevLineIndex}`);
        const currentInputEl = document.getElementById(`typing-input-${currentLineIndex}`);
        const currentTextEl = document.getElementById(`typing-text-${currentLineIndex}`);
        
        if (!prevInputEl || !prevTextEl || !currentInputEl || !currentTextEl) return;
        
        console.log(`在行 ${currentLineIndex} 按下backspace，返回到行 ${prevLineIndex}`);
        
        // 重新启用上一行输入框
        prevInputEl.disabled = false;
        
        // 更新激活行索引
        activeLineIndex = prevLineIndex;
        
        // 更新行的样式
        prevTextEl.classList.add('current-line');
        prevTextEl.classList.remove('line-completed');
        currentTextEl.classList.remove('current-line');
        
        // 禁用当前行输入框
        currentInputEl.disabled = true;
        
        // 聚焦到上一行的输入框末尾
        prevInputEl.focus();
        if (prevInputEl.value.length > 0) {
            // 将光标移动到文本末尾
            prevInputEl.selectionStart = prevInputEl.value.length;
            prevInputEl.selectionEnd = prevInputEl.value.length;
        }
        
        // 更新当前行的标记
        const prevLineChars = prevTextEl.querySelectorAll('span');
        if (prevLineChars.length > 0) {
            // 重置所有字符的样式
            prevLineChars.forEach(span => span.className = '');
            
            // 标记已输入字符
            for (let i = 0; i < prevInputEl.value.length; i++) {
                const char = prevInputEl.value[i];
                const targetChar = prevLineChars[i].textContent;
                
                if (char === targetChar) {
                    prevLineChars[i].className = 'correct';
                } else {
                    prevLineChars[i].className = 'incorrect';
                }
            }
            
            // 标记下一个要输入的字符
            if (prevInputEl.value.length < prevLineChars.length) {
                prevLineChars[prevInputEl.value.length].className = 'current';
            }
        }
        
        // 滚动到上一行
        prevTextEl.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // 播放反向换行音效
        playBackspaceSound();
    }

    /**
     * 播放反向换行音效（更低的音调）
     */
    function playBackspaceSound() {
        try {
            const AudioContext = window.AudioContext || window.webkitAudioContext;
            if (!AudioContext) return;
            
            const context = new AudioContext();
            const oscillator = context.createOscillator();
            const gain = context.createGain();
            
            oscillator.connect(gain);
            gain.connect(context.destination);
            
            oscillator.type = 'sine';
            oscillator.frequency.value = 440; // A4音符，比换行音效低一个八度
            gain.gain.value = 0.1; // 音量控制
            
            oscillator.start(0);
            gain.gain.exponentialRampToValueAtTime(0.001, context.currentTime + 0.3);
            oscillator.stop(context.currentTime + 0.3);
        } catch (e) {
            console.error('无法播放音效', e);
        }
    }

    /**
     * 保存打字记录
     * @param {number} speed - 打字速度
     * @param {number} accuracy - 准确度
     * @param {number|string} grade - 年级（可能是数字或字符串）
     */
    async function saveTypingRecord(speed, accuracy, grade) {
        try {
            // 确保grade是数字格式
            let gradeNumber = grade;
            if (isNaN(parseInt(gradeNumber)) && typeof convertGradeToNumber === 'function') {
                gradeNumber = convertGradeToNumber(gradeNumber);
                console.log('在saveTypingRecord中转换年级:', grade, '→', gradeNumber);
            }
            
            // 调试并获取学生标识符
            console.log('=== saveTypingRecord currentStudent ===');
            console.log('currentStudent:', currentStudent);
            console.log('currentStudent 属性:', Object.keys(currentStudent || {}));

            const studentId = currentStudent?.student_identifier ||
                             currentStudent?.id ||
                             currentStudent?.studentId ||
                             currentStudent?.student_id ||
                             `${currentStudent?.grade}_${currentStudent?.class}_${currentStudent?.name}`;

            console.log('保存打字记录:', {
                student_identifier: studentId,
                speed: speed,
                accuracy: accuracy,
                currentStudent: currentStudent
            });
            
            // 直接调用API保存记录
            const response = await fetch('/api/typing-records', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    student_identifier: studentId,
                    speed: speed,
                    accuracy: accuracy
                })
            });

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            
            console.log('打字记录已成功保存');
            return true;
        } catch (error) {
            console.error('保存打字记录失败:', error);
            showMessage('保存打字记录失败: ' + error.message, 'error');
            return false;
        }
    }

    /**
     * 重新开始练习 - 重新加载文章但保留计时和统计
     */
    function restartPractice() {
        // 检查是否有选择的文章
        if (!selectedArticle) {
            showMessage('请先选择一篇文章再重新开始', 'warning');
            return;
        }
        
        // 清空打字区域
        typingLinesContainer.innerHTML = '';
        
        // 重新生成文本并显示
        generateTypingText();
        displayText(textLines);
        
        // 重置所有输入框但保持统计
        inputElements.forEach(inputEl => {
            if (!inputEl) return;
            inputEl.value = '';
            // 不重置输入框的正确/错误字符计数
        });
        
        // 如果当前不在练习中，就启动练习
        if (!isTypingActive) {
            // 启动界面状态和打字记录等
            isTypingActive = true;
            
            // 设置界面状态为练习中
            // 隐藏开始按钮，显示结束按钮
            if (startPracticeBtn) {
                startPracticeBtn.disabled = true;
                startPracticeBtn.style.backgroundColor = '#ccc';
                startPracticeBtn.style.cursor = 'not-allowed';
                startPracticeBtn.style.display = 'none';
            }
            
            if (finishPracticeBtn) {
                finishPracticeBtn.style.display = 'block';
            }
            
            // 禁用文章选择和语言按钮
            const articleSelect = document.getElementById('article-select');
            if (articleSelect) {
                articleSelect.disabled = true;
            }
            if (langZhBtn) langZhBtn.disabled = true;
            if (langEnBtn) langEnBtn.disabled = true;
            
            // 禁用文章选择区域的交互
            const selectGroup = document.querySelector('.select-group');
            if (selectGroup) {
                selectGroup.style.opacity = '0.6';
                selectGroup.style.pointerEvents = 'none';
            }
            
            // 隐藏额外按钮
            const extraButtons = document.querySelector('.extra-buttons');
            if (extraButtons) {
                extraButtons.style.display = 'none';
            }
            
            // 设置时间（5分钟 = 300秒）
            let totalSeconds = 5 * 60;
            
            // 确保先清除已有的计时器，避免多个计时器同时运行
            if (practiceTimer) {
                clearInterval(practiceTimer);
                practiceTimer = null;
            }
            
            if (typingInterval) {
                clearInterval(typingInterval);
                typingInterval = null;
            }
            
            // 启动计时器
            practiceTimer = setInterval(function() {
                totalSeconds--;
                
                // 更新计时器显示
                updateTimer(totalSeconds);
                
                // 时间到，自动结束练习
                if (totalSeconds <= 0) {
                    clearInterval(practiceTimer);
                    finishPractice();
                }
            }, 1000);
            
            // 启动打字速度计算定时器
            typingInterval = setInterval(updateOverallStats, 2000);
            
            // 记录开始时间
            typingStartTime = new Date();
            practiceStartTime = new Date();
            
            // 添加键盘快捷键防护
            document.addEventListener('keydown', preventCopyPasteShortcuts);
        }
        // 如果已在练习中，不要重启计时器，使用现有的剩余时间
        else {
            // 已经在练习中，但重新开始了练习，需要重置计时器
            // 确保先清除已有的计时器，避免多个计时器同时运行
            if (practiceTimer) {
                clearInterval(practiceTimer);
                practiceTimer = null;
            }
            
            if (typingInterval) {
                clearInterval(typingInterval);
                typingInterval = null;
            }
            
            // 重置时间（5分钟 = 300秒）
            let totalSeconds = 5 * 60;
            
            // 立即更新一次计时器显示
            updateTimer(totalSeconds);
            
            // 重新启动计时器
            practiceTimer = setInterval(function() {
                totalSeconds--;
                
                // 更新计时器显示
                updateTimer(totalSeconds);
                
                // 时间到，自动结束练习
                if (totalSeconds <= 0) {
                    clearInterval(practiceTimer);
                    finishPractice();
                }
            }, 1000);
            
            // 重新启动打字速度计算定时器
            typingInterval = setInterval(updateOverallStats, 2000);
            
            // 更新开始时间
            typingStartTime = new Date();
            practiceStartTime = new Date();
        }
        
        // 激活第一个输入框
        const firstInput = document.getElementById('typing-input-0');
        if (firstInput) {
            firstInput.disabled = false;
            firstInput.focus();
        }
        
        // 提示用户
        showMessage(`已重新加载文章"${selectedArticle.title}"，继续练习`, 'info');
    }
}); 