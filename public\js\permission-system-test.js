/**
 * 权限系统测试文件
 * 用于验证教师权限管理功能的正确性
 */

// 测试数据
const testData = {
    adminUser: {
        username: 'admin',
        password: 'password',
        role: 'admin'
    },
    teacherUser: {
        username: 'teacher',
        password: 'password', 
        role: 'teacher'
    },
    schools: [
        { name: '测试小学1' },
        { name: '测试小学2' },
        { name: '测试中学1' }
    ],
    students: [
        { name: '张三', grade: 3, class: 1, school_index: 0 },
        { name: '李四', grade: 3, class: 2, school_index: 0 },
        { name: '王五', grade: 4, class: 1, school_index: 1 },
        { name: '赵六', grade: 5, class: 1, school_index: 2 }
    ]
};

class PermissionSystemTester {
    constructor() {
        this.baseUrl = window.location.origin;
        this.adminToken = null;
        this.teacherToken = null;
        this.createdSchools = [];
        this.createdStudents = [];
        this.testResults = [];
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('=== 开始权限系统测试 ===');
        this.testResults = [];

        try {
            // 1. 管理员登录测试
            await this.testAdminLogin();
            
            // 2. 创建测试学校
            await this.testCreateSchools();
            
            // 3. 配置学校年级班级
            await this.testConfigureSchoolGrades();
            
            // 4. 创建测试教师并分配权限
            await this.testCreateTeacherWithPermissions();
            
            // 5. 教师登录测试
            await this.testTeacherLogin();
            
            // 6. 测试教师权限限制
            await this.testTeacherPermissions();
            
            // 7. 创建测试学生
            await this.testCreateStudents();
            
            // 8. 测试教师只能看到权限内的学生
            await this.testTeacherStudentAccess();
            
            // 输出测试结果
            this.displayTestResults();
            
        } catch (error) {
            console.error('测试过程中发生错误:', error);
            this.addTestResult('整体测试', false, error.message);
        }
        
        console.log('=== 权限系统测试完成 ===');
    }

    /**
     * 测试管理员登录
     */
    async testAdminLogin() {
        try {
            const response = await fetch(`${this.baseUrl}/api/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: testData.adminUser.username,
                    password: testData.adminUser.password
                })
            });

            const result = await response.json();
            
            if (response.ok && result.token) {
                this.adminToken = result.token;
                this.addTestResult('管理员登录', true, '登录成功');
            } else {
                this.addTestResult('管理员登录', false, result.message || '登录失败');
            }
        } catch (error) {
            this.addTestResult('管理员登录', false, error.message);
        }
    }

    /**
     * 测试创建学校
     */
    async testCreateSchools() {
        for (const school of testData.schools) {
            try {
                const response = await fetch(`${this.baseUrl}/api/admin/schools`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.adminToken}`
                    },
                    body: JSON.stringify(school)
                });

                const result = await response.json();
                
                if (response.ok && result.data) {
                    this.createdSchools.push(result.data);
                    this.addTestResult(`创建学校 ${school.name}`, true, '创建成功');
                } else {
                    this.addTestResult(`创建学校 ${school.name}`, false, result.message || '创建失败');
                }
            } catch (error) {
                this.addTestResult(`创建学校 ${school.name}`, false, error.message);
            }
        }
    }

    /**
     * 测试配置学校年级班级
     */
    async testConfigureSchoolGrades() {
        for (const school of this.createdSchools) {
            try {
                const grades = [
                    { grade: 1, class_count: 2 },
                    { grade: 2, class_count: 2 },
                    { grade: 3, class_count: 3 },
                    { grade: 4, class_count: 3 },
                    { grade: 5, class_count: 2 }
                ];

                const response = await fetch(`${this.baseUrl}/api/admin/schools/${school.id}/config`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.adminToken}`
                    },
                    body: JSON.stringify({ grades })
                });

                if (response.ok) {
                    this.addTestResult(`配置学校年级 ${school.name}`, true, '配置成功');
                } else {
                    const result = await response.json();
                    this.addTestResult(`配置学校年级 ${school.name}`, false, result.message || '配置失败');
                }
            } catch (error) {
                this.addTestResult(`配置学校年级 ${school.name}`, false, error.message);
            }
        }
    }

    /**
     * 测试创建教师并分配权限
     */
    async testCreateTeacherWithPermissions() {
        try {
            const schools = this.createdSchools.slice(0, 2).map(s => s.id); // 只分配前两个学校
            const classes = [
                { school_id: this.createdSchools[0].id, grade: 3, class: 1 },
                { school_id: this.createdSchools[0].id, grade: 3, class: 2 },
                { school_id: this.createdSchools[1].id, grade: 4, class: 1 }
            ];

            const response = await fetch(`${this.baseUrl}/api/admin/teachers`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.adminToken}`
                },
                body: JSON.stringify({
                    username: 'test_teacher',
                    password: 'test123',
                    display_name: '测试教师',
                    schools,
                    classes
                })
            });

            const result = await response.json();
            
            if (response.ok) {
                this.addTestResult('创建教师并分配权限', true, '创建成功');
            } else {
                this.addTestResult('创建教师并分配权限', false, result.message || '创建失败');
            }
        } catch (error) {
            this.addTestResult('创建教师并分配权限', false, error.message);
        }
    }

    /**
     * 测试教师登录
     */
    async testTeacherLogin() {
        try {
            const response = await fetch(`${this.baseUrl}/api/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: 'test_teacher',
                    password: 'test123'
                })
            });

            const result = await response.json();
            
            if (response.ok && result.token) {
                this.teacherToken = result.token;
                this.addTestResult('教师登录', true, '登录成功');
            } else {
                this.addTestResult('教师登录', false, result.message || '登录失败');
            }
        } catch (error) {
            this.addTestResult('教师登录', false, error.message);
        }
    }

    /**
     * 测试教师权限限制
     */
    async testTeacherPermissions() {
        // 测试教师获取学校列表
        try {
            const response = await fetch(`${this.baseUrl}/api/teacher/schools`, {
                headers: {
                    'Authorization': `Bearer ${this.teacherToken}`
                }
            });

            const schools = await response.json();
            
            if (response.ok) {
                const hasOnlyPermittedSchools = schools.length === 2; // 教师应该只能看到分配的2个学校
                this.addTestResult('教师学校权限限制', hasOnlyPermittedSchools, 
                    `教师看到${schools.length}个学校${hasOnlyPermittedSchools ? '（正确）' : '（错误）'}`);
            } else {
                this.addTestResult('教师学校权限限制', false, '获取学校列表失败');
            }
        } catch (error) {
            this.addTestResult('教师学校权限限制', false, error.message);
        }

        // 测试教师获取班级列表
        try {
            const response = await fetch(`${this.baseUrl}/api/teacher/classes`, {
                headers: {
                    'Authorization': `Bearer ${this.teacherToken}`
                }
            });

            const classes = await response.json();
            
            if (response.ok) {
                const hasOnlyPermittedClasses = classes.length === 3; // 教师应该只能看到分配的3个班级
                this.addTestResult('教师班级权限限制', hasOnlyPermittedClasses,
                    `教师看到${classes.length}个班级${hasOnlyPermittedClasses ? '（正确）' : '（错误）'}`);
            } else {
                this.addTestResult('教师班级权限限制', false, '获取班级列表失败');
            }
        } catch (error) {
            this.addTestResult('教师班级权限限制', false, error.message);
        }
    }

    /**
     * 测试创建学生
     */
    async testCreateStudents() {
        for (const student of testData.students) {
            try {
                const school = this.createdSchools[student.school_index];
                if (!school) continue;

                const response = await fetch(`${this.baseUrl}/api/admin/students`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.adminToken}`
                    },
                    body: JSON.stringify({
                        student_identifier: `test_${Date.now()}_${Math.random()}`,
                        name: student.name,
                        grade: student.grade,
                        class: student.class,
                        school_id: school.id
                    })
                });

                const result = await response.json();
                
                if (response.ok && result.data) {
                    this.createdStudents.push(result.data);
                    this.addTestResult(`创建学生 ${student.name}`, true, '创建成功');
                } else {
                    this.addTestResult(`创建学生 ${student.name}`, false, result.message || '创建失败');
                }
            } catch (error) {
                this.addTestResult(`创建学生 ${student.name}`, false, error.message);
            }
        }
    }

    /**
     * 测试教师学生访问权限
     */
    async testTeacherStudentAccess() {
        try {
            const response = await fetch(`${this.baseUrl}/api/teacher/students`, {
                headers: {
                    'Authorization': `Bearer ${this.teacherToken}`
                }
            });

            const students = await response.json();
            
            if (response.ok) {
                // 教师应该只能看到分配班级的学生（张三、李四、王五）
                const expectedStudentCount = 3;
                const hasCorrectStudents = students.length === expectedStudentCount;
                
                this.addTestResult('教师学生访问权限', hasCorrectStudents,
                    `教师看到${students.length}个学生${hasCorrectStudents ? '（正确）' : '（错误）'}`);
            } else {
                this.addTestResult('教师学生访问权限', false, '获取学生列表失败');
            }
        } catch (error) {
            this.addTestResult('教师学生访问权限', false, error.message);
        }
    }

    /**
     * 添加测试结果
     */
    addTestResult(testName, success, message) {
        this.testResults.push({
            name: testName,
            success,
            message,
            timestamp: new Date().toLocaleString()
        });
        
        console.log(`${success ? '✅' : '❌'} ${testName}: ${message}`);
    }

    /**
     * 显示测试结果
     */
    displayTestResults() {
        const successCount = this.testResults.filter(r => r.success).length;
        const totalCount = this.testResults.length;
        
        console.log('\n=== 测试结果汇总 ===');
        console.log(`总测试数: ${totalCount}`);
        console.log(`成功: ${successCount}`);
        console.log(`失败: ${totalCount - successCount}`);
        console.log(`成功率: ${((successCount / totalCount) * 100).toFixed(1)}%`);
        
        if (window.Utils) {
            window.Utils.showMessage(
                `权限系统测试完成：${successCount}/${totalCount} 个测试通过`,
                successCount === totalCount ? 'success' : 'warning'
            );
        }
    }

    /**
     * 清理测试数据
     */
    async cleanup() {
        console.log('开始清理测试数据...');
        
        // 删除创建的学生
        for (const student of this.createdStudents) {
            try {
                await fetch(`${this.baseUrl}/api/admin/students/${student.id}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${this.adminToken}`
                    }
                });
            } catch (error) {
                console.error('删除学生失败:', error);
            }
        }
        
        // 删除测试教师
        try {
            const teachersResponse = await fetch(`${this.baseUrl}/api/admin/teachers`, {
                headers: {
                    'Authorization': `Bearer ${this.adminToken}`
                }
            });
            
            if (teachersResponse.ok) {
                const result = await teachersResponse.json();
                const testTeacher = result.data.find(t => t.username === 'test_teacher');
                
                if (testTeacher) {
                    await fetch(`${this.baseUrl}/api/admin/teachers/${testTeacher.id}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${this.adminToken}`
                        }
                    });
                }
            }
        } catch (error) {
            console.error('删除测试教师失败:', error);
        }
        
        // 删除创建的学校
        for (const school of this.createdSchools) {
            try {
                await fetch(`${this.baseUrl}/api/admin/schools/${school.id}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${this.adminToken}`
                    }
                });
            } catch (error) {
                console.error('删除学校失败:', error);
            }
        }
        
        console.log('测试数据清理完成');
    }
}

// 全局测试器实例
window.permissionTester = new PermissionSystemTester();

// 提供便捷的测试函数
window.runPermissionTests = () => window.permissionTester.runAllTests();
window.cleanupTestData = () => window.permissionTester.cleanup();