/**
 * 测试认证功能
 */

async function testAuth() {
    const BASE_URL = 'http://localhost:3005';
    
    console.log('=== 认证功能测试开始 ===\n');

    try {
        // 1. 测试登录
        console.log('1. 测试登录...');
        const loginResponse = await fetch(`${BASE_URL}/api/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: 'teacher',
                password: 'password'
            })
        });

        console.log(`   登录响应状态: ${loginResponse.status} ${loginResponse.statusText}`);
        
        const loginResult = await loginResponse.json();
        console.log('   登录响应内容:', JSON.stringify(loginResult, null, 2));

        if (!loginResponse.ok) {
            throw new Error(`登录失败: ${loginResult.message || loginResult.error}`);
        }

        const token = loginResult.data?.token || loginResult.token;
        const user = loginResult.data?.user || loginResult.user;

        if (!token) {
            throw new Error('登录成功但未获取到token');
        }

        console.log('✅ 登录成功');
        console.log(`   Token前20位: ${token.substring(0, 20)}...`);
        console.log(`   用户信息: ${JSON.stringify(user)}`);

        // 2. 测试token验证
        console.log('\n2. 测试token验证...');
        const validateResponse = await fetch(`${BASE_URL}/api/auth/validate`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        console.log(`   验证响应状态: ${validateResponse.status} ${validateResponse.statusText}`);
        
        const validateResult = await validateResponse.json();
        console.log('   验证响应内容:', JSON.stringify(validateResult, null, 2));

        if (!validateResponse.ok) {
            throw new Error(`Token验证失败: ${validateResult.message || validateResult.error}`);
        }

        console.log('✅ Token验证成功');

        // 3. 测试教师API访问
        console.log('\n3. 测试教师API访问...');
        const schoolsResponse = await fetch(`${BASE_URL}/api/teacher/schools`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        console.log(`   学校API响应状态: ${schoolsResponse.status} ${schoolsResponse.statusText}`);
        
        const schoolsResult = await schoolsResponse.json();
        console.log('   学校API响应内容:', JSON.stringify(schoolsResult, null, 2));

        if (!schoolsResponse.ok) {
            throw new Error(`学校API访问失败: ${schoolsResult.message || schoolsResult.error}`);
        }

        console.log('✅ 教师API访问成功');

        console.log('\n=== 测试完成 ===');
        console.log('🎉 认证功能测试通过！');

        return {
            success: true,
            token: token,
            user: user
        };

    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        
        return {
            success: false,
            error: error.message
        };
    }
}

// 如果在Node.js环境中运行
if (typeof window === 'undefined') {
    // 使用内置的fetch (Node.js 18+)
    testAuth().then(result => {
        if (result.success) {
            console.log('\n✅ 所有测试通过！');
            process.exit(0);
        } else {
            console.log('\n❌ 测试失败！');
            process.exit(1);
        }
    }).catch(error => {
        console.error('测试脚本执行失败:', error);
        process.exit(1);
    });
} else {
    // 浏览器环境
    window.testAuth = testAuth;
}
