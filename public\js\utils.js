/**
 * 通用工具函数
 * 提供数据处理和界面交互的通用方法
 */
const Utils = {
    /**
     * 显示加载状态
     * @param {boolean} show - 是否显示加载状态
     */
    showLoading: function(show = true) {
        document.getElementById('loading').style.display = show ? 'flex' : 'none';
    },
    
    /**
     * 显示弹窗提示
     * @param {string} message - 提示信息
     */
    showMessage: function(message) {
        // 创建自定义提示框
        const messageBox = document.createElement('div');
        messageBox.className = 'custom-message';
        messageBox.innerHTML = `
            <div class="message-content">
                <div class="message-text">${message}</div>
                <button class="message-btn">确定</button>
            </div>
        `;
        
        // 添加样式
        const style = document.createElement('style');
        if (!document.querySelector('style#message-style')) {
            style.id = 'message-style';
            style.textContent = `
                .custom-message {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                }
                .message-content {
                    background-color: white;
                    padding: 20px;
                    border-radius: 5px;
                    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
                    max-width: 80%;
                    text-align: center;
                }
                .message-text {
                    margin-bottom: 15px;
                    font-size: 16px;
                }
                .message-btn {
                    padding: 8px 20px;
                    background-color: #4a90e2;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                }
                .message-btn:hover {
                    background-color: #3a80d2;
                }
            `;
            document.head.appendChild(style);
        }
        
        // 添加到文档
        document.body.appendChild(messageBox);
        
        // 点击确定按钮关闭
        messageBox.querySelector('.message-btn').addEventListener('click', function() {
            document.body.removeChild(messageBox);
        });
        
        // 点击背景关闭
        messageBox.addEventListener('click', function(e) {
            if (e.target === messageBox) {
                document.body.removeChild(messageBox);
            }
        });
    },
    
    /**
     * 确认对话框
     * @param {string} message - 确认信息
     * @returns {boolean} - 用户确认结果
     */
    confirm: function(message) {
        return new Promise((resolve) => {
            // 创建自定义确认框
            const confirmBox = document.createElement('div');
            confirmBox.className = 'custom-confirm';
            confirmBox.innerHTML = `
                <div class="confirm-content">
                    <div class="confirm-text">${message}</div>
                    <div class="confirm-buttons">
                        <button class="confirm-btn confirm-yes">确定</button>
                        <button class="confirm-btn confirm-no">取消</button>
                    </div>
                </div>
            `;
            
            // 添加样式
            const style = document.createElement('style');
            if (!document.querySelector('style#confirm-style')) {
                style.id = 'confirm-style';
                style.textContent = `
                    .custom-confirm {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background-color: rgba(0, 0, 0, 0.5);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        z-index: 10000;
                    }
                    .confirm-content {
                        background-color: white;
                        padding: 20px;
                        border-radius: 5px;
                        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
                        max-width: 80%;
                        text-align: center;
                    }
                    .confirm-text {
                        margin-bottom: 20px;
                        font-size: 16px;
                    }
                    .confirm-buttons {
                        display: flex;
                        justify-content: center;
                        gap: 15px;
                    }
                    .confirm-btn {
                        padding: 8px 20px;
                        border: none;
                        border-radius: 3px;
                        cursor: pointer;
                    }
                    .confirm-yes {
                        background-color: #4a90e2;
                        color: white;
                    }
                    .confirm-yes:hover {
                        background-color: #3a80d2;
                    }
                    .confirm-no {
                        background-color: #f2f2f2;
                        color: #333;
                    }
                    .confirm-no:hover {
                        background-color: #e6e6e6;
                    }
                `;
                document.head.appendChild(style);
            }
            
            // 添加到文档
            document.body.appendChild(confirmBox);
            
            // 确定按钮事件
            confirmBox.querySelector('.confirm-yes').addEventListener('click', function() {
                document.body.removeChild(confirmBox);
                resolve(true);
            });
            
            // 取消按钮事件
            confirmBox.querySelector('.confirm-no').addEventListener('click', function() {
                document.body.removeChild(confirmBox);
                resolve(false);
            });
            
            // 点击背景也取消
            confirmBox.addEventListener('click', function(e) {
                if (e.target === confirmBox) {
                    document.body.removeChild(confirmBox);
                    resolve(false);
                }
            });
        });
    },
    
    /**
     * 生成随机ID
     * @returns {string} - 随机ID字符串
     */
    generateId: function() {
        return Math.random().toString(36).substring(2, 15) + 
               Math.random().toString(36).substring(2, 15);
    },
    
    /**
     * 获取当前日期时间字符串
     * @returns {string} - 当前日期时间字符串
     */
    getCurrentDateTime: function() {
        const now = new Date();
        return now.toLocaleString('zh-CN');
    },
    
    /**
     * 从localStorage获取数据
     * @param {string} key - 存储键名
     * @param {any} defaultValue - 默认值
     * @returns {any} - 获取的数据
     */
    getData: function(key, defaultValue = null) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : defaultValue;
        } catch (error) {
            console.error('获取数据失败:', error);
            return defaultValue;
        }
    },
    
    /**
     * 保存数据到localStorage
     * @param {string} key - 存储键名
     * @param {any} data - 要保存的数据
     * @returns {boolean} - 保存是否成功
     */
    saveData: function(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('保存数据失败:', error);
            return false;
        }
    },
    
    /**
     * 删除localStorage中的数据
     * @param {string} key - 存储键名
     */
    removeData: function(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('删除数据失败:', error);
        }
    },
    
    /**
     * 重置所有系统数据
     */
    resetAllData: function() {
        if (this.confirm('确定要重置所有数据吗？此操作不可恢复！')) {
            // 清除所有相关的localStorage数据
            this.removeData(CONFIG.STORAGE.STUDENTS);
            this.removeData(CONFIG.STORAGE.MEDAL);
            this.removeData(CONFIG.STORAGE.TYPING);
            this.removeData(CONFIG.STORAGE.AUTH);
            this.removeData(CONFIG.STORAGE.LOGS);
            
            // 初始化示例数据
            this.initSampleData();
            
            this.showMessage('系统数据已重置，将刷新页面');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
    },
    
    /**
     * 初始化示例数据
     */
    initSampleData: function() {
        // 初始化学生数据
        if (!this.getData(CONFIG.STORAGE.STUDENTS)) {
            const students = [
                { id: this.generateId(), grade: '一年级', class: '1班', name: '张三', gender: '男' },
                { id: this.generateId(), grade: '一年级', class: '1班', name: '李四', gender: '女' },
                { id: this.generateId(), grade: '一年级', class: '2班', name: '王五', gender: '男' },
                { id: this.generateId(), grade: '一年级', class: '2班', name: '赵六', gender: '女' },
                { id: this.generateId(), grade: '二年级', class: '1班', name: '陈七', gender: '男' },
                { id: this.generateId(), grade: '二年级', class: '1班', name: '刘八', gender: '女' },
                { id: this.generateId(), grade: '二年级', class: '2班', name: '杨九', gender: '男' },
                { id: this.generateId(), grade: '二年级', class: '2班', name: '黄十', gender: '女' }
            ];
            this.saveData(CONFIG.STORAGE.STUDENTS, students);
        }
        
        // 初始化奖章数据
        if (!this.getData(CONFIG.STORAGE.MEDAL)) {
            const medalData = [];
            const students = this.getData(CONFIG.STORAGE.STUDENTS, []);
            
            students.forEach(student => {
                medalData.push({
                    id: student.id,
                    grade: student.grade,
                    class: student.class,
                    name: student.name,
                    medalCount: Math.floor(Math.random() * 20) // 随机奖章数1-20
                });
            });
            
            this.saveData(CONFIG.STORAGE.MEDAL, medalData);
        }
        
        // 初始化打字数据
        if (!this.getData(CONFIG.STORAGE.TYPING)) {
            const typingData = [];
            const students = this.getData(CONFIG.STORAGE.STUDENTS, []);
            
            students.forEach(student => {
                typingData.push({
                    id: student.id,
                    grade: student.grade,
                    class: student.class,
                    name: student.name,
                    speed: Math.floor(Math.random() * 60) + 20 // 随机打字速度20-80
                });
            });
            
            this.saveData(CONFIG.STORAGE.TYPING, typingData);
        }
    },
    
    /**
     * 导出数据为Excel文件
     * @param {Array} data - 要导出的数据数组
     * @param {string} filename - 导出的文件名
     */
    exportToExcel: function(data, filename) {
        try {
            const worksheet = XLSX.utils.json_to_sheet(data);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
            
            // 导出为Excel文件
            XLSX.writeFile(workbook, `${filename}.xlsx`);
            this.showMessage('数据导出成功');
        } catch (error) {
            console.error('导出Excel失败:', error);
            this.showMessage('导出失败: ' + error.message);
        }
    },
    
    /**
     * 从Excel文件导入数据
     * @param {File} file - 上传的Excel文件
     * @returns {Promise<Array>} - 解析后的数据
     */
    importFromExcel: function(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet);
                    resolve(jsonData);
                } catch (error) {
                    reject(error);
                }
            };
            
            reader.onerror = (error) => {
                reject(error);
            };
            
            reader.readAsArrayBuffer(file);
        });
    },
    
    /**
     * 获取年级和班级列表
     * @param {Array} data - 学生数据
     * @returns {Object} - 包含年级和班级列表的对象
     */
    getGradesAndClasses: function(data) {
        // 使用grade_display优先，如果没有则转换数字年级为中文
        const grades = [...new Set(data.map(item => item.grade_display || this.numberToGrade(item.grade)))];
        
        const classesByGrade = {};
        grades.forEach(gradeDisplay => {
            const classesInGrade = [...new Set(
                data.filter(item => {
                    const itemGradeDisplay = item.grade_display || this.numberToGrade(item.grade);
                    return itemGradeDisplay === gradeDisplay;
                })
                .map(item => item.class)
            )];
            classesByGrade[gradeDisplay] = classesInGrade;
        });
        
        return {
            grades,
            classesByGrade
        };
    },
    
    /**
     * 更新选择器选项
     * @param {HTMLSelectElement} selectElement - 选择器元素
     * @param {Array} options - 选项数组
     * @param {string} defaultText - 默认选项文本
     */
    updateSelectOptions: function(selectElement, options, defaultText = '') {
        // 清除现有选项
        selectElement.innerHTML = '';
        
        // 添加默认选项
        if (defaultText) {
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = defaultText;
            selectElement.appendChild(defaultOption);
        }
        
        // 添加新选项
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option;
            optionElement.textContent = option;
            selectElement.appendChild(optionElement);
        });
    },
    
    /**
     * 导出数据模板
     * @param {string} type - 模板类型：'medal'或'typing'
     */
    exportTemplate: function(type) {
        try {
            let templateData = [];
            let filename = '';
            
            if (type === 'medal') {
                filename = '奖章数据模板';
                templateData = [
                    { grade: '一', class: '1', name: '学生姓名', medals: 5 },
                    { grade: '一', class: '2', name: '学生姓名', medals: 3 },
                    { grade: '二', class: '1', name: '学生姓名', medals: 7 }
                ];
            } else if (type === 'typing') {
                filename = '打字数据模板';
                templateData = [
                    { grade: '一', class: '1', name: '学生姓名', speed: 45 },
                    { grade: '一', class: '2', name: '学生姓名', speed: 58 },
                    { grade: '二', class: '1', name: '学生姓名', speed: 75 }
                ];
            }
            
            // 添加模板说明信息
            this.showMessage(`正在导出${filename}，请按照模板格式填写数据后导入`);
            
            // 导出模板
            this.exportToExcel(templateData, filename);
        } catch (error) {
            console.error('导出模板失败:', error);
            this.showMessage('导出模板失败: ' + error.message);
        }
    },
    
    /**
     * 生成并下载Excel数据模板
     * @param {string} type - 模板类型，'medal'或'typing'
     */
    generateTemplate: function(type) {
        // 创建工作簿
        const wb = XLSX.utils.book_new();
        
        // 定义表头和示例数据
        let headers, data;
        
        if (type === 'medal') {
            headers = ['年级', '班级', '姓名', '奖章数量'];
            data = [
                ['一年级', '1班', '张三', 5],
                ['一年级', '1班', '李四', 3],
                ['二年级', '2班', '王五', 8],
                ['', '', '', '']
            ];
        } else if (type === 'typing') {
            headers = ['年级', '班级', '姓名', '打字速度(字/分钟)'];
            data = [
                ['一年级', '1班', '张三', 60],
                ['一年级', '1班', '李四', 45],
                ['二年级', '2班', '王五', 80],
                ['', '', '', '']
            ];
        }
        
        // 合并表头和数据
        const wsData = [headers].concat(data);
        
        // 创建工作表
        const ws = XLSX.utils.aoa_to_sheet(wsData);
        
        // 设置列宽
        const colWidths = [
            { wch: 10 },  // 年级列宽
            { wch: 10 },  // 班级列宽
            { wch: 15 },  // 姓名列宽
            { wch: 15 }   // 数量列宽
        ];
        ws['!cols'] = colWidths;
        
        // 将工作表添加到工作簿
        XLSX.utils.book_append_sheet(wb, ws, type === 'medal' ? '奖章数据模板' : '打字数据模板');
        
        // 生成文件名
        const fileName = type === 'medal' ? '奖章数据模板.xlsx' : '打字数据模板.xlsx';
        
        // 下载文件
        XLSX.writeFile(wb, fileName);
        
        // 显示消息
        this.showMessage(`已下载${type === 'medal' ? '奖章' : '打字'}数据模板`);
    },

    /**
     * 数据保护：安全地输出日志，避免敏感信息泄露
     * @param {string} message 日志消息
     * @param {any} data 要输出的数据
     * @param {boolean} isSensitive 是否包含敏感数据
     */
    safeLog: function(message, data, isSensitive = false) {
        if (!isSensitive) {
            console.log(message, data);
            return;
        }

        // 对于敏感数据，只输出结构信息
        if (Array.isArray(data)) {
            console.log(message, `[数组，长度: ${data.length}]`);
            if (data.length > 0) {
                const sample = data[0];
                const structure = {};
                Object.keys(sample).forEach(key => {
                    structure[key] = typeof sample[key];
                });
                console.log('数据结构示例:', structure);
            }
        } else if (typeof data === 'object' && data !== null) {
            const structure = {};
            Object.keys(data).forEach(key => {
                structure[key] = typeof data[key];
            });
            console.log(message, '对象结构:', structure);
        } else {
            console.log(message, '[敏感数据已隐藏]');
        }
    },

    /**
     * 将中文年级转换为数字
     * @param {string} grade 中文年级，如'一年级'
     * @returns {number} 数字年级，如1
     */
    gradeToNumber: function(grade) {
        const gradeMap = {
            '一年级': 1,
            '二年级': 2,
            '三年级': 3,
            '四年级': 4,
            '五年级': 5,
            '六年级': 6
        };
        return gradeMap[grade] || 1; // 默认返回1年级
    },
    
    /**
     * 将数字年级转换为中文
     * @param {number|string} number 数字年级，如1
     * @returns {string} 中文年级，如'一年级'
     */
    numberToGrade: function(number) {
        const numberMap = {
            '1': '一年级',
            '2': '二年级',
            '3': '三年级',
            '4': '四年级',
            '5': '五年级',
            '6': '六年级'
        };
        return numberMap[number] || '一年级'; // 默认返回一年级
    },
    
    /**
     * 格式化日期时间
     * @param {Date|string|number} date 日期对象或时间戳
     * @param {string} format 格式化字符串，默认'YYYY-MM-DD HH:mm:ss'
     * @returns {string} 格式化后的日期字符串
     */
    formatDate: function(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!date) return '';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const pad = (num, size = 2) => String(num).padStart(size, '0');
        
        return format
            .replace('YYYY', d.getFullYear())
            .replace('MM', pad(d.getMonth() + 1))
            .replace('DD', pad(d.getDate()))
            .replace('HH', pad(d.getHours()))
            .replace('mm', pad(d.getMinutes()))
            .replace('ss', pad(d.getSeconds()));
    },
    
    /**
     * 显示确认对话框
     * @param {string} message 显示消息
     * @param {Function} onConfirm 确认回调
     * @param {Function} onCancel 取消回调
     */
    showConfirm: function(message, onConfirm, onCancel) {
        const confirmDialog = document.getElementById('confirmDialog');
        const confirmMessage = document.getElementById('confirmMessage');
        const confirmOkBtn = document.getElementById('confirmOkBtn');
        const confirmCancelBtn = document.getElementById('confirmCancelBtn');
        
        if (!confirmDialog || !confirmMessage) {
            // 如果对话框元素不存在，回退到原生confirm
            if (confirm(message)) {
                onConfirm && onConfirm();
            } else {
                onCancel && onCancel();
            }
            return;
        }
        
        // 设置消息
        confirmMessage.textContent = message;
        
        // 移除之前的事件监听器
        const newOkBtn = confirmOkBtn.cloneNode(true);
        const newCancelBtn = confirmCancelBtn.cloneNode(true);
        
        confirmOkBtn.parentNode.replaceChild(newOkBtn, confirmOkBtn);
        confirmCancelBtn.parentNode.replaceChild(newCancelBtn, confirmCancelBtn);
        
        // 添加新的事件监听器
        newOkBtn.addEventListener('click', () => {
            confirmDialog.style.display = 'none';
            onConfirm && onConfirm();
        });
        
        newCancelBtn.addEventListener('click', () => {
            confirmDialog.style.display = 'none';
            onCancel && onCancel();
        });
        
        // 显示对话框
        confirmDialog.style.display = 'flex';
    },
    
    /**
     * 获取年级和班级映射
     * @param {boolean} reverse - 是否反转键值对，如 true 时返回 {"四年级": 4}
     * @returns {Object} - 年级映射对象
     */
    getGradeMap: function(reverse = false) {
        const gradeMap = {
            1: '一年级',
            2: '二年级',
            3: '三年级',
            4: '四年级',
            5: '五年级',
            6: '六年级'
        };

        if (reverse) {
            const reversedMap = {};
            for (const key in gradeMap) {
                reversedMap[gradeMap[key]] = key;
            }
            return reversedMap;
        }
        
        return gradeMap;
    }
}; 