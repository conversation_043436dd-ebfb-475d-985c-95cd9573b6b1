-- 班级成绩管理系统V2.0 - PostgreSQL数据库表结构
-- 迁移到Supabase PostgreSQL

-- 创建学生表
CREATE TABLE IF NOT EXISTS students (
  id SERIAL PRIMARY KEY,
  student_identifier VARCHAR(255) NOT NULL UNIQUE,
  name VARCHAR(100) NOT NULL,
  grade INTEGER NOT NULL,
  class INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建学生表索引
CREATE INDEX IF NOT EXISTS idx_students_name ON students(name);
CREATE INDEX IF NOT EXISTS idx_students_grade_class ON students(grade, class);

-- 创建奖章表
CREATE TABLE IF NOT EXISTS medals (
  id SERIAL PRIMARY KEY,
  student_identifier VARCHAR(255) NOT NULL UNIQUE,
  count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT fk_medals_student FOREIGN KEY (student_identifier) 
    REFERENCES students(student_identifier) ON DELETE CASCADE
);

-- 创建打字记录表
CREATE TABLE IF NOT EXISTS typing_records (
  id SERIAL PRIMARY KEY,
  student_identifier VARCHAR(255) NOT NULL,
  speed INTEGER NOT NULL,
  accuracy DECIMAL(5,2) NOT NULL DEFAULT 100.00,
  language VARCHAR(10) NOT NULL DEFAULT 'zh',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT fk_typing_records_student FOREIGN KEY (student_identifier) 
    REFERENCES students(student_identifier) ON DELETE CASCADE
);

-- 创建打字记录表索引
CREATE INDEX IF NOT EXISTS idx_typing_student ON typing_records(student_identifier);
CREATE INDEX IF NOT EXISTS idx_typing_created ON typing_records(created_at);
CREATE INDEX IF NOT EXISTS idx_typing_student_language ON typing_records(student_identifier, language);

-- 创建最佳打字成绩表
CREATE TABLE IF NOT EXISTS typing_best (
  student_identifier VARCHAR(255) NOT NULL,
  language VARCHAR(10) NOT NULL DEFAULT 'zh',
  best_speed INTEGER NOT NULL,
  record_id INTEGER,
  record_date TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (student_identifier, language),
  CONSTRAINT fk_typing_best_student FOREIGN KEY (student_identifier) 
    REFERENCES students(student_identifier) ON DELETE CASCADE,
  CONSTRAINT fk_typing_best_record FOREIGN KEY (record_id) 
    REFERENCES typing_records(id) ON DELETE SET NULL
);

-- 创建最佳打字成绩表索引
CREATE INDEX IF NOT EXISTS idx_typing_best_speed ON typing_best(best_speed);

-- 创建学生会话表
CREATE TABLE IF NOT EXISTS student_sessions (
  id SERIAL PRIMARY KEY,
  student_identifier VARCHAR(255) NOT NULL,
  start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  end_time TIMESTAMP WITH TIME ZONE,
  CONSTRAINT fk_student_sessions_student FOREIGN KEY (student_identifier) 
    REFERENCES students(student_identifier) ON DELETE CASCADE
);

-- 创建学生会话表索引
CREATE INDEX IF NOT EXISTS idx_session_student ON student_sessions(student_identifier);
CREATE INDEX IF NOT EXISTS idx_session_times ON student_sessions(start_time, end_time);

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  display_name VARCHAR(100),
  email VARCHAR(255),
  role VARCHAR(20) NOT NULL DEFAULT 'teacher',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建用户表索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);

-- 创建日志表
CREATE TABLE IF NOT EXISTS logs (
  id SERIAL PRIMARY KEY,
  action VARCHAR(50) NOT NULL,
  details TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建日志表索引
CREATE INDEX IF NOT EXISTS idx_logs_action ON logs(action);
CREATE INDEX IF NOT EXISTS idx_logs_created ON logs(created_at);

-- 创建文章表
CREATE TABLE IF NOT EXISTS articles (
  id SERIAL PRIMARY KEY,
  title VARCHAR(200) NOT NULL,
  content TEXT NOT NULL,
  language VARCHAR(10) NOT NULL DEFAULT 'zh',
  difficulty INTEGER DEFAULT 1,
  grade INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建文章表索引
CREATE INDEX IF NOT EXISTS idx_articles_language ON articles(language);
CREATE INDEX IF NOT EXISTS idx_articles_difficulty ON articles(difficulty);
CREATE INDEX IF NOT EXISTS idx_articles_grade ON articles(grade);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要自动更新updated_at的表创建触发器
CREATE TRIGGER update_students_updated_at BEFORE UPDATE ON students 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_medals_updated_at BEFORE UPDATE ON medals 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_articles_updated_at BEFORE UPDATE ON articles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_typing_best_updated_at BEFORE UPDATE ON typing_best 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
