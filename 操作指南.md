# 班级成绩管理系统操作指南

## 一、系统说明

班级成绩管理系统是一个基于Node.js + Express + MySQL开发的Web应用程序，提供了教师端和学生端两个界面。系统支持学生信息管理、打字速度记录和奖章管理等功能。

### 教师端功能

1. **奖章管理**：记录和管理学生的奖章数量
   - 单个学生奖章增减
   - 批量选择学生进行奖章增减操作
   - 支持自定义增减奖章数量

2. **打字管理**：记录和管理学生的打字速度
   - 查看学生打字速度数据
   - 支持数据导入导出

3. **排行榜**：
   - 多种排行方式：支持个人排行、班级排行和年级排行
   - 图表可视化：使用柱状图直观展示排名数据
   - 特殊标记：前三名使用金、银、铜色彩进行特殊标记

### 学生端功能

1. **签到**：学生输入年级、班级、姓名和座位号进行签到

2. **打字练习**：
   - 支持中文和英文两种练习模式
   - 实时显示打字速度和正确率
   - 自动保存练习记录

3. **历史记录**：
   - 查看个人历史练习记录
   - 支持导出到Excel

## 二、系统使用方法

### 教师端（index.html）

1. **登录系统**
   - 访问服务器地址（例如：http://localhost:3000/index.html）
   - 默认用户名：admin
   - 默认密码：admin123

2. **模块选择**
   - 登录成功后选择"奖章管理"或"打字管理"模块

3. **奖章管理**
   - 筛选学生：通过年级、班级或姓名筛选学生
   - 增减奖章：点击"+"或"-"按钮增减单个学生的奖章
   - 批量操作：选择多个学生后使用"批量加章"或"批量减章"按钮
   - 查看排行：在右侧查看个人、班级或年级排行榜

4. **打字管理**
   - 筛选学生：通过年级、班级或姓名筛选学生
   - 查看数据：查看学生打字速度数据
   - 查看排行：在右侧查看个人、班级或年级排行榜

5. **数据导入导出**
   - 导出数据：点击"导出数据"按钮，导出为Excel文件
   - 导入数据：点击"导入数据"按钮，从Excel文件导入数据

### 学生端（student.html）

1. **签到**
   - 访问服务器地址（例如：http://localhost:3000/student.html）
   - 输入年级、班级、姓名和座位号
   - 点击"签到"按钮

2. **打字练习**
   - 选择语言：中文或英文
   - 点击"开始练习"按钮
   - 在输入框中输入显示的文本
   - 练习结束后查看结果

3. **查看历史记录**
   - 点击"查看历史记录"按钮
   - 可按日期筛选历史记录
   - 点击"导出Excel"导出历史记录

## 三、系统架构说明

系统采用现代化的三层架构设计：

1. **前端**：HTML、CSS、JavaScript
   - 使用原生JavaScript开发
   - 支持数据可视化和用户交互

2. **后端**：Node.js + Express
   - RESTful API设计
   - 支持各种数据操作和业务逻辑

3. **数据库**：MySQL
   - 存储学生信息、奖章、打字记录等数据
   - 支持高效查询和数据一致性

## 四、系统部署

1. **环境要求**：
   - Node.js v14.0.0及以上
   - MySQL 5.7及以上

2. **启动步骤**：
   ```
   # 安装依赖
   npm install
   
   # 初始化数据库
   npm run init-db
   
   # 启动服务器
   npm start
   ```

3. **数据库配置**
   在`.env`文件中设置数据库连接信息：
   ```
   DB_HOST=localhost
   DB_USER=root
   DB_PASSWORD=yourpassword
   DB_NAME=syxxstudent
   DB_PORT=3306
   ```

## 五、默认用户信息

教师端登录：
- 用户名: admin
- 密码: admin123

教师端登录：
- 用户名: teacher
- 密码: teacher123 