<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生打字练习系统</title>
    <link rel="stylesheet" href="/css/student-style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- SheetJS库 - 用于Excel导出 -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <!-- 数据库服务 -->
    <script src="/js/db.js"></script>
    <style>
        /* ... existing styles ... */
        
        .local-server-warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border: 1px solid #ffeeba;
            display: none;
        }
        
        /* 文章选择器高亮动画 */
        @keyframes selectionHighlight {
            0% { box-shadow: 0 0 5px rgba(74, 144, 226, 0.5); }
            50% { box-shadow: 0 0 15px rgba(74, 144, 226, 0.8); }
            100% { box-shadow: 0 0 5px rgba(74, 144, 226, 0.5); }
        }
        
        .highlight-selection {
            animation: selectionHighlight 1s ease infinite;
        }
        
        .highlight-selection select {
            border-color: #4a90e2 !important;
            border-width: 2px !important;
        }
        
        /* 按钮高亮动画 */
        @keyframes buttonHighlight {
            0% { box-shadow: 0 0 5px rgba(39, 174, 96, 0.5); }
            50% { box-shadow: 0 0 15px rgba(39, 174, 96, 0.8); transform: scale(1.05); }
            100% { box-shadow: 0 0 5px rgba(39, 174, 96, 0.5); }
        }
        
        .highlight-btn {
            animation: buttonHighlight 1s ease infinite;
            background-color: #27ae60 !important;
            color: white !important;
        }
        
        /* 对话框样式优化 */
        .dialog-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .dialog {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }
        
        .dialog-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .dialog-message {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .dialog-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .dialog-button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .dialog-button.confirm {
            background-color: #4a90e2;
            color: white;
        }
        
        .dialog-button.cancel {
            background-color: #f1f1f1;
            color: #666;
        }
        
        .dialog-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        /* 闪烁动画 */
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        .blink {
            animation: blink 1s ease-in-out infinite;
        }

        /* 强制确保左右布局在移动端 */
        @media screen and (max-width: 768px) {
            .typing-area-header {
                flex-wrap: wrap !important;
                padding: 8px 12px !important;
                margin-bottom: 10px !important;
                margin-top: 5px !important;
            }
            
            .typing-area-header > div:first-child {
                display: none !important;
            }
            
            .article-selection-controls {
                flex: 1 1 100% !important;
                margin-top: 10px !important;
                justify-content: center !important;
            }
            
            .article-selection-controls select {
                width: 100% !important;
                max-width: 300px !important;
                margin: 0 auto !important;
            }
        }
        
        /* 确保在桌面端也保持正确的布局 */
        @media screen and (min-width: 769px) {
            .typing-area-header {
                flex-wrap: nowrap !important;
            }
            
            .article-selection-controls {
                flex: 1 1 auto !important;
                margin-top: 0 !important;
            }
        }
        
        /* 确保文章选择器在所有设备上都居中 */
        .article-selection-controls {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            gap: 10px !important;
        }
        
        .article-selection-controls select {
            min-width: 200px !important;
            text-align: center !important;
        }
        
        /* 移动端特殊处理 */
        @media screen and (max-width: 480px) {
            .typing-area-header {
                flex-direction: column !important;
                align-items: center !important;
                gap: 10px !important;
            }
            
            .typing-area-header > div:first-child {
                display: flex !important;
                order: 2 !important;
            }
            
            .article-selection-controls {
                order: 1 !important;
                width: 100% !important;
            }
        }

        /* 统计显示区域样式 */
        .stats-display {
            transition: all 0.3s ease;
        }

        .stats-display:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .stat-item {
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: scale(1.05);
        }

        /* 计时器闪烁效果 */
        .blinking {
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
    </style>
</head>
<body oncontextmenu="return false;">
    <div class="local-server-warning" id="server-warning">
        <strong>注意：</strong> 检测到您直接从文件系统打开此页面。由于浏览器安全限制，API请求将不起作用。
        请使用以下方式运行：
        <ol>
            <li>使用Node.js服务器: <code>npm start</code></li>
            <li>然后在浏览器中访问: <code>http://localhost:3000/stu-type.html</code></li>
        </ol>
        <button onclick="this.parentElement.style.display='none'">我知道了</button>
    </div>

    <!-- 添加背景动画元素 -->
    <div class="animated-background">
        <div class="shape shape1"></div>
        <div class="shape shape2"></div>
        <div class="shape shape3"></div>
        <div class="shape shape4"></div>
    </div>

    <!-- 打字练习界面容器 -->
    <div id="student-container" style="display:block;">
        <!-- 打字练习界面 -->
        <div id="typing-practice" style="display:block;">
            <header>
                <h1>打字练习</h1>
                <div class="header-actions">
                    <span id="student-info"></span>
                    <button id="view-history">查看历史记录</button>
                </div>
            </header>
            
            <div class="practice-main-container">
                <!-- 右侧区域: 打字区 -->
                <div class="practice-content">
                    <!-- 打字区域 -->
                    <div class="typing-area glass-effect">
                        <!-- 顶部控制栏 -->
                        <div class="typing-area-header" style="display:flex; flex-direction:row; justify-content:space-between; align-items:center; width:100%; padding:10px 15px; border-bottom:1px solid rgba(0,0,0,0.1); margin-bottom:15px; margin-top:5px;">
                            <!-- 左侧按钮区域 -->
                            <div style="flex:0 0 auto; display:flex; gap:10px; align-items:center;">
                                <button id="start-practice" class="btn primary" style="height:40px; padding:0 20px; font-size:14px; border-radius:8px; background-color:#4a90e2; color:white; border:none; cursor:pointer; transition:all 0.3s;">
                                    开始练习
                                </button>
                                <button id="restart-practice" class="btn secondary" style="height:40px; padding:0 20px; font-size:14px; border-radius:8px; background-color:#f1f1f1; color:#666; border:none; cursor:pointer; transition:all 0.3s;">
                                    重新开始
                                </button>
                            </div>

                            <!-- 中间统计显示区域 -->
                            <div class="stats-display" style="flex:1; display:flex; justify-content:center; align-items:center; gap:30px; padding:0 20px;">
                                <div class="stat-item" style="text-align:center;">
                                    <div style="font-size:12px; color:#666; margin-bottom:3px; font-weight:500;">剩余时间</div>
                                    <div id="time-remaining" style="font-size:18px; font-weight:bold; color:#4a90e2;">5:00</div>
                                </div>
                                <div class="stat-item" style="text-align:center;">
                                    <div style="font-size:12px; color:#666; margin-bottom:3px; font-weight:500;">当前速度</div>
                                    <div style="display:flex; align-items:baseline; justify-content:center; gap:2px;">
                                        <span id="current-speed" style="font-size:18px; font-weight:bold; color:#27ae60;">0</span>
                                        <span style="font-size:10px; color:#666;">字/分</span>
                                    </div>
                                </div>
                                <div class="stat-item" style="text-align:center;">
                                    <div style="font-size:12px; color:#666; margin-bottom:3px; font-weight:500;">准确率</div>
                                    <div id="accuracy" style="font-size:18px; font-weight:bold; color:#e74c3c;">100%</div>
                                </div>
                            </div>

                            <!-- 文章选择区，合并中英文文章 -->
                            <div class="article-selection-controls" style="flex:0 0 auto; margin-right:15px; position:relative;">
                                <div style="font-size:13px; color:#666; margin-bottom:5px; font-weight:500;">选择文章</div>
                                <div style="display:flex; align-items:center; gap:10px;">
                                    <select id="article-select" class="article-select" style="width:220px; height:40px; padding:8px 15px; border-radius:8px; border:1px solid rgba(74,144,226,0.3); background-color:rgba(255,255,255,0.9); color:#333; box-shadow:0 2px 5px rgba(0,0,0,0.05); transition:all 0.3s;">
                                        <option value="" disabled selected>请选择练习文章</option>
                                        <optgroup label="中文文章">
                                            <!-- 中文文章选项将动态填充 -->
                                        </optgroup>
                                        <optgroup label="英文文章">
                                            <!-- 英文文章选项将动态填充 -->
                                        </optgroup>
                                    </select>
                                    <button id="refresh-articles" class="btn secondary" style="height:40px; width:40px; display:flex; align-items:center; justify-content:center; font-size:14px; border-radius:8px; background-color:#f1f1f1; color:#666; border:none; cursor:pointer; transition:all 0.3s;" title="刷新文章列表">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                                            <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 打字内容区 -->
                        <div id="typing-lines-container"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 历史记录界面 -->
        <div id="history-container" style="display:none;">
            <header>
                <h1>练习历史</h1>
                <div class="header-actions">
                    <button id="back-to-practice">返回练习</button>
                </div>
            </header>

            <div class="history-content">
                <!-- 添加打字速度折线图 - 移到最近练习记录上方 -->
                <div class="chart-container glass-effect">
                    <h3>打字速度趋势图</h3>
                    <div class="chart-wrapper">
                        <canvas id="typing-speed-chart"></canvas>
                    </div>
                </div>

                <div class="history-records glass-effect">
                    <h3>最近练习记录</h3>
                    <table id="history-table" class="data-table">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>年级</th>
                                <th>班级</th>
                                <th>姓名</th>
                                <th>座位号</th>
                                <th>打字速度</th>
                                <th>记录时间</th>
                                <th>周数</th>
                            </tr>
                        </thead>
                        <tbody id="history-data">
                            <!-- 历史数据将在这里动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loading" class="loading-overlay" style="display:none;">
        <div class="spinner"></div>
        <div class="loading-text">正在加载...</div>
    </div>

    <!-- 提示对话框 -->
    <div id="message-box" class="message-box">
        <div class="message-content"></div>
    </div>

    <!-- 确认对话框 -->
    <div class="dialog-container" id="dialog-container" style="display:none;">
        <div class="dialog">
            <div class="dialog-title" id="dialog-title">提示</div>
            <div class="dialog-message" id="dialog-message"></div>
            <div class="dialog-buttons">
                <button class="dialog-button confirm" id="dialog-confirm">确认</button>
                <button class="dialog-button cancel" id="dialog-cancel">取消</button>
            </div>
        </div>
    </div>

    <!-- 成绩展示弹窗 -->
    <div class="result-overlay" id="result-overlay" style="display:none;">
        <div class="result-box">
            <h2><span style="color:#4a90e2;">✓</span> 练习完成！</h2>
            <div class="result-content">
                <div class="result-item">
                    <div class="result-label">打字速度</div>
                    <div id="speed-result" class="result-value">0</div>
                    <div class="result-unit">字/分钟</div>
                </div>
                <div class="result-item">
                    <div class="result-label">正确率</div>
                    <div id="accuracy-result" class="result-value">100%</div>
                </div>
            </div>
            <div style="margin-top: 25px; text-align: center;">
                <div style="font-size: 15px; color: #666; margin-bottom: 15px;">成绩已保存，您可以继续练习其他文章</div>
                <button id="close-result" class="btn primary" style="background: linear-gradient(135deg, #4a90e2, #67b8ff); border: none; color: white; padding: 12px 30px; border-radius: 8px; font-size: 16px; font-weight: 600; box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3); transition: all 0.3s ease;">返回</button>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="/js/utils.js"></script>
    <script src="/js/student.js"></script>
    <script>
        // 检测是否从文件系统直接访问
        if (window.location.protocol === 'file:') {
            document.getElementById('server-warning').style.display = 'block';
        }
    </script>
</body>
</html>
