<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统配置诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagnostic-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #4a90e2;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #357abd;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
        .details {
            margin-top: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <h1>🔍 系统配置诊断工具</h1>
        <p>这个工具将帮助您检查Supabase配置和数据库状态</p>
        
        <!-- API连接测试 -->
        <div class="test-section">
            <h3>1. API服务器连接测试</h3>
            <button onclick="testAPIConnection()">测试API连接</button>
            <div id="api-status"></div>
        </div>
        
        <!-- 健康检查 -->
        <div class="test-section">
            <h3>2. 系统健康检查</h3>
            <button onclick="testHealth()">检查系统健康状态</button>
            <div id="health-status"></div>
        </div>
        
        <!-- 数据库连接测试 -->
        <div class="test-section">
            <h3>3. 数据库连接测试</h3>
            <button onclick="testDatabase()">测试数据库连接</button>
            <div id="db-status"></div>
        </div>
        
        <!-- 表结构检查 -->
        <div class="test-section">
            <h3>4. users表结构检查</h3>
            <button onclick="checkUsersTable()">检查users表结构</button>
            <div id="table-status"></div>
        </div>
        
        <!-- 注册API测试 -->
        <div class="test-section">
            <h3>5. 注册API功能测试</h3>
            <button onclick="testRegisterAPI()">测试注册API</button>
            <div id="register-status"></div>
        </div>
        
        <!-- 环境变量检查 -->
        <div class="test-section">
            <h3>6. 环境变量检查</h3>
            <button onclick="checkEnvironment()">检查环境配置</button>
            <div id="env-status"></div>
        </div>
        
        <!-- 一键全面检查 -->
        <div class="test-section">
            <h3>🚀 一键全面检查</h3>
            <button onclick="runFullDiagnostic()" style="background: #28a745; font-size: 16px; padding: 15px 30px;">开始全面诊断</button>
            <div id="full-status"></div>
        </div>
    </div>

    <script>
        // 显示状态的辅助函数
        function showStatus(elementId, type, message, details = null) {
            const element = document.getElementById(elementId);
            element.innerHTML = `
                <div class="status ${type}">${message}</div>
                ${details ? `<div class="details">${details}</div>` : ''}
            `;
        }

        // 1. API连接测试
        async function testAPIConnection() {
            showStatus('api-status', 'info', '正在测试API连接...');
            
            try {
                const response = await fetch('/api/test');
                const result = await response.json();
                
                if (response.ok) {
                    showStatus('api-status', 'success', 
                        '✅ API服务器连接正常', 
                        `<pre>${JSON.stringify(result, null, 2)}</pre>`
                    );
                } else {
                    showStatus('api-status', 'error', 
                        '❌ API服务器响应异常', 
                        `状态码: ${response.status}<br><pre>${JSON.stringify(result, null, 2)}</pre>`
                    );
                }
            } catch (error) {
                showStatus('api-status', 'error', 
                    '❌ 无法连接到API服务器', 
                    `错误: ${error.message}`
                );
            }
        }

        // 2. 健康检查
        async function testHealth() {
            showStatus('health-status', 'info', '正在检查系统健康状态...');
            
            try {
                const response = await fetch('/api/health');
                const result = await response.json();
                
                if (response.ok && result.status === 'healthy') {
                    showStatus('health-status', 'success', 
                        '✅ 系统健康状态良好', 
                        `数据库: ${result.database}<br>时间: ${result.timestamp}<br><pre>${JSON.stringify(result, null, 2)}</pre>`
                    );
                } else {
                    showStatus('health-status', 'error', 
                        '❌ 系统健康检查失败', 
                        `<pre>${JSON.stringify(result, null, 2)}</pre>`
                    );
                }
            } catch (error) {
                showStatus('health-status', 'error', 
                    '❌ 健康检查请求失败', 
                    `错误: ${error.message}`
                );
            }
        }

        // 3. 数据库连接测试
        async function testDatabase() {
            showStatus('db-status', 'info', '正在测试数据库连接...');
            
            try {
                const response = await fetch('/api/test-db', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ test: 'connection' })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showStatus('db-status', 'success', 
                        '✅ 数据库连接正常', 
                        `<pre>${JSON.stringify(result, null, 2)}</pre>`
                    );
                } else {
                    showStatus('db-status', 'error', 
                        '❌ 数据库连接失败', 
                        `<pre>${JSON.stringify(result, null, 2)}</pre>`
                    );
                }
            } catch (error) {
                showStatus('db-status', 'error', 
                    '❌ 数据库测试请求失败', 
                    `错误: ${error.message}`
                );
            }
        }

        // 4. 检查users表结构
        async function checkUsersTable() {
            showStatus('table-status', 'info', '正在检查users表结构...');
            
            try {
                const response = await fetch('/api/check-users-table');
                const result = await response.json();
                
                if (response.ok) {
                    const hasEmail = result.columns && result.columns.some(col => col.column_name === 'email');
                    
                    showStatus('table-status', hasEmail ? 'success' : 'warning', 
                        hasEmail ? '✅ users表结构正确，包含email字段' : '⚠️ users表缺少email字段', 
                        `表存在: ${result.exists ? '是' : '否'}<br>字段数量: ${result.columns ? result.columns.length : 0}<br><pre>${JSON.stringify(result, null, 2)}</pre>`
                    );
                } else {
                    showStatus('table-status', 'error', 
                        '❌ 无法检查users表结构', 
                        `<pre>${JSON.stringify(result, null, 2)}</pre>`
                    );
                }
            } catch (error) {
                showStatus('table-status', 'error', 
                    '❌ 表结构检查请求失败', 
                    `错误: ${error.message}`
                );
            }
        }

        // 5. 注册API测试
        async function testRegisterAPI() {
            showStatus('register-status', 'info', '正在测试注册API...');
            
            const testData = {
                username: 'test_' + Date.now(),
                displayName: '测试用户',
                email: '<EMAIL>',
                password: 'test123456',
                confirmPassword: 'test123456'
            };
            
            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showStatus('register-status', 'success', 
                        '✅ 注册API功能正常', 
                        `用户创建成功<br><pre>${JSON.stringify(result, null, 2)}</pre>`
                    );
                } else {
                    showStatus('register-status', 'error', 
                        '❌ 注册API测试失败', 
                        `状态码: ${response.status}<br>错误: ${result.error}<br><pre>${JSON.stringify(result, null, 2)}</pre>`
                    );
                }
            } catch (error) {
                showStatus('register-status', 'error', 
                    '❌ 注册API请求失败', 
                    `错误: ${error.message}`
                );
            }
        }

        // 6. 环境变量检查
        async function checkEnvironment() {
            showStatus('env-status', 'info', '正在检查环境配置...');
            
            try {
                const response = await fetch('/api/check-env');
                const result = await response.json();
                
                if (response.ok) {
                    const allConfigured = result.supabase_url && result.supabase_key && result.jwt_secret;
                    
                    showStatus('env-status', allConfigured ? 'success' : 'warning', 
                        allConfigured ? '✅ 环境变量配置完整' : '⚠️ 部分环境变量未配置', 
                        `Supabase URL: ${result.supabase_url ? '已配置' : '未配置'}<br>
                         Supabase Key: ${result.supabase_key ? '已配置' : '未配置'}<br>
                         JWT Secret: ${result.jwt_secret ? '已配置' : '未配置'}<br>
                         Node环境: ${result.node_env || '未设置'}`
                    );
                } else {
                    showStatus('env-status', 'error', 
                        '❌ 无法检查环境配置', 
                        `<pre>${JSON.stringify(result, null, 2)}</pre>`
                    );
                }
            } catch (error) {
                showStatus('env-status', 'error', 
                    '❌ 环境检查请求失败', 
                    `错误: ${error.message}`
                );
            }
        }

        // 一键全面检查
        async function runFullDiagnostic() {
            showStatus('full-status', 'info', '🔄 正在进行全面诊断，请稍候...');
            
            const tests = [
                { name: 'API连接', func: testAPIConnection },
                { name: '系统健康', func: testHealth },
                { name: '数据库连接', func: testDatabase },
                { name: '表结构', func: checkUsersTable },
                { name: '注册API', func: testRegisterAPI },
                { name: '环境变量', func: checkEnvironment }
            ];
            
            for (let i = 0; i < tests.length; i++) {
                showStatus('full-status', 'info', `🔄 正在执行: ${tests[i].name} (${i + 1}/${tests.length})`);
                await tests[i].func();
                await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            }
            
            showStatus('full-status', 'success', '✅ 全面诊断完成！请查看上方各项测试结果。');
        }
    </script>
</body>
</html>
