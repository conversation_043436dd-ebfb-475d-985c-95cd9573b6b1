<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生签到系统</title>
    <link rel="stylesheet" href="css/student-style.css">

    <!-- 数据库服务 -->
    <script src="js/db.js"></script>
    <style>
        /* ... existing styles ... */
        
        .local-server-warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border: 1px solid #ffeeba;
            display: none;
        }
        
        /* 签到成功样式 */
        .signin-success-btn {
            transition: all 0.3s ease;
        }

        .signin-success-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        /* 对话框样式优化 */
        .dialog-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(4px);
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .dialog {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            padding: 20px;
            width: 90%;
            max-width: 400px;
            transform: translateY(0);
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .dialog-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .dialog-message {
            font-size: 15px;
            line-height: 1.5;
            color: #555;
            margin-bottom: 20px;
        }
        
        .dialog-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .dialog-button {
            padding: 8px 18px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
        }
        
        .dialog-button.confirm {
            background-color: #4a90e2;
            color: white;
        }
        
        .dialog-button.confirm:hover {
            background-color: #3a7bc8;
        }
        
        .dialog-button.cancel {
            background-color: #f1f1f1;
            color: #555;
        }
        
        .dialog-button.cancel:hover {
            background-color: #e5e5e5;
        }
        
        /* 提示消息框样式 */
        .message-box {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%) translateY(-100px);
            padding: 12px 18px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            z-index: 1001;
            font-size: 15px;
            min-width: 250px;
            max-width: 80%;
            text-align: center;
            opacity: 0;
            transition: all 0.3s ease;
            color: white;
            pointer-events: none;
        }
        
        .message-box.show {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }
        
        .message-box.success {
            background-color: #27ae60;
        }
        
        .message-box.error {
            background-color: #e74c3c;
        }
        
        .message-box.warning {
            background-color: #f39c12;
        }
        
        .message-box.info {
            background-color: #4a90e2;
        }
        
        @keyframes messageAppear {
            0% { transform: translateX(-50%) translateY(-100px); opacity: 0; }
            10% { transform: translateX(-50%) translateY(0); opacity: 1; }
            90% { transform: translateX(-50%) translateY(0); opacity: 1; }
            100% { transform: translateX(-50%) translateY(-100px); opacity: 0; }
        }
        
        .message-box.show {
            animation: messageAppear 3s ease forwards;
        }
        
        /* 计时器闪烁效果 */
        @keyframes blink {
            0% { opacity: 1; }
            50% { opacity: 0.3; }
            100% { opacity: 1; }
        }
        
        .blinking {
            animation: blink 1s ease-in-out infinite;
        }



        /* 添加按钮禁用状态样式 */
        .btn[disabled] {
            background-color: #cccccc !important;
            color: #666666 !important;
            cursor: not-allowed !important;
            opacity: 0.7;
        }
        
        /* 按钮悬停效果 */
        .btn:not([disabled]):hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        /* 按钮点击效果 */
        .btn:not([disabled]):active {
            transform: translateY(0);
            box-shadow: none;
        }
    </style>
</head>
<body oncontextmenu="return false;">
    <div class="local-server-warning" id="server-warning">
        <strong>注意：</strong> 检测到您直接从文件系统打开此页面。由于浏览器安全限制，API请求将不起作用。
        请使用以下方式运行：
        <ol>
            <li>使用Node.js服务器: <code>npm start</code></li>
            <li>然后在浏览器中访问: <code>http://localhost:3000/student.html</code></li>
        </ol>
        <button onclick="this.parentElement.style.display='none'">我知道了</button>
    </div>

    <!-- 添加背景动画元素 -->
    <div class="animated-background">
        <div class="shape shape1"></div>
        <div class="shape shape2"></div>
        <div class="shape shape3"></div>
        <div class="shape shape4"></div>
    </div>

    <!-- 签到界面 -->
    <div id="signin-container">
        <div class="signin-box glass-effect">
            <h1>学生签到系统</h1>
            <div class="signin-form">
                <div class="form-group">
                    <label for="school">学校</label>
                    <select id="school" required>
                        <option value="" disabled selected>请选择学校</option>
                        <!-- 学校选项将从数据库动态加载 -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="grade">年级</label>
                    <select id="grade" required>
                        <option value="" disabled selected>请选择年级</option>
                        <option value="一年级">一年级</option>
                        <option value="二年级">二年级</option>
                        <option value="三年级">三年级</option>
                        <option value="四年级">四年级</option>
                        <option value="五年级">五年级</option>
                        <option value="六年级">六年级</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="class">班级</label>
                    <select id="class" required>
                        <option value="" disabled selected>请先选择年级</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="name">姓名</label>
                    <input type="text" id="name" placeholder="请输入姓名" required>
                </div>
                <div class="form-group">
                    <label for="seat">座位号</label>
                    <input type="text" id="seat" placeholder="请输入座位号(字母+数字)" required>
                </div>
                <button id="signin-btn">签到</button>
            </div>
        </div>
    </div>

    <!-- 签到成功界面 -->
    <div id="signin-success" style="display:none;">
        <div class="signin-box glass-effect">
            <div style="text-align: center; padding: 20px;">
                <h2 style="color: #27ae60; margin-bottom: 20px;">✅ 签到成功！</h2>
                <p style="font-size: 16px; color: #333; margin-bottom: 30px;">您已成功签到，可以开始打字练习了</p>
                <div style="display: flex; justify-content: center;">
                    <button id="go-to-typing" style="background-color: #4a90e2; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 16px; transition: all 0.3s ease;">开始打字练习</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loading" style="display:none;">
        <div class="loading-spinner"></div>
        <p>正在加载...</p>
    </div>

    <!-- 消息提示 -->
    <div id="message" class="message"></div>

    <!-- 消息提示框 -->
    <div id="message-box" class="message-box">
        <div class="message-content"></div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="js/student.js"></script>
    <script>
        // 检测是否从文件系统直接访问
        if (window.location.protocol === 'file:') {
            document.getElementById('server-warning').style.display = 'block';
        }
    </script>
</body>
</html> 