/**
 * 会话控制器
 * 处理学生会话的开始和结束
 */

const db = require('../config/db');
const fs = require('fs');
const path = require('path');

// 在Vercel环境中不创建日志目录（只读文件系统）
const logsDir = path.join(__dirname, '..', 'logs');
let canWriteLogs = false;

if (!process.env.VERCEL) {
  // 只在非Vercel环境下创建日志目录
  try {
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    canWriteLogs = true;
  } catch (error) {
    console.log('无法创建日志目录，将使用控制台日志:', error.message);
    canWriteLogs = false;
  }
}

/**
 * 开始学生会话
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.startSession = async (req, res) => {
  try {
    const { student_identifier, grade, class: className, name, strict_validation, seat } = req.body;
    
    console.log('开始会话请求:', {
      student_identifier, 
      grade, 
      class: className, 
      name, 
      strict_validation, 
      seat
    });
    
    // 验证必填字段
    if (!student_identifier) {
      return res.status(400).json({ error: '学生标识符为必填项' });
    }
    
    // 检查学生是否存在
    let student;
    
    if (strict_validation) {
      // 严格验证模式 - 同时验证年级、班级和姓名
      if (!grade || !className || !name) {
        return res.status(400).json({ error: '严格验证模式下，年级、班级和姓名为必填项' });
      }
      
      // 从student_identifier中提取年级数字
      const gradeNumber = student_identifier.split('_')[0];
      
      // 先检查学生是否存在于数据库中
      const existingStudent = await db.query(
        'SELECT * FROM students WHERE grade = ? OR class = ? OR name = ?',
        [gradeNumber, className, name]
      );
      
      if (existingStudent.length === 0) {
        return res.status(404).json({ error: '学生不存在，请检查输入信息是否正确' });
      }
      
      // 检查具体哪些字段不匹配
      const matchingFields = [];
      const mismatchFields = [];
      
      if (existingStudent[0].grade != gradeNumber) {
        mismatchFields.push('年级');
      } else {
        matchingFields.push('年级');
      }
      
      if (existingStudent[0].class != className) {
        mismatchFields.push('班级');
      } else {
        matchingFields.push('班级');
      }
      
      if (existingStudent[0].name != name) {
        mismatchFields.push('姓名');
      } else {
        matchingFields.push('姓名');
      }
      
      // 根据匹配和不匹配的字段构建错误消息
      if (mismatchFields.length > 0) {
        // 生成更加明确的错误消息，同时提供mismatchFields数组以供前端使用
        const errorMsg = `学生信息不匹配：${mismatchFields.join('、')}与系统记录不符，请检查后重试`;
        console.log('返回不匹配错误:', { error: errorMsg, mismatchFields: mismatchFields });
        return res.status(404).json({ 
          error: errorMsg,
          mismatchFields: mismatchFields
        });
      }
      
      // 正常查询学生信息
      student = await db.query(
        'SELECT * FROM students WHERE grade = ? AND class = ? AND name = ?',
        [gradeNumber, className, name]
      );
      
      if (student.length === 0) {
        return res.status(404).json({ error: '学生不存在或年级、班级、姓名不匹配' });
      }
      
      // 检查student_identifier是否与数据库中的一致
      if (student[0].student_identifier !== student_identifier) {
        return res.status(400).json({ error: '学生标识符不匹配，请检查年级、班级和姓名' });
      }
    } else {
      // 标准模式 - 仅验证student_identifier
      student = await db.query(
        'SELECT * FROM students WHERE student_identifier = ?',
        [student_identifier]
      );
      
      if (student.length === 0) {
        return res.status(404).json({ error: '学生不存在' });
      }
    }
    
    // 检查学生是否已有未结束的会话
    const activeSessions = await db.query(
      'SELECT * FROM student_sessions WHERE student_identifier = ? AND end_time IS NULL',
      [student_identifier]
    );
    
    if (activeSessions.length > 0) {
      return res.status(400).json({ error: '学生已有未结束的会话' });
    }
    
    // 创建新会话
    const result = await db.execute(
      'INSERT INTO student_sessions (student_identifier, start_time) VALUES (?, NOW())',
      [student_identifier]
    );
    
    // 获取会话ID
    const sessionId = await db.getLastInsertId();
    
    // 获取新创建的会话
    const newSession = await db.query(
      'SELECT * FROM student_sessions WHERE id = ?',
      [sessionId]
    );
    
    console.log(`成功创建会话，ID: ${sessionId}, 学生: ${student_identifier}`);
    
    // 在attendance表中添加签到记录（使用单独的try-catch块，不影响主流程）
    try {
      // 先检查students表中是否存在对应的学生（验证外键约束）
      const studentExists = await db.query(
        'SELECT COUNT(*) as count FROM students WHERE student_identifier = ?',
        [student_identifier]
      );
      
      if (studentExists[0].count === 0) {
        throw new Error(`学生 ${student_identifier} 在students表中不存在，无法创建签到记录`);
      }
      
      const today = new Date().toISOString().split('T')[0]; // 获取当前日期，格式为YYYY-MM-DD
      
      // 验证是否已经有今天的签到记录
      const existingAttendance = await db.query(
        'SELECT * FROM attendance WHERE student_identifier = ? AND attendance_date = ?',
        [student_identifier, today]
      );
      
      console.log(`检查学生 ${student_identifier} 今日签到记录:`, existingAttendance.length > 0 ? '已存在' : '不存在');
      
      // 如果没有今日签到记录，则添加新的签到记录
      if (existingAttendance.length === 0) {
        // 获取学生的年级、班级、姓名信息
        const studentInfo = student[0];
        
        console.log('准备插入签到记录:', {
          student_identifier,
          grade: studentInfo.grade,
          class: studentInfo.class,
          name: studentInfo.name,
          date: today,
          seat: seat || null
        });
        
        const insertResult = await db.execute(
          'INSERT INTO attendance (student_identifier, grade, class, name, attendance_date, seat_number, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())',
          [
            student_identifier,
            studentInfo.grade,
            studentInfo.class,
            studentInfo.name,
            today,
            seat || null, // 如果提供了座位号，则使用；否则为null
            'present'
          ]
        );
        
        // 获取插入的记录ID
        const attendanceId = await db.getLastInsertId();
        
        console.log(`为学生 ${student_identifier} 创建了签到记录，ID: ${attendanceId}`);

        // 只在可以写入日志时才写入文件
        if (canWriteLogs) {
          try {
            fs.appendFileSync(
              path.join(logsDir, 'attendance-success.log'),
              `[${new Date().toISOString()}] 创建签到记录成功 - 学生: ${student_identifier}, ID: ${attendanceId}, 日期: ${today}\n`
            );
          } catch (logError) {
            console.log('无法写入签到成功日志:', logError.message);
          }
        }
      } else {
        console.log(`学生 ${student_identifier} 今日已有签到记录，不再创建新记录`);
      }
    } catch (attendanceError) {
      console.error('创建签到记录失败:', attendanceError);

      // 记录错误到专门的日志文件，但不影响会话创建
      if (canWriteLogs) {
        try {
          fs.appendFileSync(
            path.join(logsDir, 'attendance-errors.log'),
            `[${new Date().toISOString()}] 创建签到记录失败 - 学生: ${student_identifier}, 错误: ${attendanceError.message}\n${attendanceError.stack}\n\n`
          );
        } catch (logError) {
          console.log('无法写入签到错误日志:', logError.message);
        }
      }
    }
    
    // 记录日志
    await db.execute(
      'INSERT INTO logs (action, details, created_at) VALUES (?, ?, NOW())',
      ['session_start', `学生 ${student_identifier} 开始会话 ${sessionId}`]
    );
    
    res.status(201).json({ data: newSession[0] });
  } catch (error) {
    console.error('开始会话错误:', error);

    // 记录错误到日志文件
    if (canWriteLogs) {
      try {
        fs.appendFileSync(
          path.join(logsDir, 'session-errors.log'),
          `[${new Date().toISOString()}] 开始会话失败: ${error.message}\n${error.stack}\n\n`
        );
      } catch (logError) {
        console.log('无法写入会话错误日志:', logError.message);
      }
    }
    
    res.status(500).json({ error: '开始会话失败: ' + error.message });
  }
};

/**
 * 结束学生会话
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.endSession = async (req, res) => {
  try {
    const { student_identifier } = req.body;
    
    // 验证必填字段
    if (!student_identifier) {
      return res.status(400).json({ error: '学生标识符为必填项' });
    }
    
    // 检查学生是否存在
    const student = await db.query(
      'SELECT * FROM students WHERE student_identifier = ?',
      [student_identifier]
    );
    
    if (student.length === 0) {
      // 即使学生不存在，也尝试结束可能的会话（容错处理）
      console.log(`尝试结束不存在学生 ${student_identifier} 的会话`);
      
      // 查找学生未结束的会话
      const activeSessions = await db.query(
        'SELECT * FROM student_sessions WHERE student_identifier = ? AND end_time IS NULL',
        [student_identifier]
      );
      
      if (activeSessions.length === 0) {
        return res.status(404).json({ error: '学生没有未结束的会话' });
      }
      
      // 继续结束会话流程
    }
    
    // 查找学生未结束的会话
    const activeSessions = await db.query(
      'SELECT * FROM student_sessions WHERE student_identifier = ? AND end_time IS NULL',
      [student_identifier]
    );
    
    if (activeSessions.length === 0) {
      return res.status(404).json({ error: '学生没有未结束的会话' });
    }
    
    const sessionId = activeSessions[0].id;
    
    // 结束会话
    await db.execute(
      'UPDATE student_sessions SET end_time = NOW() WHERE id = ?',
      [sessionId]
    );
    
    // 获取更新后的会话
    const updatedSession = await db.query(
      'SELECT * FROM student_sessions WHERE id = ?',
      [sessionId]
    );
    
    // 记录日志
    await db.execute(
      'INSERT INTO logs (action, details, created_at) VALUES (?, ?, NOW())',
      ['session_end', `学生 ${student_identifier} 结束会话 ${sessionId}`]
    );
    
    res.status(200).json({ data: updatedSession[0] });
  } catch (error) {
    console.error('结束会话错误:', error);
    res.status(500).json({ error: '结束会话失败: ' + error.message });
  }
}; 