-- 教师权限系统数据库更新脚本
-- 用于实现教师只能看到自己学校、班级、学生的权限控制

-- 1. 创建教师-学校关联表
CREATE TABLE IF NOT EXISTS teacher_school_assignments (
  id SERIAL PRIMARY KEY,
  teacher_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  school_id INTEGER NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(teacher_id, school_id)
);

-- 2. 创建教师-年级-班级权限表
CREATE TABLE IF NOT EXISTS teacher_class_permissions (
  id SERIAL PRIMARY KEY,
  teacher_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  school_id INTEGER NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
  grade INTEGER NOT NULL,
  class INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(teacher_id, school_id, grade, class),
  FOREIGN KEY (teacher_id, school_id) REFERENCES teacher_school_assignments(teacher_id, school_id) ON DELETE CASCADE
);

-- 3. 创建学校层级结构表（用于管理年级和班级配置）
CREATE TABLE IF NOT EXISTS school_grade_configs (
  id SERIAL PRIMARY KEY,
  school_id INTEGER NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
  grade INTEGER NOT NULL,
  class_count INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(school_id, grade)
);

-- 4. 给students表添加一些缺失的字段（如果不存在）
ALTER TABLE students ADD COLUMN IF NOT EXISTS seat_number INTEGER;
ALTER TABLE students ADD COLUMN IF NOT EXISTS gender VARCHAR(10);
ALTER TABLE students ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active';

-- 5. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_teacher_school_assignments_teacher ON teacher_school_assignments(teacher_id);
CREATE INDEX IF NOT EXISTS idx_teacher_school_assignments_school ON teacher_school_assignments(school_id);
CREATE INDEX IF NOT EXISTS idx_teacher_class_permissions_teacher ON teacher_class_permissions(teacher_id);
CREATE INDEX IF NOT EXISTS idx_teacher_class_permissions_school ON teacher_class_permissions(school_id);
CREATE INDEX IF NOT EXISTS idx_teacher_class_permissions_grade_class ON teacher_class_permissions(grade, class);
CREATE INDEX IF NOT EXISTS idx_school_grade_configs_school ON school_grade_configs(school_id);
CREATE INDEX IF NOT EXISTS idx_students_status ON students(status);

-- 6. 插入默认的学校年级配置（示例数据）
INSERT INTO school_grade_configs (school_id, grade, class_count) 
SELECT id, 1, 3 FROM schools WHERE name = '示例小学'
ON CONFLICT (school_id, grade) DO NOTHING;

INSERT INTO school_grade_configs (school_id, grade, class_count) 
SELECT id, 2, 3 FROM schools WHERE name = '示例小学'
ON CONFLICT (school_id, grade) DO NOTHING;

INSERT INTO school_grade_configs (school_id, grade, class_count) 
SELECT id, 3, 4 FROM schools WHERE name = '示例小学'
ON CONFLICT (school_id, grade) DO NOTHING;

INSERT INTO school_grade_configs (school_id, grade, class_count) 
SELECT id, 4, 4 FROM schools WHERE name = '示例小学'
ON CONFLICT (school_id, grade) DO NOTHING;

INSERT INTO school_grade_configs (school_id, grade, class_count) 
SELECT id, 5, 4 FROM schools WHERE name = '示例小学'
ON CONFLICT (school_id, grade) DO NOTHING;

INSERT INTO school_grade_configs (school_id, grade, class_count) 
SELECT id, 6, 4 FROM schools WHERE name = '示例小学'
ON CONFLICT (school_id, grade) DO NOTHING;

-- 为测试中学添加配置
INSERT INTO school_grade_configs (school_id, grade, class_count) 
SELECT id, 1, 2 FROM schools WHERE name = '测试中学'
ON CONFLICT (school_id, grade) DO NOTHING;

INSERT INTO school_grade_configs (school_id, grade, class_count) 
SELECT id, 2, 2 FROM schools WHERE name = '测试中学'
ON CONFLICT (school_id, grade) DO NOTHING;

INSERT INTO school_grade_configs (school_id, grade, class_count) 
SELECT id, 3, 2 FROM schools WHERE name = '测试中学'
ON CONFLICT (school_id, grade) DO NOTHING;

-- 7. 为现有的teacher用户分配示例权限
-- 获取teacher用户ID和学校ID，然后分配权限
DO $$
DECLARE
    teacher_user_id INTEGER;
    school1_id INTEGER;
    school2_id INTEGER;
BEGIN
    -- 获取teacher用户ID
    SELECT id INTO teacher_user_id FROM users WHERE username = 'teacher' AND role = 'teacher';
    
    -- 获取学校ID
    SELECT id INTO school1_id FROM schools WHERE name = '示例小学';
    SELECT id INTO school2_id FROM schools WHERE name = '测试中学';
    
    IF teacher_user_id IS NOT NULL AND school1_id IS NOT NULL THEN
        -- 分配教师到示例小学
        INSERT INTO teacher_school_assignments (teacher_id, school_id) 
        VALUES (teacher_user_id, school1_id)
        ON CONFLICT (teacher_id, school_id) DO NOTHING;
        
        -- 分配教师到3年级1班和3年级2班（示例小学）
        INSERT INTO teacher_class_permissions (teacher_id, school_id, grade, class) 
        VALUES 
            (teacher_user_id, school1_id, 3, 1),
            (teacher_user_id, school1_id, 3, 2),
            (teacher_user_id, school1_id, 4, 1)
        ON CONFLICT (teacher_id, school_id, grade, class) DO NOTHING;
    END IF;
    
    IF teacher_user_id IS NOT NULL AND school2_id IS NOT NULL THEN
        -- 分配教师到测试中学
        INSERT INTO teacher_school_assignments (teacher_id, school_id) 
        VALUES (teacher_user_id, school2_id)
        ON CONFLICT (teacher_id, school_id) DO NOTHING;
        
        -- 分配教师到5年级1班（测试中学）
        INSERT INTO teacher_class_permissions (teacher_id, school_id, grade, class) 
        VALUES 
            (teacher_user_id, school2_id, 5, 1),
            (teacher_user_id, school2_id, 5, 2)
        ON CONFLICT (teacher_id, school_id, grade, class) DO NOTHING;
    END IF;
END $$;

-- 8. 创建视图用于快速查询教师权限
CREATE OR REPLACE VIEW teacher_permissions_view AS
SELECT 
    u.id as teacher_id,
    u.username as teacher_username,
    u.display_name as teacher_name,
    s.id as school_id,
    s.name as school_name,
    tcp.grade,
    tcp.class,
    COUNT(st.id) as student_count
FROM users u
INNER JOIN teacher_school_assignments tsa ON u.id = tsa.teacher_id
INNER JOIN schools s ON tsa.school_id = s.id
LEFT JOIN teacher_class_permissions tcp ON u.id = tcp.teacher_id AND s.id = tcp.school_id
LEFT JOIN students st ON s.id = st.school_id AND tcp.grade = st.grade AND tcp.class = st.class
WHERE u.role = 'teacher'
GROUP BY u.id, u.username, u.display_name, s.id, s.name, tcp.grade, tcp.class
ORDER BY u.display_name, s.name, tcp.grade, tcp.class;

-- 9. 添加表注释
COMMENT ON TABLE teacher_school_assignments IS '教师-学校关联表，定义教师可以管理哪些学校';
COMMENT ON TABLE teacher_class_permissions IS '教师-班级权限表，定义教师在特定学校可以管理哪些年级和班级';
COMMENT ON TABLE school_grade_configs IS '学校年级配置表，定义每个学校每个年级有多少个班级';
COMMENT ON VIEW teacher_permissions_view IS '教师权限视图，方便查询教师的完整权限信息';

-- 完成
SELECT '教师权限系统数据库更新完成！' as message;