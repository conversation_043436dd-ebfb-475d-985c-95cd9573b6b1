/**
 * 调试教师权限数据脚本
 * 用于检查教师权限表中的数据是否正确
 */

const db = require('./config/db');

async function debugTeacherPermissions() {
  console.log('=== 调试教师权限数据 ===\n');

  try {
    // 1. 获取所有教师
    console.log('1. 获取所有教师...');
    const { data: teachers, error: teachersError } = await db.supabase
      .from('users')
      .select('id, username, display_name, role')
      .eq('role', 'teacher')
      .order('username');

    if (teachersError) {
      console.error('获取教师失败:', teachersError);
      return;
    }

    console.log(`找到 ${teachers.length} 个教师:`);
    teachers.forEach(teacher => {
      console.log(`  - ${teacher.username} (${teacher.display_name || '无显示名'}) [ID: ${teacher.id}]`);
    });
    console.log('');

    // 2. 检查每个教师的权限
    for (const teacher of teachers) {
      console.log(`=== 检查教师: ${teacher.username} (ID: ${teacher.id}) ===`);

      // 检查学校分配
      const { data: schoolAssignments, error: schoolError } = await db.supabase
        .from('teacher_school_assignments')
        .select(`
          id,
          school_id,
          created_at,
          schools(
            id,
            name
          )
        `)
        .eq('teacher_id', teacher.id);

      if (schoolError) {
        console.error(`  获取学校分配失败:`, schoolError);
      } else {
        console.log(`  学校分配 (${schoolAssignments?.length || 0} 个):`);
        if (schoolAssignments && schoolAssignments.length > 0) {
          schoolAssignments.forEach(sa => {
            console.log(`    - 学校: ${sa.schools?.name || '未知'} (ID: ${sa.school_id})`);
            console.log(`      分配时间: ${sa.created_at}`);
          });
        } else {
          console.log(`    无学校分配`);
        }
      }

      // 检查班级权限
      const { data: classPermissions, error: classError } = await db.supabase
        .from('teacher_class_permissions')
        .select(`
          id,
          school_id,
          grade,
          class,
          created_at,
          schools(
            id,
            name
          )
        `)
        .eq('teacher_id', teacher.id)
        .order('school_id')
        .order('grade')
        .order('class');

      if (classError) {
        console.error(`  获取班级权限失败:`, classError);
      } else {
        console.log(`  班级权限 (${classPermissions?.length || 0} 个):`);
        if (classPermissions && classPermissions.length > 0) {
          classPermissions.forEach(cp => {
            console.log(`    - ${cp.schools?.name || '未知学校'}: ${cp.grade}年级${cp.class}班 (学校ID: ${cp.school_id})`);
            console.log(`      创建时间: ${cp.created_at}`);
          });
        } else {
          console.log(`    无班级权限`);
        }
      }

      console.log('');
    }

    // 3. 检查所有学校
    console.log('=== 所有学校信息 ===');
    const { data: schools, error: schoolsError } = await db.supabase
      .from('schools')
      .select('id, name, created_at')
      .order('name');

    if (schoolsError) {
      console.error('获取学校失败:', schoolsError);
    } else {
      console.log(`找到 ${schools.length} 个学校:`);
      schools.forEach(school => {
        console.log(`  - ${school.name} (ID: ${school.id}) - 创建时间: ${school.created_at}`);
      });
    }

    // 4. 检查权限表的完整性
    console.log('\n=== 权限表完整性检查 ===');
    
    // 检查是否有孤立的班级权限（没有对应的学校分配）
    const { data: orphanedPermissions, error: orphanError } = await db.supabase
      .from('teacher_class_permissions')
      .select(`
        id,
        teacher_id,
        school_id,
        grade,
        class,
        users(username),
        schools(name)
      `)
      .not('teacher_id', 'in', `(
        SELECT DISTINCT teacher_id 
        FROM teacher_school_assignments 
        WHERE teacher_school_assignments.teacher_id = teacher_class_permissions.teacher_id 
        AND teacher_school_assignments.school_id = teacher_class_permissions.school_id
      )`);

    if (orphanError) {
      console.error('检查孤立权限失败:', orphanError);
    } else if (orphanedPermissions && orphanedPermissions.length > 0) {
      console.log(`发现 ${orphanedPermissions.length} 个孤立的班级权限（没有对应的学校分配）:`);
      orphanedPermissions.forEach(op => {
        console.log(`  - 教师: ${op.users?.username}, 学校: ${op.schools?.name}, 班级: ${op.grade}年级${op.class}班`);
      });
    } else {
      console.log('没有发现孤立的班级权限');
    }

  } catch (error) {
    console.error('调试过程中发生错误:', error);
  }
}

// 运行调试
debugTeacherPermissions().then(() => {
  console.log('\n=== 调试完成 ===');
  process.exit(0);
}).catch(error => {
  console.error('调试失败:', error);
  process.exit(1);
});
