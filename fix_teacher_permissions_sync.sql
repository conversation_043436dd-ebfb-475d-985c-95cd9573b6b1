-- 教师权限数据同步修复脚本
-- 解决添加学生后无法显示的问题
-- 确保teacher_class_permissions和teacher_school_assignments表数据一致

-- 1. 检查当前权限状态
SELECT 
  'teacher_class_permissions' as table_name,
  COUNT(*) as record_count
FROM teacher_class_permissions
UNION ALL
SELECT 
  'teacher_school_assignments' as table_name,
  COUNT(*) as record_count
FROM teacher_school_assignments;

-- 2. 查看权限不一致的情况
SELECT 
  tcp.teacher_id,
  u.username,
  tcp.school_id,
  s.name as school_name,
  tcp.grade,
  tcp.class,
  CASE WHEN tsa.id IS NULL THEN '缺少学校权限' ELSE '权限正常' END as status
FROM teacher_class_permissions tcp
LEFT JOIN teacher_school_assignments tsa ON tcp.teacher_id = tsa.teacher_id AND tcp.school_id = tsa.school_id
LEFT JOIN users u ON tcp.teacher_id = u.id
LEFT JOIN schools s ON tcp.school_id = s.id
ORDER BY tcp.teacher_id, tcp.school_id;

-- 3. 自动修复：为所有有班级权限但缺少学校权限的教师添加学校权限
INSERT INTO teacher_school_assignments (teacher_id, school_id, created_at, updated_at)
SELECT DISTINCT 
  tcp.teacher_id,
  tcp.school_id,
  NOW(),
  NOW()
FROM teacher_class_permissions tcp
LEFT JOIN teacher_school_assignments tsa ON tcp.teacher_id = tsa.teacher_id AND tcp.school_id = tsa.school_id
WHERE tsa.id IS NULL
ON CONFLICT (teacher_id, school_id) DO NOTHING;

-- 4. 验证修复结果
SELECT 
  '修复后权限检查' as description,
  COUNT(*) as missing_school_permissions
FROM teacher_class_permissions tcp
LEFT JOIN teacher_school_assignments tsa ON tcp.teacher_id = tsa.teacher_id AND tcp.school_id = tsa.school_id
WHERE tsa.id IS NULL;

-- 5. 显示最终权限状态
SELECT 
  u.username,
  s.name as school_name,
  COUNT(tcp.id) as class_permissions,
  CASE WHEN tsa.id IS NOT NULL THEN '有学校权限' ELSE '无学校权限' END as school_permission_status
FROM users u
LEFT JOIN teacher_class_permissions tcp ON u.id = tcp.teacher_id
LEFT JOIN teacher_school_assignments tsa ON u.id = tsa.teacher_id AND tcp.school_id = tsa.school_id
LEFT JOIN schools s ON tcp.school_id = s.id
WHERE u.role = 'teacher'
GROUP BY u.id, u.username, s.id, s.name, tsa.id
ORDER BY u.username, s.name;

-- 6. 创建触发器函数，确保未来添加班级权限时自动添加学校权限
CREATE OR REPLACE FUNCTION ensure_teacher_school_permission()
RETURNS TRIGGER AS $$
BEGIN
  -- 检查是否已有学校权限
  IF NOT EXISTS (
    SELECT 1 FROM teacher_school_assignments 
    WHERE teacher_id = NEW.teacher_id AND school_id = NEW.school_id
  ) THEN
    -- 自动添加学校权限
    INSERT INTO teacher_school_assignments (teacher_id, school_id, created_at, updated_at)
    VALUES (NEW.teacher_id, NEW.school_id, NOW(), NOW())
    ON CONFLICT (teacher_id, school_id) DO NOTHING;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. 创建触发器
DROP TRIGGER IF EXISTS trigger_ensure_school_permission ON teacher_class_permissions;
CREATE TRIGGER trigger_ensure_school_permission
  AFTER INSERT ON teacher_class_permissions
  FOR EACH ROW
  EXECUTE FUNCTION ensure_teacher_school_permission();

-- 8. 显示修复完成信息
SELECT 
  '权限同步修复完成' as status,
  '已自动为所有班级权限添加对应的学校权限' as description,
  '已创建触发器确保未来数据一致性' as future_protection;
