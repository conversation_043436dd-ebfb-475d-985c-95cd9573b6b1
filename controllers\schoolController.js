/**
 * 学校控制器
 * 处理所有与学校相关的请求
 */

const db = require('../config/db');

/**
 * 获取所有学校
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.getAllSchools = async (req, res) => {
  try {
    const { data: schools, error } = await db.supabase
      .from('schools')
      .select('*')
      .order('name');

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      data: schools,
      total: schools.length
    });
  } catch (error) {
    console.error('获取学校列表错误:', error);
    res.status(500).json({ 
      success: false,
      error: '获取学校列表失败: ' + error.message 
    });
  }
};

/**
 * 创建学校
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.createSchool = async (req, res) => {
  try {
    const { name, address, contact_phone, contact_email, description } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        error: '学校名称是必需的'
      });
    }

    // 检查学校名称是否已存在
    const { data: existingSchool, error: checkError } = await db.supabase
      .from('schools')
      .select('id')
      .eq('name', name)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 表示没有找到记录
      throw checkError;
    }

    if (existingSchool) {
      return res.status(409).json({
        success: false,
        error: '学校名称已存在'
      });
    }

    // 创建学校
    const { data: newSchool, error } = await db.supabase
      .from('schools')
      .insert({
        name,
        address,
        contact_phone,
        contact_email,
        description
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    res.status(201).json({
      success: true,
      message: '学校创建成功',
      data: newSchool
    });
  } catch (error) {
    console.error('创建学校错误:', error);
    res.status(500).json({ 
      success: false,
      error: '创建学校失败: ' + error.message 
    });
  }
};

/**
 * 获取教师关联的学校
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.getTeacherSchools = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    console.log(`用户 ${req.user.username} (${userRole}) 请求关联学校列表`);

    // 使用数据库函数获取学校列表
    const { data: schools, error } = await db.supabase
      .rpc('get_teacher_schools', { p_teacher_id: userId });

    if (error) {
      console.error('获取教师学校列表失败:', error);
      throw error;
    }

    console.log(`返回 ${schools.length} 个学校记录`);

    res.json({
      success: true,
      data: schools,
      total: schools.length,
      user_role: userRole
    });

  } catch (error) {
    console.error('获取教师学校列表错误:', error);
    res.status(500).json({
      success: false,
      error: '获取教师学校列表失败: ' + error.message
    });
  }
};

/**
 * 教师关联到学校
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.associateTeacherToSchool = async (req, res) => {
  try {
    const userId = req.user.id;
    const { school_id } = req.body;

    if (!school_id) {
      return res.status(400).json({
        success: false,
        error: '学校ID是必需的'
      });
    }

    // 检查学校是否存在
    const { data: school, error: schoolError } = await db.supabase
      .from('schools')
      .select('name')
      .eq('id', school_id)
      .single();

    if (schoolError || !school) {
      return res.status(404).json({
        success: false,
        error: '学校不存在'
      });
    }

    // 检查是否已经关联
    const { data: existingAssociation, error: checkError } = await db.supabase
      .from('teacher_schools')
      .select('id')
      .eq('teacher_id', userId)
      .eq('school_id', school_id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError;
    }

    if (existingAssociation) {
      return res.status(409).json({
        success: false,
        error: '您已经关联了这所学校'
      });
    }

    // 创建关联
    const { data: association, error } = await db.supabase
      .from('teacher_schools')
      .insert({
        teacher_id: userId,
        school_id: school_id
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      message: `成功关联到学校 ${school.name}`,
      data: association
    });

  } catch (error) {
    console.error('关联教师到学校错误:', error);
    res.status(500).json({
      success: false,
      error: '关联教师到学校失败: ' + error.message
    });
  }
};

/**
 * 更新学校信息
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.updateSchool = async (req, res) => {
  try {
    const schoolId = req.params.id;
    const { name, address, contact_phone, contact_email, description } = req.body;

    // 只有管理员可以更新学校信息
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: '只有管理员可以更新学校信息'
      });
    }

    if (!name) {
      return res.status(400).json({
        success: false,
        error: '学校名称是必需的'
      });
    }

    // 检查学校是否存在
    const { data: existingSchool, error: checkError } = await db.supabase
      .from('schools')
      .select('id')
      .eq('id', schoolId)
      .single();

    if (checkError || !existingSchool) {
      return res.status(404).json({
        success: false,
        error: '学校不存在'
      });
    }

    // 更新学校信息
    const { data: updatedSchool, error } = await db.supabase
      .from('schools')
      .update({
        name,
        address,
        contact_phone,
        contact_email,
        description,
        updated_at: new Date().toISOString()
      })
      .eq('id', schoolId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      message: '学校信息更新成功',
      data: updatedSchool
    });

  } catch (error) {
    console.error('更新学校信息错误:', error);
    res.status(500).json({
      success: false,
      error: '更新学校信息失败: ' + error.message
    });
  }
};

/**
 * 删除学校
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.deleteSchool = async (req, res) => {
  try {
    const schoolId = req.params.id;

    // 只有管理员可以删除学校
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: '只有管理员可以删除学校'
      });
    }

    // 检查学校是否存在
    const { data: existingSchool, error: checkError } = await db.supabase
      .from('schools')
      .select('name')
      .eq('id', schoolId)
      .single();

    if (checkError || !existingSchool) {
      return res.status(404).json({
        success: false,
        error: '学校不存在'
      });
    }

    // 检查是否有关联的数据
    const { data: relatedData, error: relatedError } = await db.supabase
      .from('teacher_schools')
      .select('id')
      .eq('school_id', schoolId)
      .limit(1);

    if (relatedError) {
      throw relatedError;
    }

    if (relatedData && relatedData.length > 0) {
      return res.status(409).json({
        success: false,
        error: '无法删除学校，存在关联的教师数据'
      });
    }

    // 删除学校
    const { error } = await db.supabase
      .from('schools')
      .delete()
      .eq('id', schoolId);

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      message: `学校 ${existingSchool.name} 删除成功`
    });

  } catch (error) {
    console.error('删除学校错误:', error);
    res.status(500).json({
      success: false,
      error: '删除学校失败: ' + error.message
    });
  }
};
