-- 🔒 第二步：启用RLS
-- 在第一步完成后执行这个脚本

-- 为核心表启用RLS
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE medals ENABLE ROW LEVEL SECURITY;
ALTER TABLE typing_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE schools ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_class_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE articles ENABLE ROW LEVEL SECURITY;

-- 为其他表启用RLS（如果存在）
DO $$ 
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'logs') THEN
        ALTER TABLE logs ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'school_grade_configs') THEN
        ALTER TABLE school_grade_configs ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'student_import_logs') THEN
        ALTER TABLE student_import_logs ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'student_sessions') THEN
        ALTER TABLE student_sessions ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'student_signins') THEN
        ALTER TABLE student_signins ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'teacher_classes') THEN
        ALTER TABLE teacher_classes ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'teacher_school_assignments') THEN
        ALTER TABLE teacher_school_assignments ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'typing_best') THEN
        ALTER TABLE typing_best ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

SELECT '✅ RLS已为所有表启用' as 状态;
