<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生批量导入模板</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .info-box h3 {
            margin-top: 0;
            color: #1976d2;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        .required {
            color: #dc3545;
            font-weight: bold;
        }
        .optional {
            color: #6c757d;
        }
        .example-row {
            background-color: #f8f9fa;
        }
        .download-btn {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .download-btn:hover {
            background: #218838;
        }
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .note h4 {
            margin-top: 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>学生批量导入模板</h1>
        
        <div class="info-box">
            <h3>📋 导入说明</h3>
            <ul>
                <li>支持Excel文件格式（.xlsx, .xls）</li>
                <li>文件大小限制：10MB</li>
                <li>必填列：姓名、学校、年级、班级</li>
                <li>可选列：学号、组号、性别、座位号</li>
                <li>如果学校不存在，系统会自动创建</li>
                <li>教师会自动获得导入班级的管理权限</li>
            </ul>
        </div>

        <h3>📊 Excel表格格式</h3>
        <table>
            <thead>
                <tr>
                    <th class="required">姓名*</th>
                    <th class="required">学校*</th>
                    <th class="required">年级*</th>
                    <th class="required">班级*</th>
                    <th class="optional">学号</th>
                    <th class="optional">组号</th>
                    <th class="optional">性别</th>
                    <th class="optional">座位号</th>
                </tr>
            </thead>
            <tbody>
                <tr class="example-row">
                    <td>张三</td>
                    <td>实验小学</td>
                    <td>5</td>
                    <td>1</td>
                    <td>20240001</td>
                    <td>1</td>
                    <td>男</td>
                    <td>1</td>
                </tr>
                <tr class="example-row">
                    <td>李四</td>
                    <td>实验小学</td>
                    <td>5</td>
                    <td>1</td>
                    <td>20240002</td>
                    <td>2</td>
                    <td>女</td>
                    <td>2</td>
                </tr>
                <tr class="example-row">
                    <td>王五</td>
                    <td>希望中学</td>
                    <td>8</td>
                    <td>3</td>
                    <td></td>
                    <td>3</td>
                    <td>男</td>
                    <td>15</td>
                </tr>
            </tbody>
        </table>

        <div class="note">
            <h4>⚠️ 重要提示</h4>
            <ul>
                <li><strong>必填字段</strong>：姓名、学校、年级、班级必须填写</li>
                <li><strong>学号</strong>：如果不填写，系统会自动生成</li>
                <li><strong>年级</strong>：请填写数字，如：1, 2, 3...12</li>
                <li><strong>班级</strong>：请填写数字，如：1, 2, 3...20</li>
                <li><strong>组号</strong>：可选，范围1-10</li>
                <li><strong>性别</strong>：可选，填写"男"或"女"</li>
                <li><strong>座位号</strong>：可选，范围1-60</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="download-btn" onclick="downloadTemplate()">
                📥 下载Excel模板
            </button>
            <button class="download-btn" onclick="window.close()">
                🔙 返回系统
            </button>
        </div>

        <div class="note">
            <h4>📝 列名支持</h4>
            <p>系统支持以下列名（中英文均可）：</p>
            <ul>
                <li><strong>姓名</strong>：姓名、name、学生姓名、Name</li>
                <li><strong>学校</strong>：学校、school、学校名称、School</li>
                <li><strong>年级</strong>：年级、grade、Grade</li>
                <li><strong>班级</strong>：班级、class、Class、班</li>
                <li><strong>学号</strong>：学号、student_id、学生标识符、StudentID</li>
                <li><strong>组号</strong>：组号、group、小组、Group</li>
                <li><strong>性别</strong>：性别、gender、Gender</li>
                <li><strong>座位号</strong>：座位号、seat、Seat</li>
            </ul>
        </div>
    </div>

    <script>
        function downloadTemplate() {
            // 创建Excel数据
            const data = [
                ['姓名', '学校', '年级', '班级', '学号', '组号', '性别', '座位号'],
                ['张三', '实验小学', 5, 1, '20240001', 1, '男', 1],
                ['李四', '实验小学', 5, 1, '20240002', 2, '女', 2],
                ['王五', '希望中学', 8, 3, '', 3, '男', 15]
            ];

            // 创建CSV内容
            const csvContent = data.map(row => 
                row.map(cell => `"${cell}"`).join(',')
            ).join('\n');

            // 添加BOM以支持中文
            const BOM = '\uFEFF';
            const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
            
            // 创建下载链接
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', '学生导入模板.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            alert('模板已下载！\n\n请注意：\n1. 下载的是CSV格式，可用Excel打开\n2. 保存时请选择Excel格式(.xlsx)\n3. 确保中文显示正常');
        }
    </script>
</body>
</html>
