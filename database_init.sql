-- 班级成绩管理系统V2.0 - 完整数据库初始化脚本
-- 请在Supabase SQL Editor中执行此脚本

-- 1. 创建学校表
CREATE TABLE IF NOT EXISTS schools (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL UNIQUE,
  address TEXT,
  contact_phone VARCHAR(50),
  contact_email VARCHAR(255),
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 创建学生表
CREATE TABLE IF NOT EXISTS students (
  id SERIAL PRIMARY KEY,
  student_identifier VARCHAR(255) NOT NULL UNIQUE,
  name VARCHAR(100) NOT NULL,
  grade INTEGER NOT NULL,
  class INTEGER NOT NULL,
  school_id INTEGER REFERENCES schools(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 创建用户表（教师/管理员）
CREATE TABLE IF NOT EXISTS users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(100) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  display_name VARCHAR(100),
  email VARCHAR(255),
  role VARCHAR(50) NOT NULL DEFAULT 'teacher',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 创建奖章表
CREATE TABLE IF NOT EXISTS medals (
  id SERIAL PRIMARY KEY,
  student_identifier VARCHAR(255) NOT NULL UNIQUE,
  count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT fk_medals_student FOREIGN KEY (student_identifier) 
    REFERENCES students(student_identifier) ON DELETE CASCADE
);

-- 5. 创建打字记录表
CREATE TABLE IF NOT EXISTS typing_records (
  id SERIAL PRIMARY KEY,
  student_identifier VARCHAR(255) NOT NULL,
  speed INTEGER NOT NULL,
  accuracy DECIMAL(5,2) NOT NULL DEFAULT 100.00,
  language VARCHAR(10) NOT NULL DEFAULT 'zh',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT fk_typing_records_student FOREIGN KEY (student_identifier) 
    REFERENCES students(student_identifier) ON DELETE CASCADE
);

-- 6. 创建最佳打字成绩表
CREATE TABLE IF NOT EXISTS typing_best (
  student_identifier VARCHAR(255) NOT NULL,
  language VARCHAR(10) NOT NULL DEFAULT 'zh',
  best_speed INTEGER NOT NULL DEFAULT 0,
  best_accuracy DECIMAL(5,2) NOT NULL DEFAULT 0.00,
  achieved_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (student_identifier, language),
  CONSTRAINT fk_typing_best_student FOREIGN KEY (student_identifier) 
    REFERENCES students(student_identifier) ON DELETE CASCADE
);

-- 7. 创建学生会话表
CREATE TABLE IF NOT EXISTS student_sessions (
  id SERIAL PRIMARY KEY,
  student_identifier VARCHAR(255) NOT NULL,
  session_token VARCHAR(255) NOT NULL UNIQUE,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ended_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT TRUE,
  CONSTRAINT fk_student_sessions_student FOREIGN KEY (student_identifier) 
    REFERENCES students(student_identifier) ON DELETE CASCADE
);

-- 8. 创建系统日志表
CREATE TABLE IF NOT EXISTS logs (
  id SERIAL PRIMARY KEY,
  level VARCHAR(20) NOT NULL DEFAULT 'info',
  message TEXT NOT NULL,
  meta JSONB,
  user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
  student_identifier VARCHAR(255) REFERENCES students(student_identifier) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. 创建文章表
CREATE TABLE IF NOT EXISTS articles (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  language VARCHAR(10) NOT NULL DEFAULT 'zh',
  difficulty_level INTEGER NOT NULL DEFAULT 1,
  target_grade INTEGER,
  word_count INTEGER,
  created_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_students_name ON students(name);
CREATE INDEX IF NOT EXISTS idx_students_grade_class ON students(grade, class);
CREATE INDEX IF NOT EXISTS idx_students_school ON students(school_id);
CREATE INDEX IF NOT EXISTS idx_typing_student ON typing_records(student_identifier);
CREATE INDEX IF NOT EXISTS idx_typing_created ON typing_records(created_at);
CREATE INDEX IF NOT EXISTS idx_sessions_student ON student_sessions(student_identifier);
CREATE INDEX IF NOT EXISTS idx_sessions_active ON student_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_logs_created ON logs(created_at);
CREATE INDEX IF NOT EXISTS idx_articles_language ON articles(language);
CREATE INDEX IF NOT EXISTS idx_articles_grade ON articles(target_grade);

-- 插入默认数据
-- 默认管理员用户
INSERT INTO users (username, password, display_name, role) 
VALUES 
  ('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 'admin'),
  ('teacher', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '教师用户', 'teacher')
ON CONFLICT (username) DO NOTHING;

-- 默认学校
INSERT INTO schools (name, address, contact_phone) 
VALUES 
  ('示例小学', '北京市朝阳区示例路123号', '010-12345678'),
  ('测试中学', '上海市浦东新区测试大道456号', '021-87654321')
ON CONFLICT (name) DO NOTHING;

-- 示例学生数据
INSERT INTO students (student_identifier, name, grade, class, school_id) 
VALUES 
  ('20241001', '张三', 3, 1, 1),
  ('20241002', '李四', 3, 1, 1),
  ('20241003', '王五', 3, 2, 1),
  ('20241004', '赵六', 4, 1, 1),
  ('20241005', '钱七', 4, 1, 1),
  ('20241006', '孙八', 4, 2, 1),
  ('20241007', '周九', 5, 1, 2),
  ('20241008', '吴十', 5, 1, 2),
  ('20241009', '郑十一', 5, 2, 2),
  ('20241010', '王十二', 6, 1, 2)
ON CONFLICT (student_identifier) DO NOTHING;

-- 示例奖章数据
INSERT INTO medals (student_identifier, count) 
VALUES 
  ('20241001', 5),
  ('20241002', 3),
  ('20241003', 8),
  ('20241004', 2),
  ('20241005', 6),
  ('20241006', 4),
  ('20241007', 7),
  ('20241008', 1),
  ('20241009', 9),
  ('20241010', 3)
ON CONFLICT (student_identifier) DO NOTHING;

-- 示例打字记录
INSERT INTO typing_records (student_identifier, speed, accuracy, language) 
VALUES 
  ('20241001', 45, 95.5, 'zh'),
  ('20241002', 38, 92.0, 'zh'),
  ('20241003', 52, 97.2, 'zh'),
  ('20241004', 41, 89.8, 'zh'),
  ('20241005', 48, 94.5, 'zh'),
  ('20241006', 35, 88.9, 'zh'),
  ('20241007', 55, 96.8, 'zh'),
  ('20241008', 29, 85.6, 'zh'),
  ('20241009', 61, 98.1, 'zh'),
  ('20241010', 43, 91.7, 'zh');

-- 更新最佳成绩
INSERT INTO typing_best (student_identifier, language, best_speed, best_accuracy) 
VALUES 
  ('20241001', 'zh', 45, 95.5),
  ('20241002', 'zh', 38, 92.0),
  ('20241003', 'zh', 52, 97.2),
  ('20241004', 'zh', 41, 89.8),
  ('20241005', 'zh', 48, 94.5),
  ('20241006', 'zh', 35, 88.9),
  ('20241007', 'zh', 55, 96.8),
  ('20241008', 'zh', 29, 85.6),
  ('20241009', 'zh', 61, 98.1),
  ('20241010', 'zh', 43, 91.7)
ON CONFLICT (student_identifier, language) DO NOTHING;

-- 示例文章
INSERT INTO articles (title, content, language, difficulty_level, target_grade, word_count) 
VALUES 
  ('春天来了', '春天来了，万物复苏。小草从地里钻出来，嫩嫩的，绿绿的。', 'zh', 1, 3, 25),
  ('我的家乡', '我的家乡是一个美丽的小城市，这里有清澈的河水和茂密的森林。', 'zh', 2, 4, 28),
  ('学习的乐趣', '学习是一件快乐的事情，通过学习我们可以获得知识，开阔视野。', 'zh', 3, 5, 30);

COMMENT ON TABLE schools IS '学校信息表';
COMMENT ON TABLE students IS '学生信息表';
COMMENT ON TABLE users IS '用户表（教师和管理员）';
COMMENT ON TABLE medals IS '学生奖章表';
COMMENT ON TABLE typing_records IS '打字记录表';
COMMENT ON TABLE typing_best IS '最佳打字成绩表';
COMMENT ON TABLE student_sessions IS '学生登录会话表';
COMMENT ON TABLE logs IS '系统日志表';
COMMENT ON TABLE articles IS '打字练习文章表';