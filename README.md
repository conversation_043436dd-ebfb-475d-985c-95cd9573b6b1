# 班级成绩管理系统 V2.0

## 项目介绍
班级成绩管理系统是一个用于管理小学生打字速度和奖牌记录的Web应用。系统包含教师端和学生端两个界面，教师可以管理学生信息、记录打字速度和奖牌，学生可以查看个人信息和进行打字练习。

系统使用Node.js + Express + MySQL架构开发，前端采用Bootstrap框架，支持响应式设计，兼容电脑和移动设备。

## 主要功能
- **教师端**
  - 学生管理：添加、编辑、删除学生信息
  - 成绩管理：记录学生打字速度和奖牌数量
  - 批量操作：支持批量更新打字速度和奖牌
  - 数据导入导出：支持Excel格式的学生数据导入导出
  - 文章管理：支持添加、编辑、删除打字练习文章
  
- **学生端**
  - 学生登录：学生通过年级、班级、姓名登录系统
  - 打字练习：提供打字练习功能，记录速度和准确率
  - 历史记录：查看历史打字记录
  - 奖牌展示：显示获得的奖牌数量
  - 文章选择：可选择不同难度和语言的文章进行练习

## 系统架构
- **前端**：HTML5, CSS3, JavaScript (ES6+), jQuery 3.6.0, Bootstrap 5.3.0
- **后端**：Node.js 18.0+, Express 4.18.0
- **数据库**：Supabase PostgreSQL
- **部署平台**：Vercel (Serverless)
- **API**：RESTful API
- **开发工具**：VS Code, Cursor IDE

## 数据库结构
系统使用Supabase PostgreSQL数据库，主要包含以下表：
- `students`：存储学生基本信息
- `medals`：记录学生奖牌数量
- `typing_records`：存储打字练习记录
- `typing_best`：存储学生最佳打字成绩
- `student_sessions`：记录学生登录会话
- `users`：存储教师账号信息
- `logs`：系统操作日志
- `articles`：存储打字练习文章

## 安装与使用

### 环境要求
- Node.js 18.0+
- Supabase账户
- 现代浏览器（Chrome 90+, Firefox 90+, Edge 90+）

### 本地开发安装步骤

1. 克隆项目
   ```bash
   git clone <repository-url>
   cd 班级成绩管理系统V2.0
   ```

2. 安装依赖
   ```bash
   npm install
   ```

3. 配置环境变量
   复制`.env.example`为`.env`，填写以下配置：
   ```env
   PORT=3000
   SUPABASE_URL=https://your-project-ref.supabase.co
   SUPABASE_ANON_KEY=your-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   JWT_SECRET=your_secret_key
   ```

4. 设置Supabase数据库
   - 在Supabase创建新项目
   - 运行`supabase/migrations/`中的SQL文件创建表结构
   - 或使用Supabase CLI：
   ```bash
   supabase db push
   ```

5. 启动应用
   ```bash
   npm start
   # 或开发模式
   npm run dev
   ```

6. 访问系统
   - 教师端：http://localhost:3000/index.html
   - 学生端：http://localhost:3000/student.html

### Vercel部署步骤

1. 将代码推送到GitHub仓库

2. 在Vercel中导入项目

3. 设置环境变量：
   - `SUPABASE_URL`
   - `SUPABASE_SERVICE_ROLE_KEY`
   - `JWT_SECRET`

4. 部署项目

### 默认登录信息
- 教师登录
  - 用户名：teacher
  - 密码：teacher123
- 管理员登录
  - 用户名：admin
  - 密码：admin123

## API接口

### 认证接口
- `POST /api/login` - 用户登录
- `POST /api/logout` - 用户登出

### 学生接口
- `GET /api/students` - 获取学生列表
- `POST /api/students` - 创建学生
- `GET /api/students/:id` - 获取学生详情
- `PUT /api/students/:id` - 更新学生信息
- `DELETE /api/students/:id` - 删除学生
- `POST /api/students/batch` - 批量导入学生

### 打字记录接口
- `GET /api/typing-records` - 获取打字记录
- `POST /api/typing-records` - 添加打字记录
- `POST /api/batch-update-typing` - 批量更新打字速度
- `GET /api/typing-records/student/:studentId` - 获取指定学生的打字记录

### 奖牌接口
- `GET /api/medals` - 获取奖牌信息
- `POST /api/medals` - 更新奖牌
- `POST /api/batch-update-medals` - 批量更新奖牌
- `GET /api/medals/student/:studentId` - 获取指定学生的奖牌信息

### 会话接口
- `POST /api/sessions/start` - 开始学生会话
- `POST /api/sessions/end` - 结束学生会话
- `GET /api/sessions/active` - 获取活跃会话

### 日志接口
- `GET /api/logs` - 获取日志列表
- `POST /api/logs` - 添加日志
- `DELETE /api/logs` - 删除日志
- `GET /api/logs/export` - 导出日志

### 文章接口
- `GET /api/articles` - 获取所有文章
- `GET /api/articles/random` - 获取随机文章
- `GET /api/articles/filter` - 根据条件筛选文章
- `GET /api/articles/:id` - 获取指定文章
- `POST /api/articles` - 添加文章
- `PUT /api/articles/:id` - 更新文章
- `DELETE /api/articles/:id` - 删除文章
- `GET /api/articles/grade/:grade` - 获取指定年级的文章

## 项目结构
```
班级成绩管理系统V2.0/
├── config/             # 配置文件
│   └── db.js          # 数据库配置
├── controllers/        # 控制器
│   ├── articleController.js    # 文章控制器
│   ├── authController.js       # 认证控制器
│   ├── logController.js        # 日志控制器
│   ├── medalController.js      # 奖牌控制器
│   ├── sessionController.js    # 会话控制器
│   ├── studentController.js    # 学生控制器
│   └── typingController.js     # 打字记录控制器
├── middlewares/        # 中间件
│   └── errorHandler.js # 错误处理中间件
├── public/             # 静态资源
│   ├── css/            # 样式文件
│   ├── js/             # 前端脚本
│   ├── index.html      # 教师端主页
│   └── student.html    # 学生端主页
├── routes/             # 路由定义
│   └── api.js         # API路由
├── utils/              # 工具函数
│   ├── create_tables.sql # 数据库表结构
│   └── db_init.js      # 数据库初始化工具
├── logs/               # 日志文件目录
├── .env                # 环境变量
├── package.json        # 项目依赖
├── server.js           # 应用入口
├── start_app.bat       # Windows启动脚本
├── syxxstudent.sql     # 数据库结构和示例数据
├── 批量添加模板.xlsx   # 批量导入学生模板
├── 操作指南.md         # 使用说明文档
└── README.md           # 项目文档
```

## 开发说明
- 代码规范遵循ESLint配置
- 使用Git进行版本控制
- 项目采用模块化结构，便于维护和扩展
- 使用JSDoc进行代码注释
- 遵循RESTful API设计规范

## 注意事项
- 本系统需要通过HTTP服务器访问，请勿直接打开HTML文件
- 确保MySQL服务正常运行
- 首次使用前，请执行数据库初始化
- 定期备份数据库
- 注意保护敏感信息，如数据库密码和JWT密钥

## 从v1.5升级说明
本系统V2.0版本是在v1.5版本基础上优化而来，主要变更：
- 优化了文件结构，移除了临时文件和备份文件
- 改进了代码组织和目录结构，使项目更加整洁
- 保留了所有核心功能，确保系统稳定运行
- 优化了文档说明，使系统更易于理解和维护

## 常见问题
1. **连接数据库失败**
   - 检查MySQL服务是否启动
   - 确认数据库用户名和密码正确
   - 确认数据库名称是否存在
   - 检查防火墙设置

2. **API请求错误**
   - 检查服务器是否正常运行
   - 检查网络连接
   - 查看浏览器控制台或服务器日志
   - 确认API路径和参数正确

3. **无法启动应用**
   - 确保Node.js和npm正确安装
   - 检查是否安装了所有依赖
   - 检查端口3000是否被占用
   - 检查环境变量配置

4. **数据导入失败**
   - 检查Excel文件格式是否正确
   - 确认数据字段与系统要求匹配
   - 检查文件编码是否为UTF-8

## 许可证
本项目采用MIT许可证，详见LICENSE文件。

## 更新日志

### 2025-07-02（V2.0）
1. 系统优化：
   - 优化文件结构，删除不必要的临时文件
   - 整合数据库相关文件，精简项目结构
   - 改进文档说明，便于用户理解和维护

### 2025-04-20
1. 系统优化：
   - 改进数据库连接池配置
   - 优化API响应时间
   - 添加请求频率限制
   - 完善错误处理机制

### 2025-04-15
1. 添加文章管理功能：
   - 创建数据库articles表，支持中英文文章存储
   - 增加文章相关API接口
   - 前端从API动态加载文章，替代硬编码的文章数组
   - 学生端文章选择功能改进，支持按年级和语言筛选文章 