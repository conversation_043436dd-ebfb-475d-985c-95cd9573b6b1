<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-item {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #ccc;
        }
        .test-success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .test-error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .test-warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .test-info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>页面加载测试</h1>
        <p>这个页面用于测试修复后的功能是否正常工作。</p>
        
        <div>
            <button onclick="testAPI()">测试API连接</button>
            <button onclick="testTypingManager()">测试TypingManager</button>
            <button onclick="testResetPassword()">测试重置密码API</button>
            <button onclick="runAllTests()">运行所有测试</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const results = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-item test-${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong> - ${message}`;
            results.appendChild(div);
        }
        
        async function testAPI() {
            addResult('开始测试API连接...', 'info');
            
            try {
                const response = await fetch('/api/test');
                if (response.ok) {
                    const data = await response.json();
                    addResult(`API连接成功: ${data.message}`, 'success');
                } else {
                    addResult(`API连接失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`API连接错误: ${error.message}`, 'error');
            }
        }
        
        function testTypingManager() {
            addResult('开始测试TypingManager...', 'info');
            
            try {
                if (typeof window.TypingManager !== 'undefined') {
                    addResult('TypingManager已正确定义', 'success');
                } else {
                    addResult('TypingManager未定义，需要加载typing.js', 'warning');
                    
                    // 动态加载typing.js
                    const script = document.createElement('script');
                    script.src = '/js/typing.js';
                    script.onload = () => {
                        if (typeof window.TypingManager !== 'undefined') {
                            addResult('TypingManager加载成功', 'success');
                        } else {
                            addResult('TypingManager加载失败', 'error');
                        }
                    };
                    script.onerror = () => {
                        addResult('无法加载typing.js文件', 'error');
                    };
                    document.head.appendChild(script);
                }
            } catch (error) {
                addResult(`TypingManager测试错误: ${error.message}`, 'error');
            }
        }
        
        async function testResetPassword() {
            addResult('开始测试重置密码API...', 'info');
            
            try {
                const response = await fetch('/api/reset-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'test_nonexistent_user'
                    })
                });
                
                const data = await response.json();
                
                if (response.status === 404 && data.message === '用户不存在') {
                    addResult('重置密码API正常工作（正确处理不存在的用户）', 'success');
                } else if (response.ok) {
                    addResult('重置密码API正常工作', 'success');
                } else {
                    addResult(`重置密码API响应异常: ${data.message}`, 'warning');
                }
            } catch (error) {
                addResult(`重置密码API测试错误: ${error.message}`, 'error');
            }
        }
        
        async function runAllTests() {
            results.innerHTML = '';
            addResult('开始运行所有测试...', 'info');
            
            await testAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testTypingManager();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testResetPassword();
            
            addResult('所有测试完成', 'info');
        }
        
        // 页面加载时自动运行测试
        window.addEventListener('load', () => {
            addResult('页面加载完成，开始自动测试...', 'info');
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
